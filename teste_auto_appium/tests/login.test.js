const { expect } = require('chai');
const AppiumHelper = require('../utils/appium-helper');
require('dotenv').config();

/**
 * Testes de Login - Versão Consolidada
 * 
 * Este arquivo consolida todos os testes de login em um só lugar,
 * incluindo login básico e troca de academia.
 */

describe('Testes de Login', function() {
  let appiumHelper;
  let driver;
  let appiumAvailable = false;

  before(async function() {
    this.timeout(60000);
    console.log('🚀 Iniciando testes de login...');

    try {
      appiumHelper = new AppiumHelper();
      driver = await appiumHelper.startDriver();
      appiumAvailable = true;
      console.log('✅ Driver Appium iniciado com sucesso');
    } catch (error) {
      console.log('⚠️ Servidor Appium não disponível - pulando testes de login');
      console.log('💡 Para executar testes de login:');
      console.log('   1. Execute: npm run start-appium');
      console.log('   2. Configure um dispositivo/emulador');
      console.log('   3. Execute: npm run test:login');
      this.skip();
    }
  });

  after(async function() {
    this.timeout(30000);
    if (appiumHelper && appiumAvailable) {
      console.log('🛑 Encerrando driver Appium...');
      await appiumHelper.stopDriver();
      console.log('✅ Driver encerrado com sucesso');
    }
  });

  describe('Login Básico do Aluno', function() {
    it('Deve realizar login como aluno com sucesso', async function() {
      this.timeout(120000);

      try {
        console.log('📱 Iniciando fluxo de login do aluno...');
        
        // Fluxo de login baseado no arquivo original Maestro
        await appiumHelper.tapOn('Continue');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Look for your gym');
        await appiumHelper.pause(1000);
        
        await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Revisão');
        await appiumHelper.pause(2000);
        
        await appiumHelper.tapOn('Login with user');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Enter your email or username');
        await appiumHelper.pause(500);
        
        await appiumHelper.inputText(process.env.TEST_EMAIL || '<EMAIL>');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Continue');
        await appiumHelper.pause(2000);
        
        await appiumHelper.tapOn('Enter password');
        await appiumHelper.pause(500);
        
        await appiumHelper.tapOn('Password');
        await appiumHelper.pause(500);
        
        await appiumHelper.inputText(process.env.TEST_PASSWORD || '123');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Confirm');
        await appiumHelper.waitForAnimationToEnd(5000);
        
        await appiumHelper.tapOn('Accept');
        await appiumHelper.pause(2000);

        console.log('🎉 Login realizado com sucesso!');
        
      } catch (error) {
        console.error('❌ Erro durante o teste de login:', error.message);
        await appiumHelper.captureScreenshot('login-error');
        throw error;
      }
    });

    it('Deve verificar elementos da tela de login', async function() {
      this.timeout(60000);

      try {
        console.log('🔍 Verificando elementos da tela de login...');
        
        const continueExists = await appiumHelper.elementExists('Continue');
        const gymSearchExists = await appiumHelper.elementExists('Look for your gym');
        
        console.log(`Continue button exists: ${continueExists}`);
        console.log(`Gym search exists: ${gymSearchExists}`);
        
        // Pelo menos um dos elementos deve existir
        expect(continueExists || gymSearchExists).to.be.true;
        
        console.log('✅ Elementos da tela verificados com sucesso');
        
      } catch (error) {
        console.error('❌ Erro na verificação de elementos:', error.message);
        throw error;
      }
    });
  });

  describe('Troca de Academia', function() {
    it('Deve trocar de academia com sucesso', async function() {
      this.timeout(90000);

      try {
        console.log('🔄 Iniciando fluxo de troca de academia...');
        
        await appiumHelper.tapOn({ id: 'bnt_trocar_academia' });
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Swap gym');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Select your gym');
        await appiumHelper.pause(2000);

        console.log('✅ Troca de academia realizada com sucesso!');
        
      } catch (error) {
        console.error('❌ Erro durante troca de academia:', error.message);
        await appiumHelper.captureScreenshot('troca-academia-error');
        throw error;
      }
    });

    it('Deve selecionar uma academia específica', async function() {
      this.timeout(60000);

      try {
        console.log('🏢 Selecionando academia específica...');
        
        await appiumHelper.tapOn({ id: 'inserir_nome_academia' });
        await appiumHelper.pause(500);
        
        await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn({ point: '50%,32%' });
        await appiumHelper.pause(1000);
        
        await appiumHelper.tapOn('Your fitness journey begins now!');
        await appiumHelper.pause(2000);

        console.log('✅ Academia selecionada com sucesso!');
        
      } catch (error) {
        console.error('❌ Erro na seleção de academia:', error.message);
        await appiumHelper.captureScreenshot('selecao-academia-error');
        throw error;
      }
    });
  });
});

/**
 * Função auxiliar para executar apenas o login
 * Pode ser reutilizada em outros testes
 */
async function executeLogin(appiumHelper) {
  console.log('🔄 Executando login reutilizável...');
  
  await appiumHelper.tapOn('Continue');
  await appiumHelper.tapOn('Look for your gym');
  await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
  await appiumHelper.tapOn('Revisão');
  await appiumHelper.tapOn('Login with user');
  await appiumHelper.tapOn('Enter your email or username');
  await appiumHelper.inputText(process.env.TEST_EMAIL || '<EMAIL>');
  await appiumHelper.tapOn('Continue');
  await appiumHelper.tapOn('Enter password');
  await appiumHelper.tapOn('Password');
  await appiumHelper.inputText(process.env.TEST_PASSWORD || '123');
  await appiumHelper.tapOn('Confirm');
  await appiumHelper.waitForAnimationToEnd(5000);
  await appiumHelper.tapOn('Accept');
  
  console.log('✅ Login reutilizável executado com sucesso');
}

module.exports = { executeLogin };
