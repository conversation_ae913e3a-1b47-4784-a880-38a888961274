const { expect } = require('chai');
const AppiumHelper = require('../utils/appium-helper');
require('dotenv').config();

/**
 * Testes de Integração - Requer Servidor <PERSON>
 * 
 * Estes testes só devem ser executados quando o servidor Appium
 * estiver rodando e um dispositivo/emulador estiver disponível.
 * 
 * Para executar:
 * 1. npm run start-appium (em outro terminal)
 * 2. npx mocha tests/integration.test.js --timeout 120000
 */

describe('Testes de Integração (Requer Appium)', function() {
  let appiumHelper;
  let driver;
  let appiumAvailable = false;

  before(async function() {
    this.timeout(30000);
    console.log('🔌 Verificando disponibilidade do Appium...');
    
    try {
      appiumHelper = new AppiumHelper();
      driver = await appiumHelper.startDriver();
      appiumAvailable = true;
      console.log('✅ Servidor Appium disponível');
    } catch (error) {
      console.log('⚠️ Servidor Appium não disponível - pulando testes de integração');
      console.log('💡 Para executar estes testes:');
      console.log('   1. Execute: npm run start-appium');
      console.log('   2. Configure um dispositivo/emulador');
      console.log('   3. Execute: npx mocha tests/integration.test.js --timeout 120000');
      this.skip();
    }
  });

  after(async function() {
    this.timeout(15000);
    if (appiumHelper && appiumAvailable) {
      await appiumHelper.stopDriver();
    }
  });

  describe('Funcionalidades com Driver Real', function() {
    it('Deve obter informações do dispositivo', async function() {
      this.timeout(10000);
      
      console.log('📱 Obtendo informações do dispositivo...');
      
      const deviceInfo = await appiumHelper.getDeviceInfo();
      expect(deviceInfo).to.be.an('object');
      expect(deviceInfo.platform).to.be.a('string');
      expect(deviceInfo.width).to.be.a('number');
      expect(deviceInfo.height).to.be.a('number');
      
      console.log(`✅ Dispositivo: ${deviceInfo.platform} ${deviceInfo.width}x${deviceInfo.height}`);
    });

    it('Deve capturar screenshot', async function() {
      this.timeout(10000);
      
      console.log('📸 Capturando screenshot...');
      
      const screenshotPath = await appiumHelper.captureScreenshot('integration-test');
      expect(screenshotPath).to.be.a('string');
      expect(screenshotPath).to.include('.png');
      
      console.log(`✅ Screenshot salvo: ${screenshotPath}`);
    });

    it('Deve executar pause', async function() {
      console.log('⏸️ Testando pause...');
      
      const startTime = Date.now();
      await appiumHelper.pause(1000);
      const endTime = Date.now();
      
      const duration = endTime - startTime;
      expect(duration).to.be.at.least(900);
      expect(duration).to.be.at.most(1200);
      
      console.log(`✅ Pause executado: ${duration}ms`);
    });

    it('Deve verificar existência de elementos', async function() {
      this.timeout(10000);
      
      console.log('🔍 Verificando elementos na tela...');
      
      // Testa com elemento que provavelmente não existe
      const exists = await appiumHelper.elementExists('ElementoQueNaoExiste123');
      expect(exists).to.be.a('boolean');
      expect(exists).to.be.false;
      
      console.log('✅ Verificação de elementos funcionando');
    });
  });

  describe('Teste de Retry', function() {
    it('Deve executar retry em operações que falham', async function() {
      this.timeout(20000);
      
      console.log('🔄 Testando sistema de retry...');
      
      try {
        // Tenta clicar em elemento que não existe - deve falhar após 3 tentativas
        await appiumHelper.tapOn('ElementoQueDefinitivamenteNaoExiste');
        
        // Se chegou aqui, algo deu errado
        expect.fail('Deveria ter falhado ao tentar clicar em elemento inexistente');
        
      } catch (error) {
        expect(error.message).to.include('falhou após');
        expect(error.message).to.include('tentativas');
        console.log('✅ Sistema de retry funcionando corretamente');
      }
    });
  });

  describe('Relatório de Integração', function() {
    it('Deve gerar relatório completo do dispositivo', async function() {
      this.timeout(10000);
      
      const deviceInfo = await appiumHelper.getDeviceInfo();
      
      console.log('\n📊 RELATÓRIO DE INTEGRAÇÃO');
      console.log('==========================');
      console.log(`✅ Plataforma: ${deviceInfo.platform}`);
      console.log(`✅ Resolução: ${deviceInfo.width}x${deviceInfo.height}`);
      console.log(`✅ Orientação: ${deviceInfo.orientation}`);
      console.log(`✅ Driver: Conectado e funcional`);
      console.log(`✅ Screenshots: Funcionando`);
      console.log(`✅ Retry: Configurado (${appiumHelper.retryAttempts} tentativas)`);
      console.log('==========================\n');
      
      expect(true).to.be.true; // Sempre passa
    });
  });
});
