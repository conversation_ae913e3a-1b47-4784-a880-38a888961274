const { expect } = require('chai');
const AppiumHelper = require('../utils/appium-helper');
const config = require('../appium.config');
require('dotenv').config();

/**
 * Testes de Saúde do Framework
 * 
 * Este arquivo contém testes para verificar se o framework
 * está funcionando corretamente antes de executar testes reais.
 */

describe('Health Check - Framework Appium', function() {
  let appiumHelper;

  describe('Configuração e Conectividade', function() {
    it('Deve carregar configurações corretamente', function() {
      console.log('🔧 Verificando configurações...');
      
      expect(config).to.be.an('object');
      expect(config.server).to.be.an('object');
      expect(config.server.host).to.be.a('string');
      expect(config.server.port).to.be.a('number');
      expect(config.ios).to.be.an('object');
      expect(config.android).to.be.an('object');
      
      console.log('✅ Configurações carregadas corretamente');
    });

    it('Deve carregar variáveis de ambiente', function() {
      console.log('🌍 Verificando variáveis de ambiente...');
      
      // Verificar se as variáveis essenciais estão definidas
      const platform = process.env.PLATFORM || 'ios';
      expect(['ios', 'android']).to.include(platform);
      
      console.log(`✅ Plataforma configurada: ${platform}`);
      console.log(`✅ Email de teste: ${process.env.TEST_EMAIL || 'padrão'}`);
      console.log(`✅ Academia de teste: ${process.env.TEST_GYM_NAME || 'padrão'}`);
    });
  });

  describe('Inicialização do Driver', function() {
    it('Deve inicializar AppiumHelper sem erros', function() {
      console.log('🚀 Inicializando AppiumHelper...');
      
      appiumHelper = new AppiumHelper();
      expect(appiumHelper).to.be.an('object');
      expect(appiumHelper.platform).to.be.a('string');
      
      console.log(`✅ AppiumHelper inicializado para plataforma: ${appiumHelper.platform}`);
    });

    it('Deve verificar disponibilidade do servidor Appium', async function() {
      this.timeout(10000);

      console.log('🔌 Verificando disponibilidade do servidor Appium...');

      try {
        const driver = await appiumHelper.startDriver();
        expect(driver).to.be.an('object');

        console.log('✅ Conexão com Appium estabelecida com sucesso');

        // Testar funcionalidades básicas do driver
        const windowSize = await driver.getWindowSize();
        expect(windowSize).to.be.an('object');
        expect(windowSize.width).to.be.a('number');
        expect(windowSize.height).to.be.a('number');

        console.log(`✅ Tamanho da tela: ${windowSize.width}x${windowSize.height}`);

      } catch (error) {
        console.log('⚠️ Servidor Appium não está rodando (isso é normal)');
        console.log('💡 Para executar testes reais, inicie o servidor: npm run start-appium');

        // Não falha o teste se o servidor não estiver rodando
        expect(error.message).to.include('Unable to connect');
      }
    });

    it('Deve encerrar driver corretamente', async function() {
      this.timeout(15000);
      
      console.log('🛑 Testando encerramento do driver...');
      
      try {
        await appiumHelper.stopDriver();
        expect(appiumHelper.driver).to.be.null;
        
        console.log('✅ Driver encerrado corretamente');
        
      } catch (error) {
        console.error('❌ Erro ao encerrar driver:', error.message);
        throw error;
      }
    });
  });

  describe('Funcionalidades do AppiumHelper (sem servidor)', function() {
    beforeEach(function() {
      appiumHelper = new AppiumHelper();
    });

    it('Deve verificar configuração de screenshots', function() {
      console.log('📸 Testando configuração de screenshots...');

      expect(appiumHelper.screenshotDir).to.be.a('string');
      expect(appiumHelper.screenshotDir).to.include('screenshots');

      console.log(`✅ Diretório de screenshots configurado: ${appiumHelper.screenshotDir}`);
    });

    it('Deve verificar configurações de retry', function() {
      console.log('🔄 Testando configurações de retry...');

      expect(appiumHelper.retryAttempts).to.be.a('number');
      expect(appiumHelper.retryDelay).to.be.a('number');
      expect(appiumHelper.retryAttempts).to.be.at.least(1);
      expect(appiumHelper.retryDelay).to.be.at.least(0);

      console.log(`✅ Retry configurado: ${appiumHelper.retryAttempts} tentativas, ${appiumHelper.retryDelay}ms delay`);
    });

    it('Deve gerar estratégias de busca de elementos', function() {
      console.log('🔍 Testando geração de estratégias de busca...');

      const strategies = appiumHelper.getElementStrategies('Continue');
      expect(strategies).to.be.an('array');
      expect(strategies.length).to.be.at.least(1);

      console.log(`✅ ${strategies.length} estratégias geradas para busca de elementos`);
    });
  });

  describe('Relatório de Saúde', function() {
    it('Deve gerar relatório de status do sistema', function() {
      console.log('\n📊 RELATÓRIO DE SAÚDE DO SISTEMA');
      console.log('================================');
      console.log(`✅ Node.js: ${process.version}`);
      console.log(`✅ Plataforma: ${process.env.PLATFORM || 'ios'}`);
      console.log(`✅ Host Appium: ${config.server.host}:${config.server.port}`);
      console.log(`✅ Timeout implícito: ${config.timeouts.implicit}ms`);
      console.log(`✅ Email de teste: ${process.env.TEST_EMAIL ? 'Configurado' : 'Usando padrão'}`);
      console.log(`✅ Academia de teste: ${process.env.TEST_GYM_NAME ? 'Configurada' : 'Usando padrão'}`);
      console.log('================================\n');
      
      expect(true).to.be.true; // Sempre passa, é só para mostrar o relatório
    });
  });
});
