#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Configurando ambiente de testes Appium - Versão Simplificada...\n');

// Função para executar comandos com tratamento de erro
function runCommand(command, description, optional = false) {
  try {
    console.log(`📋 ${description}...`);
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} - Sucesso`);
    return result.trim();
  } catch (error) {
    if (optional) {
      console.log(`⚠️ ${description} - Opcional, continuando...`);
      return null;
    } else {
      console.error(`❌ ${description} - Erro: ${error.message}`);
      throw error;
    }
  }
}

// Verificações de pré-requisitos
console.log('🔍 Verificando pré-requisitos...\n');

const nodeVersion = runCommand('node --version', 'Verificando Node.js');
console.log(`   Versão: ${nodeVersion}`);

const npmVersion = runCommand('npm --version', 'Verificando npm');
console.log(`   Versão: ${npmVersion}`);

// Verificar versão mínima do Node.js
const nodeVersionNumber = parseFloat(nodeVersion.replace('v', ''));
if (nodeVersionNumber < 16) {
  console.error('❌ Node.js versão 16+ é necessário. Versão atual:', nodeVersion);
  process.exit(1);
}

// Instalar dependências
console.log('\n📦 Instalando dependências...');
runCommand('npm install', 'Instalando dependências do projeto');

// Verificar e instalar Appium
console.log('\n🔧 Configurando Appium...');

let appiumVersion = runCommand('appium --version', 'Verificando Appium', true);
if (!appiumVersion) {
  console.log('📥 Instalando Appium globalmente...');
  runCommand('npm install -g appium@latest', 'Instalando Appium');
  appiumVersion = runCommand('appium --version', 'Verificando Appium após instalação');
}
console.log(`   Versão: ${appiumVersion}`);

// Instalar drivers do Appium
console.log('\n🚗 Instalando drivers do Appium...');

const iosDriver = runCommand('appium driver install xcuitest', 'Instalando driver iOS (XCUITest)', true);
if (iosDriver) {
  console.log('   ✅ Driver iOS instalado');
} else {
  console.log('   ⚠️ Driver iOS não instalado (pode ser necessário para testes iOS)');
}

const androidDriver = runCommand('appium driver install uiautomator2', 'Instalando driver Android (UiAutomator2)', true);
if (androidDriver) {
  console.log('   ✅ Driver Android instalado');
} else {
  console.log('   ⚠️ Driver Android não instalado (pode ser necessário para testes Android)');
}

// Criar diretórios necessários
console.log('\n📁 Criando estrutura de diretórios...');
const directories = ['reports', 'screenshots', 'logs'];

directories.forEach(dir => {
  const dirPath = path.join(__dirname, '..', dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`   ✅ Criado: ${dir}/`);
  } else {
    console.log(`   ✅ Existe: ${dir}/`);
  }
});

// Executar teste de saúde
console.log('\n🩺 Executando teste de saúde do framework...');
const healthTest = runCommand('npm run test:health', 'Teste de saúde', true);
if (healthTest) {
  console.log('   ✅ Framework funcionando corretamente');
} else {
  console.log('   ⚠️ Teste de saúde falhou - verifique configurações');
}

// Diagnóstico opcional
console.log('\n🔍 Executando diagnóstico opcional...');
runCommand('npm run doctor', 'Appium Doctor', true);

// Relatório final
console.log('\n' + '='.repeat(50));
console.log('🎉 SETUP CONCLUÍDO COM SUCESSO!');
console.log('='.repeat(50));
console.log('\n📋 Próximos passos:');
console.log('1. 📱 Configure seu dispositivo/emulador');
console.log('2. ⚙️  Ajuste configurações no arquivo .env se necessário');
console.log('3. 🧪 Execute testes: npm run test:login');
console.log('\n🚀 Comandos úteis:');
console.log('   npm run test:health    # Verificar se tudo está funcionando');
console.log('   npm run test:login     # Executar testes de login');
console.log('   npm run test:android   # Executar no Android');
console.log('   npm run test:ios       # Executar no iOS');
console.log('   npm run validate       # Validação completa');
console.log('\n💡 Dica: Execute "npm run validate" para verificar se tudo está OK!');
console.log('='.repeat(50));
