# 🚀 G<PERSON><PERSON> <PERSON> Início <PERSON>

## ⚡ Setup em 3 Passos

### 1. Instalar e Configurar
```bash
cd teste_auto_appium
npm run setup
```

### 2. Validar Framework
```bash
npm run test:health
```

### 3. Executar Testes (com Appium rodando)
```bash
# Terminal 1: Iniciar Appium
npm run start-appium

# Terminal 2: Executar testes
npm run test:login
```

## 📋 Checklist de Validação

### ✅ Framework Funcionando
- [ ] `npm run test:health` - Todos os testes passam
- [ ] Configurações carregadas corretamente
- [ ] Variáveis de ambiente configuradas
- [ ] AppiumHelper inicializado

### ✅ Ambiente Pronto para Testes
- [ ] Servidor Appium iniciado (`npm run start-appium`)
- [ ] Dispositivo/emulador conectado
- [ ] Testes de integração passam (`npm run test:integration`)

## 🔧 Comandos Essenciais

```bash
# Validação completa
npm run validate

# Testes sem servidor Appium
npm run test:health

# Testes com servidor Appium
npm run test:login

# Limpeza
npm run clean

# Diagnóstico
npm run doctor
```

## 📱 Configuração de Dispositivos

### iOS (macOS)
```bash
# Listar simuladores
xcrun simctl list devices

# Iniciar simulador específico
xcrun simctl boot "iPhone 14"
```

### Android
```bash
# Listar dispositivos
adb devices

# Iniciar emulador
emulator -avd Pixel_4_API_30
```

## ⚙️ Configuração Rápida (.env)

```env
# Plataforma
PLATFORM=ios

# Dados de teste
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=sua-senha
TEST_GYM_NAME=Nome-da-Academia
```

## 🐛 Problemas Comuns

### ❌ "Unable to connect to Appium"
**Solução**: Inicie o servidor Appium
```bash
npm run start-appium
```

### ❌ "No devices found"
**Solução**: Configure dispositivo/emulador
```bash
# iOS
xcrun simctl list devices

# Android
adb devices
```

### ❌ "Driver not installed"
**Solução**: Execute setup novamente
```bash
npm run setup
```

## 📊 Status do Framework

Execute `npm run test:health` para ver:

```
📊 RELATÓRIO DE SAÚDE DO SISTEMA
================================
✅ Node.js: v22.19.0
✅ Plataforma: ios
✅ Host Appium: localhost:4723
✅ Timeout implícito: 10000ms
✅ Email de teste: Configurado
✅ Academia de teste: Configurada
================================
```

## 🎯 Próximos Passos

1. **Validar**: `npm run validate`
2. **Configurar dispositivo**: iOS Simulator ou Android Emulator
3. **Executar testes**: `npm run test:login`
4. **Desenvolver novos testes**: Usar `AppiumHelper` como base

## 📞 Suporte Rápido

- 🔧 **Problemas**: `npm run doctor`
- 📋 **Logs**: Pasta `logs/`
- 📸 **Screenshots**: Pasta `screenshots/`
- 📖 **Documentação**: `README.md`
