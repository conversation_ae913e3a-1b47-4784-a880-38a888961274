# 🚀 Framework de Testes Mobile - Appium Simplificado

Framework de testes automatizados para aplicações mobile, migrado do Maestro para Appium com foco na simplicidade e robustez.

## ⚡ Início Rápido

### 1. Setup Automático
```bash
cd teste_auto_appium
npm run setup
```

### 2. Executar Testes
```bash
npm run test:login
```

## 📋 Pré-requisitos

- **Node.js 16+**
- **Dispositivo/Emulador** configurado
- **Appium** (instalado automaticamente)

### Para iOS
- **macOS** com **Xcode**
- **Simulador iOS** ou dispositivo físico

### Para Android
- **Android Studio** com **SDK**
- **Emulador Android** ou dispositivo físico

## 🔧 Configuração

### Variáveis de Ambiente (.env)
```env
# Plataforma
PLATFORM=ios                    # ou android

# Dados de teste
TEST_EMAIL=<EMAIL>
TEST_PASSWORD=sua-senha
TEST_GYM_NAME=Nome-da-Academia

# Configurações opcionais
APPIUM_HOST=localhost
APPIUM_PORT=4723
```

## 🧪 Executando Testes

### Comandos Principais
```bash
# Verificar se tudo está funcionando
npm run validate

# Executar testes de login
npm run test:login

# Executar por plataforma
npm run test:android
npm run test:ios

# Teste de saúde do framework
npm run test:health
```

### Servidor Appium
```bash
# Iniciar servidor manualmente (opcional)
npm run start-appium

# O framework inicia automaticamente quando necessário
```

## 📁 Estrutura Simplificada

```
teste_auto_appium/
├── tests/                    # 🧪 Todos os testes
│   ├── login.test.js        #    Testes de login consolidados
│   └── health.test.js       #    Testes de validação do framework
├── utils/                   # 🛠️ Utilitários
│   └── appium-helper.js     #    Helper principal do Appium
├── scripts/                 # 📜 Scripts de automação
│   ├── setup.js            #    Setup automático
│   └── start-appium.js      #    Iniciar servidor Appium
├── screenshots/             # 📸 Screenshots de erro
├── logs/                    # 📋 Logs de execução
├── .env                     # ⚙️ Configurações
├── appium.config.js         # 🔧 Configuração do Appium
└── package.json             # 📦 Dependências
```

## 🔄 Melhorias Implementadas

### ✨ Novos Recursos
- **🔄 Retry Automático**: Tentativas automáticas em caso de falha
- **📸 Screenshots**: Captura automática em erros
- **🔍 Múltiplos Seletores**: Estratégias robustas de busca de elementos
- **⚡ Validação**: Testes de saúde do framework
- **📊 Logs Detalhados**: Acompanhamento completo da execução

### 🛠️ AppiumHelper Melhorado
```javascript
// Toque com retry automático
await appiumHelper.tapOn('Continue');

// Input com limpeza automática
await appiumHelper.inputText('texto');

// Verificação de elementos
const exists = await appiumHelper.elementExists('elemento');

// Screenshot em caso de erro
await appiumHelper.captureScreenshot('erro-login');
```

## 🛠️ Comandos Úteis

```bash
# Validação completa
npm run validate

# Limpeza
npm run clean

# Diagnóstico
npm run doctor

# Setup completo
npm run setup
```

## 🐛 Troubleshooting

### ❌ Problemas Comuns

#### Appium não encontrado
```bash
npm install -g appium@latest
```

#### Driver não instalado
```bash
appium driver install xcuitest      # iOS
appium driver install uiautomator2  # Android
```

#### Dispositivo não conectado
```bash
# iOS
xcrun simctl list devices

# Android
adb devices
```

#### Teste falha constantemente
```bash
# Execute validação
npm run test:health

# Verifique logs
cat logs/latest.log
```

### 🔍 Debug Avançado

```bash
# Appium com logs detalhados
appium server --log-level debug

# Executar teste específico
npx mocha tests/login.test.js --timeout 120000
```

## 📊 Recursos Avançados

### Screenshots Automáticos
- Capturados automaticamente em erros
- Salvos em `screenshots/` com timestamp
- Incluem informações do dispositivo

### Retry Inteligente
- 3 tentativas automáticas por operação
- Delay configurável entre tentativas
- Logs detalhados de cada tentativa

### Múltiplas Estratégias de Busca
- Busca por texto, ID, coordenadas
- Fallback automático entre estratégias
- Compatível com iOS e Android

## 🎯 Próximos Passos

1. **Execute a validação**: `npm run validate`
2. **Configure seu dispositivo/emulador**
3. **Ajuste variáveis no .env se necessário**
4. **Execute seus primeiros testes**: `npm run test:login`

## 📞 Suporte

- 🔧 **Problemas técnicos**: Execute `npm run doctor`
- 📋 **Logs**: Verifique pasta `logs/`
- 📸 **Screenshots**: Pasta `screenshots/` para erros visuais
