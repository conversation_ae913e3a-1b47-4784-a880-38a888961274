require('dotenv').config();

const config = {
  // Configurações do servidor Appium
  server: {
    host: process.env.APPIUM_HOST || 'localhost',
    port: parseInt(process.env.APPIUM_PORT) || 4723,
    path: '/wd/hub'
  },

  // Configurações para iOS
  ios: {
    platformName: 'iOS',
    platformVersion: process.env.IOS_PLATFORM_VERSION || '16.0',
    deviceName: process.env.IOS_DEVICE_NAME || 'iPhone 14',
    bundleId: process.env.IOS_BUNDLE_ID || 'br.com.pactosolucoes.treino.ios',
    automationName: 'XCUITest',
    noReset: false,
    fullReset: false,
    newCommandTimeout: 300,
    commandTimeouts: 300000,
    wdaLaunchTimeout: 300000,
    wdaConnectionTimeout: 300000
  },

  // Configurações para Android
  android: {
    platformName: 'Android',
    platformVersion: process.env.ANDROID_PLATFORM_VERSION || '12.0',
    deviceName: process.env.ANDROID_DEVICE_NAME || 'Android Emulator',
    appPackage: process.env.ANDROID_APP_PACKAGE || 'br.com.pactosolucoes.treino',
    appActivity: process.env.ANDROID_APP_ACTIVITY || '.MainActivity',
    automationName: 'UiAutomator2',
    noReset: false,
    fullReset: false,
    newCommandTimeout: 300
  },

  // Configurações de timeout
  timeouts: {
    implicit: parseInt(process.env.IMPLICIT_TIMEOUT) || 10000,
    pageLoad: parseInt(process.env.PAGE_LOAD_TIMEOUT) || 30000,
    script: parseInt(process.env.SCRIPT_TIMEOUT) || 30000
  },

  // Configurações de retry
  retry: {
    attempts: parseInt(process.env.RETRY_ATTEMPTS) || 3,
    delay: parseInt(process.env.RETRY_DELAY) || 2000
  }
};

module.exports = config;
