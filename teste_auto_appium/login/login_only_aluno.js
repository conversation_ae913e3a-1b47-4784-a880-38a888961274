const { expect } = require('chai');
const AppiumHelper = require('../utils/appium-helper');
require('dotenv').config();

/**
 * Teste de Login - Aluno
 * Migrado do arquivo Maestro: login_only_aluno.yaml
 * 
 * Fluxo original:
 * - tapOn: Continue
 * - tapOn: Look for your gym
 * - inputText: Revisao
 * - tapOn: Revisão
 * - tapOn: Login with user
 * - tapOn: Enter your email or username
 * - inputText: <EMAIL>
 * - tapOn: Continue
 * - tapOn: Enter password
 * - tapOn: Password
 * - inputText: 123
 * - tapOn: Confirm
 * - waitForAnimationToEnd: timeout: 5000
 * - tapOn: Accept
 */

describe('Login - Aluno (Standalone)', function() {
  let appiumHelper;
  let driver;

  before(async function() {
    this.timeout(60000);
    console.log('🚀 Iniciando teste de login do aluno...');
    appiumHelper = new AppiumHelper();
    driver = await appiumHelper.startDriver();
    console.log('✅ Driver Appium iniciado com sucesso');
  });

  after(async function() {
    this.timeout(30000);
    if (appiumHelper) {
      console.log('🛑 Encerrando driver Appium...');
      await appiumHelper.stopDriver();
      console.log('✅ Driver encerrado com sucesso');
    }
  });

  it('Deve realizar login como aluno com sucesso', async function() {
    this.timeout(120000);

    try {
      console.log('📱 Iniciando fluxo de login do aluno...');
      
      // Passo 1: Continuar na tela inicial
      console.log('1️⃣ Clicando em Continue...');
      await appiumHelper.tapOn('Continue');
      await driver.pause(1000);
      
      // Passo 2: Procurar academia
      console.log('2️⃣ Clicando em "Look for your gym"...');
      await appiumHelper.tapOn('Look for your gym');
      await driver.pause(1000);
      
      // Passo 3: Inserir nome da academia
      console.log('3️⃣ Inserindo nome da academia...');
      await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
      await driver.pause(1000);
      
      // Passo 4: Selecionar academia
      console.log('4️⃣ Selecionando academia "Revisão"...');
      await appiumHelper.tapOn('Revisão');
      await driver.pause(2000);
      
      // Passo 5: Escolher login com usuário
      console.log('5️⃣ Clicando em "Login with user"...');
      await appiumHelper.tapOn('Login with user');
      await driver.pause(1000);
      
      // Passo 6: Clicar no campo de email
      console.log('6️⃣ Clicando no campo de email...');
      await appiumHelper.tapOn('Enter your email or username');
      await driver.pause(500);
      
      // Passo 7: Inserir email
      console.log('7️⃣ Inserindo email...');
      await appiumHelper.inputText(process.env.TEST_EMAIL || '<EMAIL>');
      await driver.pause(1000);
      
      // Passo 8: Continuar para senha
      console.log('8️⃣ Clicando em Continue...');
      await appiumHelper.tapOn('Continue');
      await driver.pause(2000);
      
      // Passo 9: Clicar no campo de senha
      console.log('9️⃣ Clicando no campo de senha...');
      await appiumHelper.tapOn('Enter password');
      await driver.pause(500);
      
      // Passo 10: Clicar especificamente no campo Password
      console.log('🔟 Clicando no campo "Password"...');
      await appiumHelper.tapOn('Password');
      await driver.pause(500);
      
      // Passo 11: Inserir senha
      console.log('1️⃣1️⃣ Inserindo senha...');
      await appiumHelper.inputText(process.env.TEST_PASSWORD || '123');
      await driver.pause(1000);
      
      // Passo 12: Confirmar login
      console.log('1️⃣2️⃣ Clicando em Confirm...');
      await appiumHelper.tapOn('Confirm');
      
      // Passo 13: Aguardar animação de login
      console.log('1️⃣3️⃣ Aguardando animação de login...');
      await appiumHelper.waitForAnimationToEnd(5000);
      
      // Passo 14: Aceitar termos (se aparecer)
      console.log('1️⃣4️⃣ Clicando em Accept...');
      await appiumHelper.tapOn('Accept');
      await driver.pause(2000);

      // Verificação de sucesso do login
      console.log('✅ Verificando se login foi bem-sucedido...');
      
      // Aguarda um pouco mais para garantir que a tela carregou
      await driver.pause(3000);
      
      console.log('🎉 Login realizado com sucesso!');
      
    } catch (error) {
      console.error('❌ Erro durante o teste de login:', error.message);
      
      // Captura screenshot em caso de erro
      try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const screenshot = await driver.takeScreenshot();
        const fs = require('fs');
        const path = require('path');
        
        // Criar diretório de screenshots se não existir
        const screenshotDir = path.join(__dirname, '..', 'screenshots');
        if (!fs.existsSync(screenshotDir)) {
          fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        const screenshotPath = path.join(screenshotDir, `login-error-${timestamp}.png`);
        fs.writeFileSync(screenshotPath, screenshot, 'base64');
        console.log(`📸 Screenshot capturada: ${screenshotPath}`);
        
      } catch (screenshotError) {
        console.error('❌ Erro ao capturar screenshot:', screenshotError.message);
      }
      
      throw error;
    }
  });

  // Teste adicional para verificar elementos da tela de login
  it('Deve verificar elementos da tela de login', async function() {
    this.timeout(60000);

    try {
      console.log('🔍 Verificando elementos da tela de login...');
      
      // Verificar se elementos essenciais estão presentes
      const continueExists = await appiumHelper.elementExists('Continue');
      const gymSearchExists = await appiumHelper.elementExists('Look for your gym');
      
      console.log(`Continue button exists: ${continueExists}`);
      console.log(`Gym search exists: ${gymSearchExists}`);
      
      // Pelo menos um dos elementos deve existir
      expect(continueExists || gymSearchExists).to.be.true;
      
      console.log('✅ Elementos da tela verificados com sucesso');
      
    } catch (error) {
      console.error('❌ Erro na verificação de elementos:', error.message);
      throw error;
    }
  });
});

module.exports = {
  /**
   * Função auxiliar para executar apenas o login
   * Pode ser reutilizada em outros testes
   */
  async executeLogin(appiumHelper) {
    console.log('🔄 Executando login reutilizável...');
    
    await appiumHelper.tapOn('Continue');
    await appiumHelper.tapOn('Look for your gym');
    await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
    await appiumHelper.tapOn('Revisão');
    await appiumHelper.tapOn('Login with user');
    await appiumHelper.tapOn('Enter your email or username');
    await appiumHelper.inputText(process.env.TEST_EMAIL || '<EMAIL>');
    await appiumHelper.tapOn('Continue');
    await appiumHelper.tapOn('Enter password');
    await appiumHelper.tapOn('Password');
    await appiumHelper.inputText(process.env.TEST_PASSWORD || '123');
    await appiumHelper.tapOn('Confirm');
    await appiumHelper.waitForAnimationToEnd(5000);
    await appiumHelper.tapOn('Accept');
    
    console.log('✅ Login reutilizável executado com sucesso');
  }
};
