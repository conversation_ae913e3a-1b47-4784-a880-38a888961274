# Testes de Login - Formato Appium

Esta pasta contém os testes de login migrados do formato Maestro (YAML) para o formato Appium (JavaScript).

## 📁 Estrutura

```
login/
├── login_only_aluno.js           # Teste principal de login do aluno
├── troca_de_academia/
│   ├── troca_de_academia.js      # Testes de troca de academia
│   └── config.js                 # Configurações específicas
└── README.md                     # Este arquivo
```

## 🔄 Arquivos Migrados

### ✅ login_only_aluno.yaml → login_only_aluno.js
- **Formato original**: YAML (Maestro)
- **Formato atual**: JavaScript (Appium)
- **Funcionalidade**: Login completo do aluno no app

### ✅ trocar_academia.yaml + selecionar_academia.yaml → troca_de_academia.js
- **Formato original**: YAML (Maestro)
- **Formato atual**: JavaScript (Appium)
- **Funcionalidade**: Troca e seleção de academia

### ✅ config.yaml → config.js
- **Formato original**: YAML (Maestro)
- **Formato atual**: JavaScript (Appium)
- **Funcionalidade**: Configurações de execução

## 🚀 Como Executar

### Executar Teste de Login (Standalone)
```bash
cd teste_auto_appium
npm run test:login:standalone
```

### Executar Teste de Troca de Academia (Standalone)
```bash
cd teste_auto_appium
npm run test:troca:standalone
```

### Executar Todos os Testes de Login
```bash
cd teste_auto_appium
npm run test:login
```

## 📋 Pré-requisitos

1. **Servidor Appium iniciado**:
   ```bash
   npm run start-appium
   ```

2. **Variáveis de ambiente configuradas** (arquivo `.env`):
   ```env
   PLATFORM=ios
   TEST_EMAIL=<EMAIL>
   TEST_PASSWORD=123
   TEST_GYM_NAME=Revisao
   ```

3. **Dispositivo/emulador conectado**

## 🔍 Detalhes dos Testes

### login_only_aluno.js

**Fluxo testado**:
1. Clicar em "Continue"
2. Clicar em "Look for your gym"
3. Inserir nome da academia
4. Selecionar academia
5. Escolher "Login with user"
6. Inserir email
7. Continuar para senha
8. Inserir senha
9. Confirmar login
10. Aceitar termos

**Recursos**:
- Logs detalhados de cada passo
- Captura de screenshot em caso de erro
- Verificação de elementos da interface
- Função reutilizável para outros testes

### troca_de_academia.js

**Fluxos testados**:
1. **Trocar Academia**: Inicia processo de troca
2. **Selecionar Academia**: Seleciona academia específica
3. **Fluxo Completo**: Executa troca + seleção

**Recursos**:
- Testes modulares e independentes
- Configuração centralizada
- Captura de screenshots
- Função reutilizável

## 🛠️ Configurações

### config.js
Contém configurações específicas para testes de troca de academia:

```javascript
const config = {
  execution: {
    continueOnFailure: false,
    timeout: 120000,
    retryAttempts: 2
  },
  flowsOrder: [
    'trocar_academia',
    'selecionar_academia',
    'fluxo_completo'
  ],
  testData: {
    gymName: 'Revisao',
    searchCoordinates: '50%,32%'
  }
};
```

## 📊 Saída dos Testes

### Sucesso
```
Login - Aluno (Standalone)
  🚀 Iniciando teste de login do aluno...
  ✅ Driver Appium iniciado com sucesso
  📱 Iniciando fluxo de login do aluno...
  1️⃣ Clicando em Continue...
  2️⃣ Clicando em "Look for your gym"...
  ...
  🎉 Login realizado com sucesso!
  ✓ Deve realizar login como aluno com sucesso (45123ms)
```

### Erro
```
Login - Aluno (Standalone)
  ❌ Erro durante o teste de login: Elemento não encontrado: "Continue"
  📸 Screenshot capturada: screenshots/login-error-2025-09-23T14-30-15-123Z.png
  1) Deve realizar login como aluno com sucesso
```

## 🔧 Troubleshooting

### Elemento não encontrado
1. Verificar se app está na tela correta
2. Usar Appium Inspector para encontrar seletores
3. Verificar se texto está correto (case-sensitive)

### Timeout
1. Aumentar timeout no teste
2. Verificar se dispositivo está respondendo
3. Adicionar waits específicos

### Screenshot não capturada
1. Verificar permissões da pasta screenshots
2. Verificar se driver está ativo

## 📝 Diferenças do Maestro

| Aspecto | Maestro | Appium |
|---------|---------|--------|
| **Formato** | YAML | JavaScript |
| **Execução** | `maestro test file.yaml` | `npm run test:login:standalone` |
| **Logs** | Básicos | Detalhados com emojis |
| **Screenshots** | Automáticos | Em caso de erro |
| **Reutilização** | Limitada | Funções exportáveis |

## 🎯 Próximos Passos

1. **Testar execução**: Execute os testes para validar migração
2. **Ajustar seletores**: Adapte seletores conforme necessário
3. **Adicionar validações**: Implemente mais verificações de sucesso
4. **Otimizar performance**: Ajuste timeouts e waits
5. **Expandir cobertura**: Migre outros fluxos de teste

## 💡 Dicas

1. **Use logs**: Os logs detalhados ajudam no debug
2. **Screenshots**: Sempre verifique screenshots de erro
3. **Timeouts**: Seja generoso com timeouts inicialmente
4. **Modularidade**: Reutilize funções entre testes
5. **Configuração**: Use o arquivo config.js para centralizar configurações
