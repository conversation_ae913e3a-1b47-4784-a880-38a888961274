const { expect } = require('chai');
const AppiumHelper = require('../../utils/appium-helper');
require('dotenv').config();

/**
 * Testes de Troca de Academia
 * Migrado dos arquivos Maestro:
 * - trocar_academia.yaml
 * - selecionar_academia.yaml
 * 
 * Fluxos originais:
 * 
 * trocar_academia.yaml:
 * - tapOn: id: "bnt_trocar_academia"
 * - tapOn: "Swap gym"
 * - tapOn: "Select your gym"
 * 
 * selecionar_academia.yaml:
 * - tapOn: id: "inserir_nome_academia"
 * - inputText: "Revisao"
 * - tapOn: point: "50%,32%"
 * - tapOn: "Your fitness journey begins now!"
 */

describe('Troca de Academia', function() {
  let appiumHelper;
  let driver;

  before(async function() {
    this.timeout(60000);
    console.log('🚀 Iniciando testes de troca de academia...');
    appiumHelper = new AppiumHelper();
    driver = await appiumHelper.startDriver();
    console.log('✅ Driver Appium iniciado com sucesso');
  });

  after(async function() {
    this.timeout(30000);
    if (appiumHelper) {
      console.log('🛑 Encerrando driver Appium...');
      await appiumHelper.stopDriver();
      console.log('✅ Driver encerrado com sucesso');
    }
  });

  describe('Trocar Academia', function() {
    it('Deve iniciar o processo de troca de academia', async function() {
      this.timeout(60000);

      try {
        console.log('🔄 Iniciando processo de troca de academia...');
        
        // Passo 1: Clicar no botão de trocar academia
        console.log('1️⃣ Clicando no botão trocar academia...');
        await appiumHelper.tapOn({ id: 'bnt_trocar_academia' });
        await driver.pause(1000);
        
        // Passo 2: Confirmar troca
        console.log('2️⃣ Clicando em "Swap gym"...');
        await appiumHelper.tapOn('Swap gym');
        await driver.pause(1000);
        
        // Passo 3: Selecionar nova academia
        console.log('3️⃣ Clicando em "Select your gym"...');
        await appiumHelper.tapOn('Select your gym');
        await driver.pause(2000);

        console.log('✅ Processo de troca de academia iniciado com sucesso!');
        
      } catch (error) {
        console.error('❌ Erro durante a troca de academia:', error.message);
        
        // Captura screenshot em caso de erro
        try {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const screenshot = await driver.takeScreenshot();
          const fs = require('fs');
          const path = require('path');
          
          const screenshotDir = path.join(__dirname, '..', '..', 'screenshots');
          if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
          }
          
          const screenshotPath = path.join(screenshotDir, `troca-academia-error-${timestamp}.png`);
          fs.writeFileSync(screenshotPath, screenshot, 'base64');
          console.log(`📸 Screenshot capturada: ${screenshotPath}`);
          
        } catch (screenshotError) {
          console.error('❌ Erro ao capturar screenshot:', screenshotError.message);
        }
        
        throw error;
      }
    });
  });

  describe('Selecionar Academia', function() {
    it('Deve selecionar uma academia específica', async function() {
      this.timeout(60000);

      try {
        console.log('🏢 Iniciando seleção de academia...');
        
        // Passo 1: Clicar no campo de inserir nome da academia
        console.log('1️⃣ Clicando no campo de inserir nome...');
        await appiumHelper.tapOn({ id: 'inserir_nome_academia' });
        await driver.pause(1000);
        
        // Passo 2: Inserir nome da academia
        console.log('2️⃣ Inserindo nome da academia...');
        await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
        await driver.pause(1000);
        
        // Passo 3: Clicar em coordenada específica (resultado da busca)
        console.log('3️⃣ Clicando no resultado da busca...');
        await appiumHelper.tapOn({ point: '50%,32%' });
        await driver.pause(2000);
        
        // Passo 4: Confirmar seleção
        console.log('4️⃣ Confirmando seleção da academia...');
        await appiumHelper.tapOn('Your fitness journey begins now!');
        await driver.pause(2000);

        console.log('✅ Academia selecionada com sucesso!');
        
      } catch (error) {
        console.error('❌ Erro durante a seleção de academia:', error.message);
        
        // Captura screenshot em caso de erro
        try {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const screenshot = await driver.takeScreenshot();
          const fs = require('fs');
          const path = require('path');
          
          const screenshotDir = path.join(__dirname, '..', '..', 'screenshots');
          if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
          }
          
          const screenshotPath = path.join(screenshotDir, `selecionar-academia-error-${timestamp}.png`);
          fs.writeFileSync(screenshotPath, screenshot, 'base64');
          console.log(`📸 Screenshot capturada: ${screenshotPath}`);
          
        } catch (screenshotError) {
          console.error('❌ Erro ao capturar screenshot:', screenshotError.message);
        }
        
        throw error;
      }
    });
  });

  describe('Fluxo Completo - Troca e Seleção', function() {
    it('Deve executar o fluxo completo de troca e seleção de academia', async function() {
      this.timeout(120000);

      try {
        console.log('🔄 Executando fluxo completo de troca de academia...');
        
        // Fase 1: Trocar academia
        console.log('📍 Fase 1: Iniciando troca...');
        await appiumHelper.tapOn({ id: 'bnt_trocar_academia' });
        await driver.pause(1000);
        await appiumHelper.tapOn('Swap gym');
        await driver.pause(1000);
        await appiumHelper.tapOn('Select your gym');
        await driver.pause(2000);
        
        // Fase 2: Selecionar nova academia
        console.log('📍 Fase 2: Selecionando academia...');
        await appiumHelper.tapOn({ id: 'inserir_nome_academia' });
        await driver.pause(1000);
        await appiumHelper.inputText(process.env.TEST_GYM_NAME || 'Revisao');
        await driver.pause(1000);
        await appiumHelper.tapOn({ point: '50%,32%' });
        await driver.pause(2000);
        await appiumHelper.tapOn('Your fitness journey begins now!');
        await driver.pause(3000);

        console.log('🎉 Fluxo completo de troca de academia executado com sucesso!');
        
      } catch (error) {
        console.error('❌ Erro durante o fluxo completo:', error.message);
        
        // Captura screenshot em caso de erro
        try {
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const screenshot = await driver.takeScreenshot();
          const fs = require('fs');
          const path = require('path');
          
          const screenshotDir = path.join(__dirname, '..', '..', 'screenshots');
          if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
          }
          
          const screenshotPath = path.join(screenshotDir, `fluxo-completo-error-${timestamp}.png`);
          fs.writeFileSync(screenshotPath, screenshot, 'base64');
          console.log(`📸 Screenshot capturada: ${screenshotPath}`);
          
        } catch (screenshotError) {
          console.error('❌ Erro ao capturar screenshot:', screenshotError.message);
        }
        
        throw error;
      }
    });
  });

  // Teste de verificação de elementos
  describe('Verificação de Elementos', function() {
    it('Deve verificar se elementos de troca de academia estão presentes', async function() {
      this.timeout(30000);

      try {
        console.log('🔍 Verificando elementos da interface...');
        
        // Verificar se botão de trocar academia existe
        const trocaButtonExists = await appiumHelper.elementExists({ id: 'bnt_trocar_academia' });
        console.log(`Botão trocar academia existe: ${trocaButtonExists}`);
        
        // Verificar se campo de inserir nome existe
        const nomeFieldExists = await appiumHelper.elementExists({ id: 'inserir_nome_academia' });
        console.log(`Campo inserir nome existe: ${nomeFieldExists}`);
        
        // Pelo menos um dos elementos deve existir
        expect(trocaButtonExists || nomeFieldExists).to.be.true;
        
        console.log('✅ Verificação de elementos concluída');
        
      } catch (error) {
        console.error('❌ Erro na verificação de elementos:', error.message);
        throw error;
      }
    });
  });
});

module.exports = {
  /**
   * Função auxiliar para trocar academia
   * Pode ser reutilizada em outros testes
   */
  async trocarAcademia(appiumHelper, nomeAcademia = null) {
    console.log('🔄 Executando troca de academia reutilizável...');
    
    const gymName = nomeAcademia || process.env.TEST_GYM_NAME || 'Revisao';
    
    await appiumHelper.tapOn({ id: 'bnt_trocar_academia' });
    await appiumHelper.tapOn('Swap gym');
    await appiumHelper.tapOn('Select your gym');
    await appiumHelper.tapOn({ id: 'inserir_nome_academia' });
    await appiumHelper.inputText(gymName);
    await appiumHelper.tapOn({ point: '50%,32%' });
    await appiumHelper.tapOn('Your fitness journey begins now!');
    
    console.log('✅ Troca de academia reutilizável executada com sucesso');
  }
};
