const { remote } = require('webdriverio');
const config = require('../appium.config');
const fs = require('fs');
const path = require('path');

class AppiumHelper {
  constructor() {
    this.driver = null;
    this.platform = process.env.PLATFORM || 'ios';
    this.retryAttempts = config.retry?.attempts || 3;
    this.retryDelay = config.retry?.delay || 2000;
    this.screenshotDir = path.join(__dirname, '..', 'screenshots');

    // Criar diretório de screenshots se não existir
    this.ensureScreenshotDir();
  }

  ensureScreenshotDir() {
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
    }
  }

  async startDriver() {
    try {
      console.log(`🚀 Iniciando driver para plataforma: ${this.platform}`);

      const capabilities = this.platform === 'ios' ? config.ios : config.android;

      this.driver = await remote({
        protocol: 'http',
        hostname: config.server.host,
        port: config.server.port,
        path: config.server.path,
        capabilities,
        logLevel: 'warn' // Reduzir logs verbosos
      });

      // Configurar timeouts
      await this.driver.setTimeout({
        implicit: config.timeouts.implicit,
        pageLoad: config.timeouts.pageLoad,
        script: config.timeouts.script
      });

      console.log('✅ Driver iniciado com sucesso');
      return this.driver;

    } catch (error) {
      console.error('❌ Erro ao iniciar driver:', error.message);
      throw new Error(`Falha ao iniciar driver Appium: ${error.message}`);
    }
  }

  async stopDriver() {
    try {
      if (this.driver) {
        console.log('🛑 Encerrando driver...');
        await this.driver.deleteSession();
        this.driver = null;
        console.log('✅ Driver encerrado com sucesso');
      }
    } catch (error) {
      console.error('⚠️ Erro ao encerrar driver:', error.message);
      this.driver = null; // Força limpeza mesmo com erro
    }
  }

  // Equivalente ao tapOn do Maestro com retry automático
  async tapOn(selector, options = {}) {
    return await this.retryOperation(async () => {
      console.log(`👆 Clicando em: ${JSON.stringify(selector)}`);

      if (selector && selector.point) {
        // Toque por coordenadas
        await this.tapByCoordinates(selector.point);
      } else {
        // Toque por elemento
        const element = await this.findElement(selector);
        await element.click();
      }

      if (options.waitAfter) {
        await this.pause(options.waitAfter);
      }
    }, `tapOn ${JSON.stringify(selector)}`);
  }

  // Equivalente ao inputText do Maestro com retry automático
  async inputText(text, selector = null) {
    return await this.retryOperation(async () => {
      console.log(`⌨️ Inserindo texto: "${text}"`);

      if (selector) {
        const element = await this.findElement(selector);
        await element.clearValue(); // Limpa campo antes de inserir
        await element.setValue(text);
      } else {
        // Se não há seletor, assume que o elemento já está focado
        await this.driver.keys(text);
      }
    }, `inputText "${text}"`);
  }

  // Equivalente ao waitForAnimationToEnd do Maestro
  async waitForAnimationToEnd(timeout = 5000) {
    console.log(`⏳ Aguardando animação por ${timeout}ms...`);
    await this.pause(timeout);
  }

  // Função pause melhorada
  async pause(milliseconds) {
    await this.driver.pause(milliseconds);
  }

  // Método auxiliar para encontrar elementos com múltiplas estratégias
  async findElement(selector) {
    const strategies = this.getElementStrategies(selector);

    for (const strategy of strategies) {
      try {
        const element = await this.driver.$(strategy);
        if (await element.isExisting()) {
          console.log(`✅ Elemento encontrado com estratégia: ${strategy}`);
          return element;
        }
      } catch (error) {
        // Continua para próxima estratégia
      }
    }

    throw new Error(`Elemento não encontrado com nenhuma estratégia: ${JSON.stringify(selector)}`);
  }

  // Gera múltiplas estratégias de busca para um seletor
  getElementStrategies(selector) {
    const strategies = [];

    if (typeof selector === 'string') {
      if (this.platform === 'ios') {
        strategies.push(
          `~${selector}`, // Accessibility ID
          `//XCUIElementTypeButton[@name="${selector}"]`,
          `//XCUIElementTypeStaticText[@name="${selector}"]`,
          `//XCUIElementTypeTextField[@name="${selector}"]`,
          `//XCUIElementTypeSecureTextField[@name="${selector}"]`,
          `//*[contains(@name,"${selector}")]`,
          `//*[@label="${selector}"]`,
          `//*[contains(@label,"${selector}")]`
        );
      } else {
        strategies.push(
          `//*[@text="${selector}"]`,
          `//*[contains(@text,"${selector}")]`,
          `//*[@content-desc="${selector}"]`,
          `//*[contains(@content-desc,"${selector}")]`,
          `//*[@resource-id="${selector}"]`
        );
      }
    } else if (selector.id) {
      if (this.platform === 'ios') {
        strategies.push(`~${selector.id}`);
      } else {
        strategies.push(`//*[@resource-id="${selector.id}"]`);
      }
    }

    return strategies;
  }

  // Toque por coordenadas
  async tapByCoordinates(point) {
    const [x, y] = point.split(',').map(coord => {
      const percentage = parseFloat(coord.replace('%', ''));
      return percentage;
    });

    const windowSize = await this.driver.getWindowSize();
    const pixelX = Math.round((windowSize.width * x) / 100);
    const pixelY = Math.round((windowSize.height * y) / 100);

    console.log(`👆 Clicando em coordenadas: ${pixelX}, ${pixelY} (${point})`);

    await this.driver.touchAction({
      action: 'tap',
      x: pixelX,
      y: pixelY
    });
  }

  // Método para aguardar elemento aparecer
  async waitForElement(selector, timeout = 10000) {
    console.log(`⏳ Aguardando elemento: ${JSON.stringify(selector)}`);

    return await this.retryOperation(async () => {
      const element = await this.findElement(selector);
      await element.waitForExist({ timeout });
      return element;
    }, `waitForElement ${JSON.stringify(selector)}`);
  }

  // Método para verificar se elemento existe
  async elementExists(selector) {
    try {
      const strategies = this.getElementStrategies(selector);

      for (const strategy of strategies) {
        try {
          const element = await this.driver.$(strategy);
          if (await element.isExisting()) {
            return true;
          }
        } catch (error) {
          // Continua para próxima estratégia
        }
      }

      return false;
    } catch {
      return false;
    }
  }

  // Sistema de retry para operações
  async retryOperation(operation, operationName, maxAttempts = null) {
    const attempts = maxAttempts || this.retryAttempts;
    let lastError;

    for (let attempt = 1; attempt <= attempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.log(`⚠️ Tentativa ${attempt}/${attempts} falhou para ${operationName}: ${error.message}`);

        if (attempt < attempts) {
          console.log(`🔄 Tentando novamente em ${this.retryDelay}ms...`);
          await this.pause(this.retryDelay);
        }
      }
    }

    throw new Error(`Operação ${operationName} falhou após ${attempts} tentativas. Último erro: ${lastError.message}`);
  }

  // Captura de screenshot melhorada
  async captureScreenshot(name = 'screenshot') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${name}-${timestamp}.png`;
      const screenshotPath = path.join(this.screenshotDir, filename);

      const screenshot = await this.driver.takeScreenshot();
      fs.writeFileSync(screenshotPath, screenshot, 'base64');

      console.log(`📸 Screenshot capturado: ${screenshotPath}`);
      return screenshotPath;

    } catch (error) {
      console.error('❌ Erro ao capturar screenshot:', error.message);
      throw error;
    }
  }

  // Método para obter informações do dispositivo
  async getDeviceInfo() {
    try {
      const windowSize = await this.driver.getWindowSize();
      const orientation = await this.driver.getOrientation();

      return {
        platform: this.platform,
        windowSize,
        orientation,
        width: windowSize.width,
        height: windowSize.height
      };
    } catch (error) {
      console.error('❌ Erro ao obter informações do dispositivo:', error.message);
      return null;
    }
  }
}

module.exports = AppiumHelper;
