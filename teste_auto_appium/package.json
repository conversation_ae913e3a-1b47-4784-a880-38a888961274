{"name": "teste-auto-appium", "version": "2.0.0", "description": "Framework de testes automatizados mobile com Appium - Versão Simplificada", "main": "index.js", "scripts": {"setup": "node scripts/setup.js", "start-appium": "appium server --address localhost --port 4723", "test": "mocha tests/**/*.test.js --timeout 60000 --recursive", "test:login": "mocha tests/login.test.js --timeout 120000", "test:android": "cross-env PLATFORM=android npm test", "test:ios": "cross-env PLATFORM=ios npm test", "test:health": "mocha tests/health.test.js --timeout 30000", "doctor": "appium-doctor --android --ios", "clean": "rimraf reports screenshots logs node_modules package-lock.json", "validate": "npm run test:health && npm run doctor"}, "keywords": ["appium", "mobile", "testing", "automation", "ios", "android"], "author": "Equipe de QA", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"appium": "^2.11.0", "appium-doctor": "^1.16.2", "webdriverio": "^8.24.0", "mocha": "^10.2.0", "chai": "^4.3.10", "cross-env": "^7.0.3", "rimraf": "^5.0.5"}, "dependencies": {"dotenv": "^16.3.1"}}