# fvm flutter clean;fvm flutter build appbundle --release --flavor treino -t lib/main-Treino.dart;fvm flutter build appbundle --release --flavor box -t lib/main-Box.dart
# fvm flutter clean;fvm flutter build appbundle --release --flavor box -t lib/main-Box.dart
# fvm flutter clean;fvm flutter build ipa --release --flavor treino -t lib/main-Treino.dart
# fvm flutter clean && fvm flutter build apk --release --flavor treino -t lib/main-Treino.dart && fvm flutter build appbundle --release --flavor box -t lib/main-Box.dart && flutter build appbundle --release --flavor selfit -t lib/main-Selfit.dart
# fvm flutter packages pub run build_runner watch build --delete-conflicting-outputs
# fvm flutter clean;fvm flutter build apk --release --flavor treino -t lib/main-Treino.dart --build-name=7.0.0  --build-number=319
# echo 'pj1p8WsmmVZrJbDC' | dart pub token add https://dart.cloudsmith.io/n2b-brasil/packages/
# echo 'e6WwAWiAl1SzGU6Q' | dart pub token add https://dart.cloudsmith.io/n2b-brasil/patient-app-core/
# flutter build appbundle --analyze-size
# --flavor fabricademonstros -t lib/main_personalizados/main-fdm.dart --build-name="$(echo $CM_TAG | cut -d '-' -f1 | xargs)" --build-number=$(($PROJECT_BUILD_NUMBER+10))
# fvm flutter clean;fvm flutter build appbundle --release --flavor fabricademonstros -t lib/main_personalizados/main-fdm.dart
# --flavor liveacademia -t lib/main_personalizados/main-liveacademia.dart --build-name="$(echo $CM_TAG | cut -d '-' -f1 | xargs)" --build-number=$(($PROJECT_BUILD_NUMBER+10))
# fvm flutter clean;fvm flutter build ipa --release --flavor liveacademia -t lib/main_personalizados/main-liveacademia.dart
# fvm flutter clean;fvm flutter build appbundle --release --flavor liveacademia -t lib/main_personalizados/main-liveacademia.dart
# --flavor maybootcamp -t lib/main_personalizados/main-maybootcamp.dart --build-name="$(echo $CM_TAG | cut -d '-' -f1 | xargs)" --build-number=$(($PROJECT_BUILD_NUMBER+10))
# fvm flutter clean;fvm flutter build ipa --release --flavor maybootcamp -t lib/main_personalizados/main-maybootcamp.dart
# fvm flutter clean;fvm flutter build appbundle --release --flavor maybootcamp -t lib/main_personalizados/main-maybootcamp.dart
# --flavor intensefit -t lib/main_personalizados/main-intensefit.dart --build-name="$(echo $CM_TAG | cut -d '-' -f1 | xargs)" --build-number=$(($PROJECT_BUILD_NUMBER+10))
# fvm flutter clean;fvm flutter build ipa --release --flavor intensefit -t lib/main_personalizados/main-intensefit.dart
# fvm flutter clean;fvm flutter build appbundle --release --flavor intensefit -t lib/main_personalizados/main-intensefit.dart
# --flavor fitstream -t lib/main_personalizados/main-fitstream.dart --build-name="$(echo $CM_TAG | cut -d '-' -f1 | xargs)" --build-number=$(($PROJECT_BUILD_NUMBER+10))
# fvm flutter clean;fvm flutter build ipa --release --flavor fitstream -t lib/main_personalizados/main-fitstream.dart
# fvm flutter clean;fvm flutter build appbundle --release --flavor fitstream -t lib/main_personalizados/main-fitstream.dart
# fvm flutter clean;fvm flutter build appbundle --release --flavor apppersonalizado -t lib/main_personalizados/main-personalizado.dart
name: app_treino
description: Pacto soluções tecnologicas | melhorando sua experiência fitness.
version: 6.17.5+2019013962
publish_to: none

environment:
  sdk: '>=3.1.3 <4.0.0'
 
dependencies:
  flutter:
    sdk: flutter
  intl: ^0.19.0
  crop_image: ^1.0.16
  crop_your_image: ^2.0.0
  flutter_timezone: ^4.1.0
  pretty_qr_code: ^3.0.0
  decimal: ^2.3.3
  easy_localization: ^3.0.0
  mobx: ^2.3.3+2
  flutter_mobx: ^2.2.1+1
  # google_fonts: ^6.2.1
  get_it: ^8.0.3
  permission_handler: ^11.3.0
  firebase_core: 3.13.0
  firebase_auth: 5.4.1
  cloud_firestore: 5.6.2
  firebase_analytics: 11.4.1
  firebase_performance: 0.10.1+1
  firebase_messaging: 15.2.1
  firebase_crashlytics: 4.3.1
  # firebase_dynamic_links: 6.1.5
  firebase_remote_config: ^5.4.3
  firebase_storage: 12.4.5
  flutter_facebook_auth: ^7.1.1
  # purchases_flutter: ^6.3.0
  #in_app_review: ^2.0.2
  health: ^12.1.0 
  liquid_progress_indicator_v2: ^0.5.0
  encrypt: ^5.0.1
  retrofit: ^4.2.0
  json_annotation: ^4.8.1
  url_launcher: ^6.3.0
  cached_network_image: ^3.2.3
  shared_preferences: ^2.2.0
  after_layout: ^1.2.0
  pin_code_fields: ^8.0.1
  #carousel_slider: ^4.0.0
  awesome_card: ^1.1.7
  path_provider: ^2.0.15
  sembast: ^3.4.9
  sembast_web:
  font_awesome_flutter: ^10.6.0
  time_ago_provider: 
  like_button: ^2.0.5
  pull_to_refresh: ^2.0.0
  event_bus: ^2.0.0
  youtube_parser: ^2.0.0
  #camera_gallery_image_picker: ^0.0.4
  image_picker: ^1.0.4
  webview_flutter: 4.11.0
  screenshot: ^3.0.0
  percent_indicator: ^4.2.2
  dots_indicator: ^3.0.0
  #flutter_slidable: ^3.0.0
  dotted_line: ^3.0.0
  #share_plus: ^7.2.1
  flutter_chat_bubble: ^2.0.0
  diacritic: ^0.1.3
  auto_size_text: ^3.0.0-nullsafety.0
  circular_countdown_timer: ^0.2.0
  youtube_player_flutter: ^9.1.1
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.2
  expandable: ^5.0.1 
  date_format: ^2.0.4
  skeleton_text: ^3.0.1
  camera: ^0.10.5+4
  app_tracking_transparency: ^2.0.6+1
  flare_flutter: any
  flutter_calendar_carousel: ^2.4.1
  flutter_svg: ^2.0.4
  # syncfusion_flutter_datepicker: ^27.2.5
  # syncfusion_flutter_charts: ^27.2.5
  detectable_text_field: ^3.0.2
  collection: ^1.17.0
  uuid: ^4.5.1
  html: ^0.15.0
  image_gallery_saver:
    git:
      url: https://github.com/luanbatistadev/image_gallery_saver.git
  esys_flutter_share_plus: ^2.3.0
  restart_app: ^1.1.0
  fluttertoast: ^8.0.9
  card_swiper: ^3.0.1
  badges: ^3.0.2
  signature: ^5.4.0
  just_audio: ^0.9.36
  audio_session: ^0.1.18
  custom_sliding_segmented_control: ^1.7.2
  wakelock_plus: ^1.1.1
  scrollable_positioned_list: ^0.3.5
  pinch_zoom: ^2.0.0
  easy_pdf_viewer: ^1.0.4
  rive: ^0.12.3
  jaguar_jwt: ^3.0.0
  # fvm flutter pub upgrade ds_pacto
  ds_pacto:
    # path: /Users/<USER>/Documents/pacto/lib_dspacto
     git:
      url: https://CI:<EMAIL>/Plataformazw/apps/ds-team-app.git
      ref: stable-01
  # ondoc_flut_opentok:
  #   git:
  #     url: https://github.com/derafael04/n2b-pacto.git
  #     ref: master
  # fvm flutter pub upgrade pacto_nutri_module
  health_devices:
    # path: /Users/<USER>/Documents/pacto/health_devices
    git:
      url: https://HEALTH:<EMAIL>/Plataformazw/apps/appsemflutter/nossas-libs/health-devices.git
      ref: fix/beurer
  package_info_plus: ^8.3.0
  # echo 'pj1p8WsmmVZrJbDC' | fvm flutter pub token add https://dart.cloudsmith.io/n2b-brasil/patient-app-core/
  pacto_nutri_module:
    hosted: https://dart.cloudsmith.io/n2b-brasil/packages/
    version: ^1.3.1
  # launch_review: ^3.0.1
  activity_ring: ^0.3.0
  gif: ^2.3.0
  provider: ^6.0.2
  flutter_switch: ^0.3.2
  timelines_plus: ^1.0.6
  juxtapose: ^1.0.2
  lottie: ^2.7.0
  flutter_widget_from_html_core: ^0.15.2
  flutter_image_compress: ^2.1.0
  shake: ^2.2.0
  dart_ipify: ^1.1.1
  loading_animation_widget: ^1.1.0+0
  flutter_keyboard_visibility: ^5.4.1
  pdf: ^3.10.4
  device_info_plus: ^9.1.2
  android_id: ^0.3.6
  #http: ^0.13.6
  get_ip_address: ^0.0.6
  external_app_launcher: ^4.0.0
  flutter_xlider: ^3.5.0
  flutter_secure_storage: ^9.2.4
  pointycastle: ^3.9.1
  avatar_glow: ^3.0.1
  toastification: ^1.0.1
  image: any
  qr_code_scanner: any
  #dio: any
  flutter_blue_plus: any
  path: any
  crypto: any
  # mockito: ^5.4.4
  test: ^1.24.0

  # google_mlkit_text_recognition: ^0.15.0
  # google_mlkit_face_detection: ^0.13.1
dependency_overrides:
  intl: ^0.20.2
  share_plus: ^10.0.0
  flutter_slidable: ^3.0.0
  dio: ^5.4.1
  http: ^1.1.0
  sensors_plus: ^6.1.1
  camera_android_camerax: 0.6.10+1
  fl_chart: ^0.69.0
  skeletonizer: ^1.4.2
  sqflite: 2.3.3+1
  # fvm flutter pub upgrade assets_audio_player
  assets_audio_player: 
    git:
      url: https://github.com/jpaulo789b/Flutter-AssetsAudioPlayer.git
  file: ^6.1.0 # Ajuste para a versão necessária pela dart_code_metrics
  rxdart: ^0.27.0
  flare_flutter:
    git:
      url: https://github.com/mbfakourii/Flare-Flutter.git
      path: flare_flutter
      ref: remove_hashValues
  qr_code_scanner:
    git:
      url: https://github.com/X-SLAYER/qr_code_scanner.git
      ref: flutter-beta  
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  json_serializable:
  mobx_codegen: ^2.3.0
  retrofit_generator: ^9.6.0
  flutter_lints: ^3.0.1
 
  

flavorizr:
  app:
    android:
      flavorDimensions: "flavor-type"
    ios:

  flavors:
    treino:
      app:
        name: "Treino"
      android:
        applicationId: "com.pacto"
      ios:
        bundleId: "com.pacto.treino"
    box:
      app:
        name: "Meu Box"
      android:
        applicationId: "app.com.meubox"
      ios:
        bundleId: "br.com.pactosolucoes.zwcross"
    academia:
      app:
        name: "Minha Academia"
      android:
        applicationId: "com.pacto.zwacademia"
      ios:
        bundleId: "br.com.pactosolucoes.zwacademia"    
    selfit:
      app:
        name: "Selfit"
      android:
        applicationId: "br.com.pactosolucoes.selfit"
      ios:
        bundleId: "br.com.pactosolucoes.selfit"
    wellness:
      app:
        name: "Wellness Club"
      android:
        applicationId: "com.pacto.wellness"
      ios:
        bundleId: "com.pacto.wellness" 
    winnersgym:
      app:
        name: "Winners Gym"
      android:
        applicationId: "com.pacto.winnersgym"
      ios:
        bundleId: "com.branded.app.winnerstrainingcenter" 
    engenharia_do_corpo:
      app:
        name: "Engenharia do Corpo"
      android:
        applicationId: "com.pacto.engenhariadocorpo"
      ios:
        bundleId: "com.pacto.engenhariadocorpo"  
    
        

flutter:

  uses-material-design: true
  fonts:
    - family:  PersonalIcon
      fonts:
       - asset: fonts/PersonalIcon.ttf
    - family:  TreinoIcon
      fonts:
       - asset: fonts/TreinoIcon.ttf
    - family:  SFCompact
      fonts:
       - asset: fonts/ios_fonts/SF-Compact-Display-Regular.ttf
  assets:
    - google_fonts/
    - assets/applogo/
    - assets/langs/
    - assets/gifs/
    - assets/images/
    - assets/images/icones/
    - assets/images/icones/agua/
    - assets/images/icones/reservaEquipamentos/
    - assets/images/empty/
    - assets/images/treinoDefault/
    - assets/images/imageCross/
    - assets/images/treinoAluno/
    - assets/images/imagemWod/
    - assets/images/imagemAulas/
    - assets/images/wodIntensidade/
    - assets/images/imageBeach/
