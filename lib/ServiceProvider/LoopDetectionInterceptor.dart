import 'package:dio/dio.dart';
import 'dart:async';
import 'package:crypto/crypto.dart';
import 'dart:convert';

class LoopDetectionInterceptor extends Interceptor {
  // Mapa para armazenar contadores de requisições por URL
  final Map<String, List<DateTime>> _requestTimestamps = {};
  // Mapa para armazenar respostas em cache
  final Map<String, Response> _responseCache = {};
  // Mapa para armazenar timestamps das respostas em cache
  final Map<String, DateTime> _cacheTimestamps = {};

  // Define o limite de requisições por serviço (URL) e o intervalo em segundos
  final int requestLimit = 15;
  final int timeWindow = 10; // Intervalo de tempo em segundos

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Gera uma chave única para identificar o serviço (URL)
    final requestKey = _generateRequestKey(options);

    final now = DateTime.now();
    // Verifica se a URL contém o domínio específico
    if (options.uri.toString().contains('https://discovery.ms.pactosolucoes.com.br/find')) {
      // Verifica se a resposta está no cache e é recente
      if (_responseCache.containsKey(requestKey) && now.difference(_cacheTimestamps[requestKey]!).inSeconds <= timeWindow) {
        // Retorna a resposta do cache
        return handler.resolve(_responseCache[requestKey]!);
      }
    }

    // Inicializa a lista de timestamps se não existir
    if (!_requestTimestamps.containsKey(requestKey)) {
      _requestTimestamps[requestKey] = [];
    }

    // Remove registros antigos que não estão mais no intervalo de tempo (10 segundos)
    _requestTimestamps[requestKey] = _requestTimestamps[requestKey]!.where((timestamp) => now.difference(timestamp).inSeconds <= timeWindow).toList();

    // Adiciona o timestamp atual
    _requestTimestamps[requestKey]!.add(now);
    if (options.uri.path.contains('/authUser/DKZXM20v6ESb7wzZ9jrcGw%3D%3D')) {
      return handler.next(options);
    }
    // Verifica se o número de requisições excede o limite
    if (_requestTimestamps[requestKey]!.length > requestLimit) {
      // Se exceder o limite, considera que pode estar ocorrendo um loop
      return handler.reject(
        DioException(
          requestOptions: options,
          error: 'Loop de requisições detectado para o serviço ${options.uri}',
          type: DioExceptionType.unknown,
        ),
      );
    }

    // Caso contrário, prossiga com a requisição
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Gera uma chave única para identificar o serviço (URL)
    final requestKey = _generateRequestKey(response.requestOptions);

    // Verifica se a URL contém o domínio específico
    if (response.requestOptions.uri.toString().contains('https://discovery.ms.pactosolucoes.com.br/find')) {
      // Armazena a resposta no cache
      _responseCache[requestKey] = response;
      _cacheTimestamps[requestKey] = DateTime.now();
    }

    // Continue com a resposta normalmente
    return handler.next(response);
  }


  // Função para gerar uma chave única com base na URL e parâmetros
  String _generateRequestKey(RequestOptions options) {
    // ignore: unnecessary_null_comparison
    if (options.uri == null) {
      options.queryParameters = {};
    }
    final url = options.uri.toString();
    final queryParams = options.queryParameters.toString();
    final headersCopy = Map<String, dynamic>.from(options.headers);
    headersCopy.remove('User-Agent');
    headersCopy.remove('Authorization');
    headersCopy.remove('fbchave');
    headersCopy.remove('fbcodEmpresa');
    headersCopy.remove('limiteTimeout');
    headersCopy.remove('original');
    headersCopy.remove('originalQuerys');
    headersCopy.remove('content-length');
    final headers = headersCopy.toString();
    final body = options.data.toString();
    final keyString = '$url-$queryParams-$headers-$body';
    return md5.convert(utf8.encode(keyString)).toString();
  }
}
