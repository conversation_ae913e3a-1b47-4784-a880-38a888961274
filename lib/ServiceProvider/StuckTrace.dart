import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_treino/ServiceProvider/authServices/ClienteAppService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/NavigatorController.dart';
import 'package:app_treino/fabricaGetIt.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StuckTrace {
  Function()? tentarNovamente;
  final navigationObserver = NavigationHistoryObserver();
  static final StuckTrace _singleton = StuckTrace._internal();
  ControladorApp get mCApp => GetIt.I.get<ControladorApp>();
  ControladorCliente get mCc => GetIt.I.get<ControladorCliente>();
  bool submitLog = true, isInHome = false;
  bool exibindo_sem_internet = false, exibindo_relogar = false;
  int tentativas = 0;
  List<RequestOptions> requisicoes = [];
  final Map<String, Map<String, dynamic>> _mRespones = {}, _mErros = {};
  factory StuckTrace() {
    return _singleton;
  }

  void putErro(DioException e, {int? elapsedTime}) {
    if (StuckTrace().submitLog) {
      _mErros[DateTime.now().toIso8601String()] = {
        'responseInMs': elapsedTime ?? -1,
        'uri': e.response?.realUri.toString() ?? e.requestOptions.uri.toString(),
        'message': e.toString(),
      };
    }
  }

  void putResponse(Response response, {int? elapsedTime}) {
    if (StuckTrace().submitLog) {
      _mRespones[DateTime.now().toIso8601String()] = {
        'responseInMs': elapsedTime ?? -1,
        'uri': response.realUri.toString(),
      };
    }
  }

  void putRequest(RequestOptions options) {
    StuckTrace().requisicoes.add(options);
  }

  String? get authKey => '';
  Map<String, dynamic>? get mClienteAppStuck => mStuckTeste['mClienteapp'] != null ? mStuckTeste['mClienteapp'] : null;
  Map<String, dynamic>? get mUsuarioLogado => mStuckTeste['mUsuario'] != null ? mStuckTeste['mUsuario'] : null;
  bool aplicar = false;
  setToken(Function() done) async {
    if (!aplicar) {
      SharedPreferences.getInstance().then((db) async {
        dadosDoStack['TokenColaborador${tentativas}'] = db.getString(UserDataKeys.TOKENCOLABORADOR.toString()) ?? db.getString(UserDataKeys.TOKENDEAUTHAPI.toString());
        dadosDoStack['TokenUsuario${tentativas}'] = db.getString(UserDataKeys.TOKENUSUARIO.toString()) ?? db.getString(UserDataKeys.TOKENDEAUTHAPI.toString());
        await Future.wait([
          UserDataKeys.TOKENCOLABORADOR,
          UserDataKeys.TOKENUSUARIO,
          UserDataKeys.VALIDADETOKEN,
        ].map((e) => db.remove(e.toString())));
        done();
      });
      return;
    }
    // UserDataKeys.TOKENUSUARIO
    SharedPreferences.getInstance().then((db) {
      db.setString(UserDataKeys.CHAVE.toString(), '79b6fe8f03c95397a40490c0776f45a3');
      if (authKey != null) {
        db.setString(UserDataKeys.TOKENUSUARIO.toString(), authKey!);
      }
      done();
    });
  }

  var dadosDoStack = {};
  Timer? _debounce;
  Map<String, dynamic> get mStuckTeste => {};
  void cancelSendLog() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    submitLog = false;
  }

  Future<void> sendLog() async {
    submitLog = true;
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    PackageInfo info = await PackageInfo.fromPlatform();
    _debounce = Timer(const Duration(seconds: 30), () async {
      try {
        var cc = GetIt.I.get<ControladorCliente>();
        var ca = GetIt.I.get<ControladorApp>();
        // Verifica se tudo foi carregado se sim tenta jogar para home :)
        if (cc.mUsuarioLogado != null && ca.mClienteAppSelecionado != null && cc.mUsuarioAuth != null && ca.chave != null && ca.mConfiguracoesTreinoWeb.isNotEmpty) {
          DSLib.theme = ca.themeMode;
          GetIt.I.get<ControladorExecucaoTreino>().getTreinoEmExecucao();
          GetIt.I.get<ControladorApp>().prepararTelasBaseadoNasConfiguracoes(
              context: GetIt.I.get<NavigationService>().context, eColaborador: GetIt.I.get<ControladorCliente>().isUsuarioColaborador, tipoApp: F.appFlavor!);
          analytic(EventosKey.stuck_to_home);
          Navigator.of(GetIt.I.get<NavigationService>().context).pushReplacementNamed('/homePageApp');
          StuckTrace().cancelSendLog();
          return;
        } else if (cc.mUsuarioLogado == null) {
          Navigator.of(GetIt.I.get<NavigationService>().context).pushReplacementNamed('/homePageApp');
          Navigator.of(GetIt.I.get<NavigationService>().context).pushNamed('/telaPreLogin');
          StuckTrace().cancelSendLog();
          return;   
        }
        dadosDoStack['conexao'] = await ConnectionQuality().checkConnectionQuality();
        dadosDoStack['routeHistory'] = navigationObserver.getHistory();
        if (!StuckTrace().submitLog) {
          return;
        }
        if (isInHome) {
          return;
        }

        BuildContext context = GetIt.I.get<NavigationService>().context;

        dadosDoStack['mUsuario'] = cc.mUsuarioLogado != null ? cc.mUsuarioLogado!.toJson() : 'sem user';
        dadosDoStack['mUsuarioFirebase'] = cc.mUsuarioAuth != null ? cc.mUsuarioAuth!.toJson() : 'sem user';
        dadosDoStack['mClienteapp'] = ca.mClienteAppSelecionado != null ? ca.mClienteAppSelecionado!.toJson() : 'sem app selecionado';
        dadosDoStack['userDeviceId'] = thedeviceID;
        dadosDoStack['urls'] = ca.mUrls?.toJson() ?? 'sem urls';
        dadosDoStack['historyDio'] = [];
        for (final options in requisicoes) {
          (dadosDoStack['historyDio'] as List).add(_cURLRepresentation(options));
        }
        dadosDoStack['appInfo'] = {
          'buildNumber': info.buildNumber,
          'buildName': info.version,
        };
        bool hasInternet = dadosDoStack['conexao']['temAcesso'] != 'failed';
        if (StuckTrace().submitLog && hasInternet) {
          if (tentativas == 0) {
            tentativas = 1;
            StuckTrace().cancelSendLog();
            tentarNovamente?.call();
            return;
          }
          StuckTrace().submitLog = true;
          dadosDoStack['mostrouStuckDialog'] = true;
          if (!exibindo_relogar) {
            exibindo_relogar = true;
          } else {
            return;
          }
          DSalerta().exibirAlerta(
              context: context,
              titulo: 'splash_preso_titulo',
              subtitulo: 'splash_preso_mensagem',
              tituloBotao: 'splash_preso_refazer_login',
              tituloBotaoSecundario: 'splash_preso_recarregar_app',
              onTapSecundario: () {
                exibindo_relogar = false;
                Navigator.pop(context);
                tentarNovamente?.call();
              },
              onTap: () {
                exibindo_relogar = false;
                analytic(EventosKey.log_out_preso_splash);
                mCc.consultarSeTemUsuariosQueJaLogaram(
                  (usuarios) async {
                    var clienteAppAtual = mCApp.mClienteAppSelecionado!.clone();
                    await mCc.deletarDoBanco('clienteAppSelecionado');
                    mCc.deslogarUsuario(sucesso: () async {
                      if (usuarios.isNotEmpty) {
                        try {
                          var userKeep = usuarios.firstWhere((a) => a.clienteApp == clienteAppAtual.documentkey);
                          mCc.removerUsuarioDoKeepUser(userKeep);
                        } catch (e) {
                          if (kDebugMode) {
                            print(e);
                          }
                        }
                      }
                      mCApp.limparTodosControladores();
                      await mCApp.deletarDoBanco('clienteAppSelecionado');
                      mCApp.mClienteAppSelecionado = null;
                      mCc.mUsuarioLogado = null;
                      FocusScope.of(context).requestFocus(FocusNode());
                      Navigator.of(context).pushNamedAndRemoveUntil('/', (Route<dynamic> route) => false);
                    });
                  },
                );
              });
        } else if (!hasInternet && StuckTrace().submitLog) {
          dadosDoStack['mostrouSemNet'] = true;
          if (tentativas == 0) {
            tentativas = 1;
            tentarNovamente?.call();
            StuckTrace().cancelSendLog();
            return;
          }
          if (!exibindo_sem_internet) {
            exibindo_sem_internet = true;
          } else {
            return;
          }
          DSalerta().exibirAlertaSimplificado(
            context: context,
            titulo: 'unable_to_load_data',
            subtitulo: 'no_internet_connection',
            tituloBotao: 'splash_preso_recarregar_app',
            onTap: () {
              StuckTrace().cancelSendLog();
              exibindo_sem_internet = false;
              Navigator.pop(context);
              tentarNovamente?.call();
            },
          );
          return;
        }

        if (hasInternet && kReleaseMode && StuckTrace().submitLog) {
          String? ip = await GetIt.I.get<ClienteAppService>().getUserIP();
          dadosDoStack['userIP'] = ip ?? 'NO IP';
          dadosDoStack['sistema'] = Platform.isIOS ? 'ios' : 'android';
          dadosDoStack['HoraFato'] = DateTime.now().toIso8601String();
          await FirebaseRemoteConfig.instance.ensureInitialized();

          FirebaseRemoteConfig.instance.fetchAndActivate().then((value) async {
            return FirebaseRemoteConfig.instance.getString('splash_bot_min_version');
          }).then((remoteInfo) async {
            if (await isAppVersionEqualsOrAbove(remoteInfo)) {
              try {
                dadosDoStack['HttpResponses'] = _mRespones;
                dadosDoStack['HttpResponsesErros'] = _mErros;
                bool logout = false;
                if (FirebaseAuth.instance.currentUser == null) {
                  logout = true;
                  await FirebaseAuth.instance.signInAnonymously();
                }
                FirebaseFirestore.instance
                    .collection('splashErros')
                    .add({'version': info.version, 'app': F.nomeApp, 'text': '⚠️ Falha ao carregar app *${F.nomeApp} (${info.version})*', 'json': dadosDoStack}).then(
                        (x) async => {if (logout) await FirebaseAuth.instance.signOut()});
              } catch (e) {}
            }
          });
        }
      } catch (e) {}
    });
  }

  Future<bool> isAppVersionEqualsOrAbove(String version) async {
    try {
      PackageInfo info = await PackageInfo.fromPlatform();
      if (version.isNotEmpty) {
        List<int> v1 = info.version.split('.').map(int.parse).toList();
        List<int> v2 = version.split('.').map(int.parse).toList();
        for (int i = 0; i < 3; i++) {
          if (v1[i] > v2[i]) return true;
          if (v1[i] < v2[i]) return false;
        }
        return true; // as versões são iguais
      }
      return true;
    } catch (error) {
      return true;
    }
  }

  StuckTrace._internal();
  String _cURLRepresentation(RequestOptions options) {
    List<String> components = ['curl -i'];
    if (options.method.toUpperCase() != 'GET') {
      components.add('-X ${options.method}');
    }

    options.headers.forEach((k, v) {
      if (k != 'Cookie') {
        components.add('-H "$k: $v"');
      }
    });

    if (options.data != null) {
      // FormData can't be JSON-serialized, so keep only their fields attributes
      if (options.data is FormData) {
        options.data = Map.fromEntries(options.data.fields);
      }

      final data = const JsonCodec().encode(options.data).replaceAll('"', '\\"');
      components.add('-d "$data"');
    }

    components.add('"${options.uri.toString()}"');

    return components.join(' \\\n\t');
  }

  void putExtra(String s, onError) {
    try {
      dadosDoStack[s] = onError.toString();
    } catch (e) {
      dadosDoStack[s] = e.toString();
    }
  }
}

class NavigationHistoryObserver extends NavigatorObserver {
  final List<String> _history = [];

  // Método que retorna o histórico de navegação
  List<String> getHistory() => List.unmodifiable(_history);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _history.add(route.settings.name ?? '');
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    if (newRoute != null) {
      _history.add(newRoute.settings.name ?? '');
    }
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
  }
}

class ConnectionQuality {
  static final ConnectionQuality _singleton = ConnectionQuality._internal();
  ConnectionQuality._internal();

  DateTime? _lastCheckedTime;
  Map<String, String>? _lastConnectionData;

  factory ConnectionQuality() {
    return _singleton;
  }
  Future<Map<String, String>> checkConnectionQuality() async {
    final now = DateTime.now();

    // Se a última verificação foi feita há menos de 2 minutos, retorna os dados antigos
    if (_lastCheckedTime != null && now.difference(_lastCheckedTime!).inMinutes < 2) {
      return _lastConnectionData!;
    }

    String connectionType = '';
    String provider = 'Desconhecido';

    // Verifica se está no Wi-Fi ou Dados Móveis
    connectionType = await _getConnectionType();

    // if (connectionType == 'Nenhuma') {
    //   return {
    //     'qualidade': 'Sem conexão',
    //     'tipo_conexao': connectionType,
    //     'provedor': provider,
    //     'temAcesso': 'failed',
    //   };
    // }

    final stopwatch = Stopwatch()..start();

    try {
      // Realiza uma requisição para medir o tempo de resposta
      // ignore: unused_local_variable
      final response = await _pingGoogle();
      stopwatch.stop();

      final int responseTime = stopwatch.elapsedMilliseconds;
      if (response == 'failed') {
        return {
          'qualidade': 'Sem conexão',
          'tipo_conexao': connectionType,
          'provedor': provider,
          'temAcesso': 'failed',
        };
      }

      String quality = classifyConnection(responseTime);

      _lastConnectionData = {
        'qualidade': quality,
        'tipo_conexao': connectionType,
        'provedor': provider,
        'temAcesso': response,
      };
    } catch (e) {
      _lastConnectionData = {
        'qualidade': 'Erro ao medir a conexão',
        'tipo_conexao': connectionType,
        'provedor': provider,
        'temAcesso': 'failed',
      };
    }
    return _lastConnectionData!;
  }

  String classifyConnection(int responseTime) {
    // Baseado no tempo de resposta em milissegundos
    if (responseTime < 100) {
      return 'Excelente';
    } else if (responseTime < 300) {
      return 'Boa';
    } else if (responseTime < 600) {
      return 'Média';
    } else if (responseTime < 1000) {
      return 'Ruim';
    } else {
      return 'Muito Ruim';
    }
  }

  Future<String> _pingGoogle() async {
    try {
      final result = await InternetAddress.lookup('discovery.ms.pactosolucoes.com.br');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return 'success';
      }
    } on SocketException catch (_) {
      return 'failed';
    }
    return 'failed';
  }

  Future<String> _getConnectionType() async {
    try {
      List<NetworkInterface> interfaces = await NetworkInterface.list();

      // Verifica se está conectado a um Wi-Fi ou Dados Móveis
      for (final interface in interfaces) {
        if (interface.name.contains('en') || interface.name.contains('wlan')) {
          return 'Wi-Fi';
        } else if (interface.name.contains('rmnet') || interface.name.contains('wwan')) {
          return 'Dados Móveis';
        } else if (interface.name.contains('eth')) {
          return 'Cabo';
        } else if (Platform.isIOS && (interface.name.contains('pdp_ip') || interface.name.contains('ipsec'))) {
          return 'Dados Móveis';
        }
      }
    } catch (e) {
      return 'Nenhuma';
    }
    return 'Nenhuma';
  }
}
