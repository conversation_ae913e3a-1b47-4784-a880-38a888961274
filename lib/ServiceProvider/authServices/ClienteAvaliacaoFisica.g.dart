// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ClienteAvaliacaoFisica.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations,unused_element_parameter

class _ClienteAvaliacaoFisicaService implements ClienteAvaliacaoFisicaService {
  _ClienteAvaliacaoFisicaService(this._dio, {this.baseUrl, this.errorLogger});

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<List<FotoUsuarioAvaliacao>> consultarHistoricoFotosAvaliacao(
    String usuarioApp,
    String idAvaliacao,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'usuarioApp': usuarioApp,
      r'idAvaliacao': idAvaliacao,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<FotoUsuarioAvaliacao>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{FIREBASE}/authUser/consultarHistoricoFotosAvaliacao',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<FotoUsuarioAvaliacao> _value;
    try {
      _value = _result.data!
          .map(
            (dynamic i) =>
                FotoUsuarioAvaliacao.fromJson(i as Map<String, dynamic>),
          )
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<FotoUsuarioAvaliacao>> consultarTodasFotosAvaliacao(
    String refUsuarioApp,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'refUsuarioApp': refUsuarioApp};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<FotoUsuarioAvaliacao>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{FIREBASE}/authUser/consultarTodasFotosAvaliacao',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<FotoUsuarioAvaliacao> _value;
    try {
      _value = _result.data!
          .map(
            (dynamic i) =>
                FotoUsuarioAvaliacao.fromJson(i as Map<String, dynamic>),
          )
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> atualizarFotosAvaliacao(
    String usuarioApp,
    Map<String, dynamic> fotoUsuarioAvaliacao,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'usuarioApp': usuarioApp};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(fotoUsuarioAvaliacao);
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{FIREBASE}/authUser/atualizarFotosAvaliacao',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> removerFotoAvaliacao(String id, String usuarioApp) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'usuarioApp': usuarioApp,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'DELETE', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{FIREBASE}/authUser/removerFotoAvaliacao',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
