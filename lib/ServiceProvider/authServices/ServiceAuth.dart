import 'package:app_treino/ServiceProvider/ConexaoFracaInterceptor.dart';
import 'package:app_treino/ServiceProvider/KeepInSessionInterceptor.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/model/NotificacaoCRM.dart';
import 'package:app_treino/model/bodyscrypto.dart';
import 'package:app_treino/model/doClienteApp/DadosDoUsuario.dart';
import 'package:app_treino/model/doClienteApp/TokenAutenticacao.dart';
import 'package:app_treino/model/doClienteApp/UsuarioAppTelefone.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/BodyAuthSocial.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/BodyLinkLogin.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/DadosUsuario.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/PontuacaoAluno.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/ResponsePreviusUsers.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/ResultCentralDeAjuda.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/SituacaoCliente.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/UserAuthFirebase.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/Usuario.dart';
import 'package:app_treino/model/doClienteApp/doUsuario/registrar_uso_app.dart';
import 'package:app_treino/model/historicoPresenca/historico_presenca.dart';
import 'package:app_treino/model/termo_de_aceite.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';
import 'package:dio/dio.dart' hide Headers;

part 'ServiceAuth.g.dart';

@RestApi()
abstract class ServiceAuth {
  factory ServiceAuth(Dio dio, {String? baseUrl}) = _ServiceAuth;

  @GET('https://pactosolucoes.com.br/ajuda/kb/login/json')
  Future<ResultCentralDeAjuda> getCentralDeAjuda();

  @PATCH('${ConfigURL.FIREBASE}/usuario/veificarUsuariosPorDeviceID')
  Future<ResponsePreviusUsers> veificarUsuariosPorDeviceID(@Body() Map<String, dynamic> body);

  @UseAppCache()
  @POST('${ConfigURL.TOKENWEB}/aut/gt/')
  Future<TokenAutenticacao> consultarAuth(@Body() Map<String, dynamic> token);

  @PATCH('${ConfigURL.FIREBASE}/usuario/validarCodigoLinkLogin')
  Future<BodyLinkLogin> validarCodigoLinkLogin(@Body() BodyLinkLogin body);

  @PATCH('${ConfigURL.FIREBASE}/usuario/solicitarLinkDeLogin')
  Future<BodyLinkLogin> solicitarLinkDeLogin(@Body() BodyLinkLogin body);

  @UseAppCache()
  @UseAppCrypto()
  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/vA6fM2oW4uY8tS4aA4bZ6tM2uA0tV8dB')
  Future<Usuario> loginSenha(@Body() BodyLoginPasswordCrypto body);

  @PATCH('${ConfigURL.FIREBASE}/usuario/usuarioVinculadoApple')
  Future<BodyAuthSocial> usuarioVinculadoApple(@Body() BodyAuthSocial body);

  @POST('${ConfigURL.FIREBASE}/usuario/vincularUsuariosSocial')
  Future<BodyAuthSocial> vincularUsuariosSocial(@Body() BodyAuthSocial body);

  @UseAppCache()
  @UseAppCrypto()
  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU')
  Future<Usuario> loginRedeSocial(@Body() BodyLoginCrypto body);

  @UseAppCache()
  @UseAppCrypto()
  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/v2/fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU')
  Future<Usuario> loginRedeSocialV2(@Body() BodyLoginAppPorCod body);

  @UseAppCache()
  @POST('${ConfigURL.TREINO}/prest/login/v3/app')
  Future<String> gerarTokenAuthColaboradorV3(@Body() Map<String, dynamic> dadosUsuario);

  @UseAppCrypto()
  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/rO2aS2tZ5jJ2aM8hE3dZ3pH1jY4lG1mM')
  Future<String> enviarLembreteSenha(@Body() BodyRecuperarSenhaCrypto body);

  @POST('${ConfigURL.OAMD}/prest/usuarioapp/{chave}/gerarUsuarioTR')
  Future<String> gerarUsuarioTR(@Query('email') String email, @Query('senha') String senha, {@Query('codigoCliente') int? codigoCliente, @Query('codigoColaborador') int? codigoColaborador});

  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/alterarFotoUsuarioAPP')
  Future<String> alterarFotoPerfil(@Query('email') String email, @Body() Map<String, String> imagem,
      {@Query('matricula') int? matricula, @Query('usuario') int? usuario, @Query('atualizaFotoZW') bool? atualizaFotoZW});

  @UseAppCache()
  @POST('${ConfigURL.FIREBASE}/authUser/auth')
  Future<UserAuthFirebase> pegaInfoAuthFirebase(@Body() Map<String, dynamic> bodyAuth);

  @UseAppCache()
  @PATCH('${ConfigURL.FIREBASE}/authUser/manterUsuario')
  Future<String> manterUsuarioFirebase(@Body() Map<String, dynamic> bodyAuth, {@Query('personalMantendo') bool personalMantendo = false});

  @UseAppCrypto()
  @POST('${ConfigURL.OAMD}/prest/empresa/{chave}/iY7pJ3sO6sT0rA0nR3wS8jQ2yL8iO2qX')
  Future<List<UsuarioAppTelefone>> consultarPeloNumeroTelefone(@Body() BodyDescrobrirUsuariosCrypto body);

  @UseAppCrypto()
  @POST('${ConfigURL.OAMD}/prest/empresa/{chave}/wV9zD7rW1yJ6tI4zG4nX7fT9oL2fG4fJ')
  Future<List<UsuarioAppTelefone>> consultarPeloEmailOuUsername(@Body() BodyLoginCrypto bodyAuth);

  @GET('${ConfigURL.TREINO}/prest/usuario/{chave}/dados')
  Future<DadosUsuario> consultarDadosUsuario(@Query('username') String username);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarClienteJson')
  Future<List<UsuarioAppTelefone>> consultarPeloNumeroCPF(@Query('cpf') String cpf);

  @UseAppCache()
  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/consultarDadosCliente')
  Future<DadosDoUsuario> consultarDadosCliente(@Query('matricula') String matricula);

  @POST('${ConfigURL.APIZW}/prest/negociacao/{chave}/obterBandeirasConvenio')
  Future<BandeirasConvenio> obterBandeirasConvenio(@Query('matricula') String matricula);

  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/historicoPontosAluno')
  Future<List<PontuacaoAluno>> obterPontosPorCliente(@Query('matricula') String matricula, @Query('analitico') bool analitico);

  @GET('${ConfigURL.TREINO}/prest/cliente/{chave}/brindes')
  Future<List<Brindes>> obterDadosBrinde({@Query('ativo') bool ativo = true});

  @POST('${ConfigURL.APIZW}/prest/negociacao/{chave}/incluirAutorizacaoCobranca')
  Future<String> incluirAutorizacaoCobrancaCartaoCredito(
      @Query('cliente') int cliente,
      @Query('operadoraCartao') String operadoraCartao,
      @Query('validadeCartao') String validadeCartao,
      @Query('convenioCobranca') int convenio,
      @Query('numeroCartao') String numeroCartao,
      @Query('cvv') String cvv,
      @Query('titularCartao') String titularCartao,
      @Query('usarConfConvEmpresa') bool usarConfConvEmpresa,
      @Query('origemCobrancaEnum') num origemCobrancaEnum);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/alterarTelefone')
  Future<String> alterarTelefoneUsuario(@Query('codigo') num codigoTelefone, @Query('tipo') String tipo, @Query('numero') String numero, @Query('descricao') String descricao,
      @Query('excluir') bool excluir, @Query('matricula') String matricula, @Query('origemSistema') num origemSistema);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/alterarEmail')
  Future<String> alterarEmailUsuario(@Query('codigo') num codigoEmail, @Query('email') String email, @Query('emailCorrespondencia') bool emailCorrespondencia, @Query('excluir') bool excluir,
      @Query('matricula') String matricula, @Query('origemSistema') num origemSistema);

  @UseAppCrypto()
  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/v2/bQ3jI3pM1zT1aB7mE3gC3zO6nC3nW1pF')
  Future<String> alterarSenhaUsuario(@Body() BodyAlterarSenhaCrypto body);

  @POST('${ConfigURL.APIZW}/prest/cliente/{chave}/alterarEndereco')
  Future<String> alterarEnderecoUsuario(
      @Query('codigo') num codigoEndereco,
      @Query('tipo') String tipo,
      @Query('numero') String numero,
      @Query('cep') String cep,
      @Query('excluir') bool excluir,
      @Query('enderecoCorrespondencia') bool enderecoCorrespondencia,
      @Query('complemento') String complemento,
      @Query('bairro') String bairro,
      @Query('endereco') String endereco,
      @Query('matricula') String matricula,
      @Query('origemSistema') num origemSistema);

  @POST('${ConfigURL.TREINO}/prest/notificacoes/{chave}/get')
  Future<NotificacaoCRM> consultarNotificacoes(@Query('username') String username, [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.TREINO}/prest/notificacoes/{chave}/respostaNotificacao')
  Future<String> responderNotificacao(@Query('idNotf') num idNotificacao, @Query('reposta') String resposta);

  @POST('${ConfigURL.TREINO}/prest/notificacoes/{chave}/marcarLida')
  Future<String> marcarNotificacaoCrmLida(@Query('username') String username, @Query('idNotificacao') num idNotificacao, @Query('marcarTodas') bool marcarTodas,
      [@Query('matricula') String? matricula]);

  @POST('${ConfigURL.OAMD}/prest/token/aae06a9469a47e5b58da769ec6041af0/gerar?tipoChavePrimaria=0&tipoEnvioToken=SMS&chavePrimaria=1&email=')
  Future<String> gerarTokenSMSNovo(@Query('msg') String msg, @Query('idRemetente') String nomeApp, @Query('telefone') String telefone);

  @UseAppCache()
  @KeppCacheInSession()
  @GET('${ConfigURL.PERSONAGEM}/clientes/{chave}/situacao/{matricula}')
  Future<SituacaoCliente> consultarSituacaoAluno(@Path('matricula') String matricula);

  @POST('${ConfigURL.TREINO}/prest/login/app')
  Future<String> gerarTokenAuthColaborador(@Body() Map<String, dynamic> dadosUsuario);

  @GET('${ConfigURL.TREINO}/prest/psec/validateToken')
  Future<PerfilAcesso> validarToken(@Header('empresaId') num empresaId);

  @POST('${ConfigURL.OAMD}/prest/recursoEmpresa/notificar')
  Future<String> notificarRecursoEmpresa(@Body() NotificarRecursoEmpresa recursoEmpresa);

  @GET('https://app.pactosolucoes.com.br/login/prest/session/getSuspectNets')
  Future<List<String>> consultarRedesSuspeitas();

  @GET('${ConfigURL.FIREBASE}/usuario/consultarPushs')
  Future<List<PushUsuario>> consultarPushs(@Query('chave') String chave, @Query('userNameUsuario') String userNameUsuario, @Query('codUsuario') num codUsuario);

  @POST('${ConfigURL.FIREBASE}/usuario/marcarPushComoLida')
  Future<String> marcaPushComoLida(@Body() Map<String, dynamic> body);

  @POST('${ConfigURL.TREINO}/prest/usuario/{chave}/registrar-uso-app')
  Future<RetornoUsoApp> registarUsoAppTreinoWeb(@Body() List<RegistarUsoApp> body);

  @PATCH('${ConfigURL.FIREBASE}/usuario/obterPaseDeUsoDevMove')
  Future<DevModeCodigo> obterPasseDevMode(@Body() Map<String, dynamic> email);

  @GET('${ConfigURL.ACESSOMS}/locaisAcesso/find-all')
  Future<ReponseContent<List<LocalDeAcesso>>> listarPontosDeAcesso(@Query('empresa') num empresa);

  @POST('${ConfigURL.TREINO}/prest/psec/termo-aceite/assinatura')
  Future<String> aceitarTermosAplicativo(
      {@Query('cpf') required String cpf,
      @Query('data') required num data,
      @Query('ip') required String ip,
      @Query('nome') required String nome,
      @Query('termo') required num termo,
      @Query('email') required String email,
      @Query('codigoMatricula') required String codigoMatricula});

  @GET('${ConfigURL.TREINO}/prest/psec/termo-aceite')
  Future<List<TermoDeAceite>> buscarTermosDeAceite();

  @POST('${ConfigURL.TREINO}/prest/psec/termo-aceite')
  Future<String> salvarTermosAceite(
    @Query('termo') String termo,
  );

  @GET('${ConfigURL.TREINO}/prest/psec/termo-aceite/assinaturas/{codigoMatricula}')
  Future<dynamic> consultarAssinaturaTermos(@Path('codigoMatricula') String codigoMatricula);

  @POST('${ConfigURL.URLZW}/app/prest/registraracesso')
  Future<AcessoRegistrado> registrarAcesso(
    @Query('key') String key,
    @Query('operacao') String operacao,
    @Query('empresa') num empresa,
    @Query('codigo') num codigo,
    @Query('dataAcesso') String dataAcesso,
    @Query('direcao') String direcao,
    @Query('meioIdentificacao') String meioIdentificacao,
    @Query('situacao') String situacao,
    @Query('tipo') num tipo,
    @Query('usuario') num usuario,
    @Query('app') bool app,
  );

  @UseAppCache()
  @POST('https://auth.ms.pactosolucoes.com.br/aut/login')
  Future<AutLoginMs> loginMs(@Body() Map<String, dynamic> dadosUsuario);

  @GET('${ConfigURL.TREINO}/prest/cliente/{chave}/historico-presenca')
  Future<HistoricoPresenca> obterHistoricoPresenca({
    @Query('matricula') required  int matricula,
    @Query('empresa') required num empresa,
    @Query('atualizaCache') required bool atualizaCache,
  });
  
  @POST('${ConfigURL.FIREBASE}/aluno/enviarContatoOtp')
  Future<String> enviarContatoEmailOtp(@Body() BodyContatoEmail body,
  );

  @PATCH('${ConfigURL.FIREBASE}/aluno/validarContatoOtp')
  Future<String> validarContatoEmailOtp(
    @Body()  BodyContatoEmail body,
  );  
}
