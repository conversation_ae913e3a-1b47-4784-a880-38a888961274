import 'package:app_treino/ServiceProvider/StuckTrace.dart';
import 'package:dio/dio.dart';

class DioStuckInterceptor extends Interceptor {
  DateTime? startTime;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Armazena o tempo de início da requisição
    startTime = DateTime.now();
    return handler.next(options); //continue
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Calcula o tempo decorrido
    final elapsedTime = startTime != null ? DateTime.now().difference(startTime!).inMilliseconds : -1;

    // Registra o erro junto com o tempo decorrido
    StuckTrace().putErro(err, elapsedTime: elapsedTime);
    return handler.next(err); //continue
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    // Calcula o tempo decorrido
    final elapsedTime = startTime != null ? DateTime.now().difference(startTime!).inMilliseconds : -1;

    // Registra a resposta junto com o tempo decorrido
    StuckTrace().putResponse(response, elapsedTime: elapsedTime);
    return handler.next(response); //continue
  }
}
