import 'dart:async';
import 'dart:convert';
import 'package:app_treino/ServiceProvider/StuckTrace.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';
import 'dart:io';
import 'package:retrofit/http.dart' as retrofit;
import 'package:path/path.dart';

class SembastCacheStore {
  static final SembastCacheStore _singleton = SembastCacheStore._internal();

  factory SembastCacheStore() {
    return _singleton;
  }

  SembastCacheStore._internal();
  Database? _db;
  var store = stringMapStoreFactory.store('cache_storedio');
  final lastCleanKey = 'last_clean_timestamp'; // Chave para armazenar o timestamp da última limpeza

  // Inicializa o banco de dados do Sembast
  Future<void> init() async {
    if (_db == null) {
      final dbPath = await _getDbPath();
      final db = await databaseFactoryIo.openDatabase(dbPath);
      _db = db;
    }
  }

  // Obtém o caminho do banco de dados
  Future<String> _getDbPath() async {
    Directory appDocDirectory = await getApplicationDocumentsDirectory();
    await Directory(appDocDirectory.path + '/' + 'dir').create(recursive: true);
    var path = await getApplicationDocumentsDirectory();
    return join(path.path, 'cachedio.db');
  }

  // Armazena a resposta no cache, incluindo os headers
  Future<void> saveToCache(String key, dynamic data, Map<String, List<String>> headers) async {
    final cacheData = {
      'data': data,
      'headers': headers, // Garantimos que os headers sejam salvos como Map<String, List<String>>
    };
    var loginKeys = [
      'vA6fM2oW4uY8tS4aA4bZ6tM2uA0tV8dB',
      'fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU',
    ];
    if (loginKeys.any((l) => key.contains(l))) {
      key = 'fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU';
    }
    await store.record(key).put(_db!, cacheData);
  }

  // Recupera a resposta do cache, incluindo os headers
  Future<Map<String, dynamic>?> getFromCache(String key) async {
    bool isCacheClean = await checkAndCleanCacheIfNecessary();
    if (isCacheClean) {
      return null;
    }
    var loginKeys = [
      'vA6fM2oW4uY8tS4aA4bZ6tM2uA0tV8dB',
      'fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU',
    ];
    if (loginKeys.any((l) => key.contains(l))) {
      key = 'fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU';
    }
    final recordSnapshot = await store.record(key).get(_db!);
    // final keys = await store.findKeys(_db!);

    store.findKeys(_db!);
    if (recordSnapshot == null) return null;
    var jsonParsed = const JsonCodec().decode(const JsonCodec().encode(recordSnapshot));
    Map<String, List<String>> headers = {};
    (jsonParsed['headers'] as Map).forEach((key, value) {
      headers[key] = [];
      for (final element in (value as List)) {
        headers[key]!.add(element);
      }
    });
    return {
      'data': jsonParsed['data'],
      'headers': headers,
    };
  }

  // Limpa o cache
  Future<void> clearCache() async {
    await store.delete(_db!);
    if (kDebugMode) {
      print('Cache limpo!');
    }
  }

  // Verifica a data da última limpeza e limpa se já passou uma semana
  Future<bool> checkAndCleanCacheIfNecessary({bool forceClean = false}) async {
    if (_db == null) {
      return false;
    }
    final currentTime = DateTime.now();

    final lastCleanTimestamp = (await store.record(lastCleanKey).get(_db!) as Map<String, dynamic>?)?['date'] as int?;

    if (lastCleanTimestamp == null && !forceClean) {
      // Se não houver uma data anterior, salvar a data atual
      await store.record(lastCleanKey).put(_db!, {'date': currentTime.millisecondsSinceEpoch});
      return false;
    }

    final lastCleanDate = DateTime.fromMillisecondsSinceEpoch(lastCleanTimestamp ?? 0);
    final differenceInDays = currentTime.difference(lastCleanDate).inDays;

    if (differenceInDays >= 7 || forceClean) {
      // Se já passou mais de uma semana, limpar o cache
      await clearCache();
      // Atualizar a data da última limpeza
      await store.record(lastCleanKey).put(_db!, {'date': currentTime.millisecondsSinceEpoch});
      return true;
    } else {
      return false;
    }
  }
}

class DioCacheInterceptor extends Interceptor {
  final SembastCacheStore cacheStore = SembastCacheStore();
  final ConnectionQuality connectionQuality = ConnectionQuality();
  int timeoutDuration = 3000; // Tempo limite em milissegundos (3 segundos)
  // Armazena um temporizador único para cada requisição
  final Map<String, Timer?> _timers = {};
  final Map<String, bool> _requestCompleted = {}; // Para rastrear se a requisição já foi completada

  DioCacheInterceptor() {
    cacheStore.init(); // Inicializa o banco de dados no construtor
  }

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Verifica a qualidade da conexão antes de continuar
    Map<String, String> connectionData = await connectionQuality.checkConnectionQuality();
    final shouldCache = UseAppCache.containsHeader(options); //options.headers.containsKey('cacheinapp');
    if (['Ruim', 'Muito Ruim', 'Sem conexão'].contains(connectionData['qualidade'])) {
      if (connectionData['qualidade'] == 'Sem conexão') {
        options.connectTimeout = const Duration(milliseconds: 1000);
        timeoutDuration = 1000;
      } else {
        timeoutDuration = 4500;
      }
      // Inicia o temporizador se o header "limiteTimeout" estiver presente
      if (shouldCache) {
        _startTimeoutForRequest(options, handler);
        return;
      }
    } else {
      // Se a internet estiver em boas condições, verifica se o cache precisa ser limpo
      if (shouldCache) {
        await cacheStore.checkAndCleanCacheIfNecessary();
      }
    }

    return handler.next(options); // Continue a requisição normalmente
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    _completeRequest(response.requestOptions);
    // Verifica se o header `limiteTimeout` está presente e se o valor é `true`
    if (UseAppCache.containsHeader(response.requestOptions)) {
      // Verifica o tipo de dados da resposta antes de salvar no cache
      if (response.data is Map<String, dynamic> || response.data is String || response.data is bool || response.data is num) {
        await cacheStore.saveToCache(response.requestOptions.uri.toString(), response.data, response.headers.map); // headers.map é um Map<String, List<String>>
      }
    }
    return handler.next(response); // Continue com a resposta normalmente
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    _completeRequest(err.requestOptions);
    // Em caso de erro, tenta retornar uma resposta do cache, se disponível
    final cachedData = await cacheStore.getFromCache(err.requestOptions.uri.toString());
    try {
      if (UseAppCache.containsHeader(err.requestOptions) &&
          cachedData != null &&
          ([503, 502, 501, 401].contains(err.response?.statusCode) || err.toString().contains('Failed host lookup'))) {
        return handler.resolve(Response(
          requestOptions: err.requestOptions,
          data: cachedData['data'],
          headers: Headers.fromMap(cachedData['headers']), // Reconstrói os headers
          statusCode: 200,
        ));
      }
    } catch (e) {}
    return handler.next(err); // Continue com o erro normalmente
  }

  // Método para iniciar o timeout de 3 segundos para a requisição específica
  void _startTimeoutForRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final requestKey = options.uri.toString();
    _requestCompleted[requestKey] = false; // Inicialmente, a requisição não foi completada
    // Inicia o temporizador e armazena-o no mapa usando a URI como chave
    _timers[requestKey] = Timer(Duration(milliseconds: timeoutDuration), () async {
      if (_requestCompleted[requestKey] == true) return; // Se já foi completada, não faz n
      // Após 3 segundos, tenta buscar do cache
      final cachedData = await cacheStore.getFromCache(options.uri.toString());
      if (cachedData != null) {
        // Retorna a resposta do cache se disponível
        handler.resolve(Response(
          requestOptions: options,
          data: cachedData['data'],
          headers: Headers.fromMap(cachedData['headers']),
          statusCode: 200,
        ));
      } else {
        handler.next(options);
      }
      _completeRequest(options); // Marca como completa para evitar conflitos
    });
  }

  // Método para cancelar o timeout da requisição específica e marcar como completada
  void _completeRequest(RequestOptions options) {
    final requestKey = options.uri.toString();
    if (_requestCompleted[requestKey] == true) return; // Evita completar a requisição mais de uma vez

    _requestCompleted[requestKey] = true; // Marca como concluída
    _timers[requestKey]?.cancel(); // Cancela o temporizador se ainda estiver ativo
    _timers.remove(requestKey); // Remove o temporizador do mapa
    _requestCompleted.remove(requestKey); // Limpa o status da requisição
  }
}

class UseAppCrypto extends retrofit.Headers {
  static const Map<String, dynamic> headers = {'encrypted': 'true'};
  static bool containsHeader(RequestOptions request) {
    return request.headers.containsKey(headers.keys.first);
  }
  // exemplo de uso UseAppCrypto.containsHeader(request)

  const UseAppCrypto() : super(headers);
}

class UseAppCache extends retrofit.Headers {
  static const Map<String, dynamic> headers = {'limiteTimeout': 'true'};
  static bool containsHeader(RequestOptions request) {
    return request.headers.containsKey(headers.keys.first);
  }
  // exemplo de uso UseAppCache.containsHeader(request)

  const UseAppCache() : super(headers);
}
