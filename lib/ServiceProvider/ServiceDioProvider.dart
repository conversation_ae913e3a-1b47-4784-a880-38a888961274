import 'dart:convert';
import 'dart:developer';
import 'package:app_treino/ServiceProvider/ApiCache.dart';
import 'package:app_treino/ServiceProvider/ConexaoFracaInterceptor.dart';
import 'package:app_treino/ServiceProvider/DioCurl.dart';
import 'package:app_treino/ServiceProvider/KeepInSessionInterceptor.dart';
import 'package:app_treino/ServiceProvider/LoopDetectionInterceptor.dart';
// ignore: unused_import
import 'package:app_treino/ServiceProvider/MonitoramentoPerformace.dart';
import 'package:app_treino/ServiceProvider/RetryLaterDio.dart';
import 'package:app_treino/ServiceProvider/StuckInterceptor.dart';
import 'package:app_treino/ServiceProvider/StuckTrace.dart';
import 'package:app_treino/ServiceProvider/TokenDioInterceptor.dart';
import 'package:app_treino/ServiceProvider/UrlsSerice.dart';
import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorAppLoading.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoIA.dart';
import 'package:app_treino/controlladores/ControladorSplash.dart';
import 'package:app_treino/model/UserDataKeys.dart';
import 'package:app_treino/model/aulaTurma/AulaTurma.dart';
import 'package:app_treino/model/util/UtilCrypt.dart';
import 'package:dart_ipify/dart_ipify.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

Dio dioToken = Dio();

class ServiceDioProvider {
  final dio = Dio();
  final sessionInterceptor = KeppSessionCacheInterceptor();
  final urlDescover = ConfigURL();
  String? tokenUsuarioMS;
  static final ServiceDioProvider _instance = ServiceDioProvider._internal();

  factory ServiceDioProvider() {
    return _instance;
  }

  ServiceDioProvider._internal();

  /// Obtem o token de autenticação do usuário.
  ///
  /// Verifica se o usuário é colaborador ou não e busca o token correspondente nas preferências compartilhadas.
  Future<String> getToken({RequestOptions? request}) async {
    if (request?.uri.toString().contains('prest/psec/') ?? false) {
      return GetIt.I.get<ControladorSplash>().currentTokenPsec;
    }
    return GetIt.I.get<ControladorSplash>().currentToken;
  }

  Future<void> getTokenUsuarioMS() async {
    return await SharedPreferences.getInstance().then((db) {
      tokenUsuarioMS = db.getString(UserDataKeys.TOKENUSUARIOMS.toString()) ?? '';
      if ((tokenUsuarioMS ?? '').split(' ').length == 2) {
        tokenUsuarioMS = (tokenUsuarioMS ?? '').split(' ')[1];
      }
    });
  }

  /// Retorna a instância do Dio configurada.
  ///
  /// Adiciona interceptadores para configurar a requisição, tratar a resposta e lidar com erros.
  /// Em modo de debug, adiciona um interceptor para registrar a requisição cURL.
  Dio getDio() {
    // dio.interceptors.add(MonitoramentoPerformace());
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // options.connectTimeout = 30000; // 30 segundos
        return _configureRequest(options).then((value) => handler.next(value));
        // options = await _configureRequest(options);
        // return handler.next(options);
      },
      onResponse: (response, handler) {
        //print("URL CONSULTA ${response.realUri.toString()}");UserDataKeys
        response = handleResponse(response);
        return handler.next(response);
      },
      onError: (e, handler) {
        _handleError(e, handler);
      },
    ));
    dio.interceptors.add(ApiCacheSembastInterceptor());
    dio.interceptors.add(RetryLaterDio());
    dio.interceptors.add(LoopDetectionInterceptor());
    dio.interceptors.add(UrlDioInterceptor(dioToken, rejectHandler: _handleError, responseHandler: (original, resonseDone) {
      return resonseDone(handleResponse(original));
    }));
    dio.interceptors.add(TokenDioInterceptor(dioToken, rejectHandler: _handleError, responseHandler: (original, resonseDone) {
      return resonseDone(handleResponse(original));
    }));
    dio.interceptors.add(DioCacheInterceptor());
    if (!kReleaseMode) {
      dio.interceptors.add(DioCurl(printOnSuccess: true));
    }
    dio.interceptors.add(DioStuckInterceptor());
    dio.interceptors.add(sessionInterceptor);
    // }
    return dio;
  }

  /// Configura a requisição Dio antes do envio.
  ///
  /// Define a chave, o token, o tipo de consulta e a base URL.
  /// Criptografa a URL e os parâmetros da requisição se a URL contém '{FIREBASE}'.
  /// Define o token e a base URL específicos para o Vitio se a URL contém '{VITIO}'.
  Future<RequestOptions> _configureRequest(RequestOptions options) async {
    if (GetIt.I.get<ControladorApp>().chave != null) {
      options.path = options.path.replaceAll('{chave}', GetIt.I.get<ControladorApp>().chave!);
    }

    try {
      options = UtilCrypt().encryptPath(options);
    } catch (e) {}
    await getToken(request: options);
    await getTokenUsuarioMS();
    if (options.path.contains('app/solicitarEmail') || options.path.contains('app/validarCodiogEmail')) {
      options.headers.addAll({'Authorization': 'flowApp'});
    } else if (options.path.contains('prest/psec/fichas/obterFichaPredefinida')) {
      options.headers.addAll({'Authorization': GetIt.I.get<ControladorPrescricaoIA>().tokenIA});
    } else if (!options.path.contains('/find?key=') && options.headers['Authorization'] == null) {
      options.headers.addAll({'Authorization': await getToken(request: options)});
    }
    if (options.path.contains('prest/psec/aulas/removerDaFilaV2') || options.path.contains('/v2/removerDaFila') || (options.path.contains('/equipamento/') && options.method == 'DELETE')) {
      options.headers.remove('content-length');
      options.data = null;
    } else if (options.path.contains('{ADMCORE}')) {
      options.headers.addAll({'Authorization': GetIt.I.get<ControladorSplash>().currentTokenPsec});
    }

    // UtilCrypt

    var urlSistema = await urlDescover.url(options.path.split('/').first);
    if (urlSistema != null) {
      options.path = options.path.replaceAll(options.path.split('/').first, '');
      options.baseUrl = urlSistema;
    }
    //User-Agent: <product> / <product-version> <comment>
    if (!options.path.contains('avaliacoes-fisica/alunos')) {
      options.headers.addAll({
        'User-Agent': await GetIt.I.get<ControladorApp>().gerarHeaderDeOrigin(),
        'fbchave': GetIt.I.get<ControladorApp>().chave,
        'fbcodEmpresa': GetIt.I.get<ControladorCliente>().mUsuarioLogado?.codEmpresa ??
            GetIt.I.get<ControladorAppLoading>().mLoadedUsuario?.codEmpresa ??
            GetIt.I.get<ControladorAppLoading>().codEmpresaRelogin ??
            '',
      });
    }

    options.path = options.path.replaceAll('{tokenApp}', await getToken(request: options));
    options.path = options.path.replaceAll('{APPTIPOCONSULTA}', GetIt.I.get<ControladorApp>().mClienteAppSelecionado?.tipoConsultaPremium ?? 'PACTO');
    if (options.method == 'GET') {
      options.headers.remove('content-length');
      options.data = null;
    }
    StuckTrace().putRequest(options);
    return options;
  }

  /// Trata erros do Dio.
  ///
  /// Lida com erros de autorização (401) e outros erros, retransmitindo-os para o interceptor.
  void _handleError(DioException e, ErrorInterceptorHandler handler) async {
    handleNoConnectionError(e, handler);
    if (e.response?.statusCode != 401) {
      try {
        if (e.message.toString().contains('SocketException: Failed host lookup:')) {
          handler.next(DioException(
            error: 'Sem conexão com a internet',
            requestOptions: e.requestOptions,
          ));
          return;
        } else if (e.response != null) {
          try {
            var json = const JsonCodec().decode(e.response!.data);
            if (json['erro'] != null) {
              handler.next(DioException(
                error: json['erro'],
                message: json['erro'],
                requestOptions: e.requestOptions,
              ));
              return;
            }
            if (json['meta']?['message'] != null) {
              final errorMessage = json['meta']['message'];
              handler.next(DioException(
                error: errorMessage,
                requestOptions: e.requestOptions,
              ));
              handler.next(e);
              return;
            }
          } catch (e) {}
          if (e.response!.data['meta']?['message'] != null) {
            handler.next(DioException(
              error: e.response!.data['meta']['message'],
              requestOptions: e.requestOptions,
            ));
            return;
          } else {
            handler.next(e);
          }
        } else {
          handler.next(e);
          return;
        }
      } catch (ex) {
        handler.next(e);
      }
    } else {
      if (!handler.isCompleted) {
        handler.next(e);
      }
    }
  }
}

List<String> _ipsEmAnalise = [];

handleNoConnectionError(DioException e, ErrorInterceptorHandler handler) async {
  try {
    String currentIp = await Ipify.ipv4();
    if (e.toString().contains('Connecting timed out') && !_ipsEmAnalise.contains(currentIp)) {
      _ipsEmAnalise.add(currentIp);
      GetIt.I.get<ControladorAppLoading>().verificarSeIpEstaBloqueado();
    }
  } catch (e) {}
}

/// Trata a resposta do Dio.
///
/// Decriptografa a resposta se o cabeçalho indica que a resposta está segura.
/// Decodifica dados JSON e trata erros específicos de acordo com o caminho da URL.
Response<dynamic> handleResponse(Response response) {
  // ignore: unused_local_variable
  bool hasThrow = false;
  final path = response.requestOptions.path;
  if (response.requestOptions.uri.toString().contains('prest/operacoescontratoservlet')) {
    return response;
  }
  if (response.requestOptions.uri.toString().contains('vitio-production-aej2ba9c')) {
    return response;
  }
  if (path.contains('/atual')) {
    log(path);
  }

  if (path.contains('ipify.org')) {
    return response;
  }
  // Decrypt if header indicates data is safe
  try {
    if (path.contains('/debit_credit')) {
      return response;
    }
    if (path.contains('/validarProdutoBioTotem') || path.contains('/validarProdutoConsultaNutricional')) {
      response.data = response.data['retorno'];
      return response;
    }
    if (response.requestOptions.headers.keys.any((element) => ['axpxpt', 'axpxp'].contains(element))) {
      Map<String, dynamic> dataParse = {};
      if (response.data is String) {
        if (response.data?.toString().contains('"return":') ?? false) {
          response.data = const JsonCodec().decode(response.data)['return'];
        }
        var decrypted = UtilCrypt().decrypt(response.data, type: response.requestOptions.headers.containsKey('axpxp') ? CryptType.FB : CryptType.OTHER);
        try {
          response.data = const JsonCodec().decode(decrypted);
        } catch (e) {
          response.data = decrypted;
        }
      } else {
        (response.data as Map<String, dynamic>).forEach((key, value) {
          var decrypted = UtilCrypt().decrypt(value, type: response.requestOptions.headers.containsKey('axpxp') ? CryptType.FB : CryptType.OTHER);
          try {
            dataParse[key] = const JsonCodec().decode(decrypted);
          } catch (e) {
            dataParse[key] = decrypted;
          }
        });
        response.data = dataParse;
      }
    }
  } catch (e) {}

  if (path.contains('/rO2aS2tZ5jJ2aM8hE3dZ3pH1jY4lG1mM')) {
    return response;
  }

  if (path.contains('registrar-uso-app') || path.contains('app-treino6/json')) {
    return response;
  }

  if (['kC8zT9lL2xH6qG9wB0mP9vR1mM2bA7aE', 'rB3mU7oX8eS4eA6iC4lH9eY2fU6sF5kR'].any((e) => path.contains(e))) {
    try {
      response.data = const JsonCodec().encode(response.data);
      return response;
    } catch (e) {}
  }

  if (response.data is String) {
    response.data = const JsonCodec().decode(response.data);
  }

  if (path.contains('/alunovendaapp') && response.data['return']?.contains('ERRO') == true) {
    hasThrow = true;
    throw DioException(
      error: response.data['return'].replaceAll('ERRO: ', ''),
      type: DioExceptionType.badResponse,
      response: response,
      requestOptions: response.requestOptions,
    );
  }

  if ([
    'MyNutriAPI',
    'goals',
    'availability_times',
    'us-central1-nutriconnect',
    '/messages',
    '/bioimpedances',
    '/cobrarParcelasAbertasPix/',
    'aut/gt/',
    '/gerarUsuarioTR',
    '/consultarGruposTrabalhadosPeriodo',
    '/operacoescontratoservlet'
  ].any((segment) => path.contains(segment) || response.realUri.toString().contains(segment))) {
    return response;
  }

  if (path.contains('empresa/dadosRede?chave') && response.data['empresas'] != null) {
    response.data = response.data['empresas'];
    return response;
  }

  if (response.data != null && ((response.data is Map) && response.data['erro'] != null || ((response.data as Map).entries.length == 1 && (response.data as Map).keys.first == 'erro'))) {
    hasThrow = true;
    throw DioException(
      error: UtilCrypt().decrypt(response.data['erro'] ?? 'erro desconhecido', type: response.requestOptions.baseUrl.contains('web.app') ? CryptType.FB : CryptType.OTHER),
      type: DioExceptionType.badResponse,
      message: UtilCrypt().decrypt(response.data['erro'] ?? 'erro desconhecido', type: response.requestOptions.baseUrl.contains('web.app') ? CryptType.FB : CryptType.OTHER),
      response: response,
      requestOptions: response.requestOptions,
    );
  }
  if (path.contains('/marcarAula') && response.data['sucesso']?.contains(';') == true) {
    response.data = MarcacaoAulaTurma(
      confirmado: true,
      nrAulasExperimentais: int.parse(response.data['sucesso'].split(';')[0]),
    ).toJson();
    return response;
  }

  if (path.contains('consultarAlunosDeUmaAula') && response.data is Map && response.data['alunos'] == null) {
    response.data = [];
    return response;
  }

  final ingorePaths = ['alunos'];
  final allowPaths = ['psec/alunos/linha-tempo', '/prest/psec/alunos/obter-aluno-completo-por-matricula/179'];
  final fieldsToReturn = [
    'status',
    'agendamentosAbertos',
    'resultEmpresasJSON',
    'content',
    'return',
    'sucesso',
    'aulas',
    'alunos',
  ];
  for (final field in fieldsToReturn) {
    if (response.data is Map && (response.data as Map).entries.length == 1) {
      if (response.data[field] != null) {
        response.data = response.data[field];
        return response;
      }
    }
    if (allowPaths.any((allow) => path.contains(allow))) {
      if (response.data[field] != null) {
        response.data = response.data[field];
        return response;
      }
    }
    if (!(response.data is List) && response.data[field] != null && !ingorePaths.any((e) => path.split('/').any((p) => p == e))) {
      response.data = response.data[field];
      return response;
    }
  }
  return response;
}
