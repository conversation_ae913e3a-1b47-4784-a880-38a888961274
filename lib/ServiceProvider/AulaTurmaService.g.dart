// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'AulaTurmaService.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations,unused_element_parameter

class _AulaTurmaService implements AulaTurmaService {
  _AulaTurmaService(this._dio, {this.baseUrl, this.errorLogger});

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<SaldoAulaColetiva> consultarSaldoAulasColetivas({
    required num matricula,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'matricula': matricula};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<SaldoAulaColetiva>(
      Options(
        method: 'GET',
        headers: _headers,
        extra: _extra,
        contentType: 'application/x-www-form-urlencoded',
      )
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/saldo-aulas-coletivas',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late SaldoAulaColetiva _value;
    try {
      _value = SaldoAulaColetiva.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AulaTurma>> consultarAulasColetivas({
    required String dia,
    required num codigoEmpresa,
    required num matricula,
    required bool aulasFuturas,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dia': dia,
      r'empresa': codigoEmpresa,
      r'matricula': matricula,
      r'aulasFuturas': aulasFuturas,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/consultarAulas',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AulaTurma> _value;
    try {
      _value = _result.data!
          .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AulaTurma>> consultarTurmasDoAluno(
    num matricula,
    String inicio,
    String fim,
    num? contrato,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'inicio': inicio,
      r'fim': fim,
      r'contrato': contrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AulaTurma>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/app/consultarTurmasDisponiveis',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AulaTurma> _value;
    try {
      _value = _result.data!
          .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AulaTurma>> consultarTurmasAgendada(
    String matricula,
    bool aulasFuturas,
    num? contrato,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'aulasFuturas': aulasFuturas,
      r'contrato': contrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/consultarAulas',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AulaTurma> _value;
    try {
      _value = _result.data!
          .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<Map<String, String>> consultarSaldoDoUsuario({
    required num matricula,
    num? idContrato,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'contrato': idContrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<Map<String, String>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/saldoAlunoReporEMarcar',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late Map<String, String> _value;
    try {
      _value = _result.data!.cast<String, String>();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<MarcacaoAulaTurma> marcarPresencaAula({
    required num codigoAula,
    num? codigoContratoMarcacao,
    required bool aulaExperimental,
    required String matricula,
    required String dia,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'contrato': codigoContratoMarcacao,
      r'aulaExperimental': aulaExperimental,
      r'matricula': matricula,
      r'dia': dia,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<MarcacaoAulaTurma>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/marcarPresencaOrigem?origem=APP_TREINO',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late MarcacaoAulaTurma _value;
    try {
      _value = MarcacaoAulaTurma.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<AulaTurmaSimplificado> marcarPresencaTodosAlunosNaAula(
    String horarioAulaId,
    num empresaId,
    num dia,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'dia': dia};
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<AulaTurmaSimplificado>(
      Options(
        method: 'PUT',
        headers: _headers,
        extra: _extra,
        contentType: 'application/x-www-form-urlencoded',
      )
          .compose(
            _dio.options,
            '{TREINO}/prest/psec/agenda/turmas/${horarioAulaId}/confirmar-todas-presencas',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late AulaTurmaSimplificado _value;
    try {
      _value = AulaTurmaSimplificado.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<MarcacaoAulaTurma> professorMarcarPresenca(
    num codigoAula,
    bool aulaExperimental,
    String matricula,
    String dia,
    num codUsuario,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'aulaExperimental': aulaExperimental,
      r'matricula': matricula,
      r'dia': dia,
      r'codUsuario': codUsuario,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<MarcacaoAulaTurma>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/marcarPresenca?origem=APP_TREINO',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late MarcacaoAulaTurma _value;
    try {
      _value = MarcacaoAulaTurma.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<MarcacaoAulaTurma> marcarPresencaTurma(
    String matricula,
    String data,
    num codigoHorarioTurma,
    num? codigoContrato,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'data': data,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'contrato': codigoContrato,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<MarcacaoAulaTurma>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/marcarAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late MarcacaoAulaTurma _value;
    try {
      _value = MarcacaoAulaTurma.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> desmarcarPresencaAula(
    num codigoHorarioTurma,
    String matricula,
    String data,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'matricula': matricula,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/desmarcarAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> desmarcarPresencaTurma(
    String matricula,
    String data,
    num codigoHorarioTurma,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'matricula': matricula,
      r'data': data,
      r'codigoHorarioTurma': codigoHorarioTurma,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/desmarcarAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> marcarEuQueroAula(
    num codigoAluno,
    num codigoHorarioTurma,
    String data,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAluno': codigoAluno,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/marcarEuQueroHorario',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> desmarcarEuQueroAula(
    num codigoAluno,
    num codigoHorarioTurma,
    String data,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAluno': codigoAluno,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/desmarcarEuQueroHorario',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AlunoNaAulaTurma>> consultarAlunosTurma(
    num codigoHorarioTurma,
    String data,
    num codigoEmpresa,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'data': data,
      r'empresa': codigoEmpresa,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AlunoNaAulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/alunoTurma/{chave}/alunosTurma',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AlunoNaAulaTurma> _value;
    try {
      _value = _result.data!
          .map(
            (dynamic i) => AlunoNaAulaTurma.fromJson(i as Map<String, dynamic>),
          )
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AlunoNaAulaTurma>> consultarAlunosColetiva(
    num codigoAula,
    String dia,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'dia': dia,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AlunoNaAulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/consultarAlunosDeUmaAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AlunoNaAulaTurma> _value;
    try {
      _value = _result.data!
          .map(
            (dynamic i) => AlunoNaAulaTurma.fromJson(i as Map<String, dynamic>),
          )
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AulaTurma>> consultarAulasTurmasProfessor(
    num empresa,
    String dia,
    num codColaborador,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'empresa': empresa,
      r'dia': dia,
      r'codColaborador': codColaborador,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/consultarAulasTurmaProfessorDia',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AulaTurma> _value;
    try {
      _value = _result.data!
          .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> confirmarAlunoAula(
    String tipoAula,
    num codigoAula,
    num cliente,
    String dia,
    num usuario,
    num matricula,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'horarioTurma': codigoAula,
      r'cliente': cliente,
      r'dia': dia,
      r'usuario': usuario,
      r'matricula': matricula,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/${tipoAula}',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<AulaTurmaSimplificado> desmarcarPresencaAulaTurma(
    String horarioAulaId,
    num empresaId,
    num dia,
    num matricula,
    String justificativa,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dia': dia,
      r'matricula': matricula,
      r'justificativa': justificativa,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<AulaTurmaSimplificado>(
      Options(
        method: 'DELETE',
        headers: _headers,
        extra: _extra,
        contentType: 'application/x-www-form-urlencoded',
      )
          .compose(
            _dio.options,
            '{TREINO}/prest/psec/agenda/turmas/${horarioAulaId}/desmarcar-aluno',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late AulaTurmaSimplificado _value;
    try {
      _value = AulaTurmaSimplificado.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<dynamic> isAlunoNaAula({
    String? urlTreino,
    required num codigoAula,
    required String chaveUnidade,
    required num matricula,
    required String dia,
    required String chaveOrigem,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'matricula': matricula,
      r'dia': dia,
      r'chaveOrigem': chaveOrigem,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<dynamic>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '${urlTreino}/prest/aula/${chaveUnidade}/confirmarAlunoEmAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch(_options);
    final _value = _result.data;
    return _value;
  }

  @override
  Future<dynamic> inserirNaFila({
    required num codigoAula,
    required String dia,
    required num codigoAluno,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoAula,
      r'dia': dia,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<dynamic>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/inserirNaFila',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch(_options);
    final _value = _result.data;
    return _value;
  }

  @override
  Future<dynamic> removerNaFila({
    required num codigoHorarioTurma,
    required String dia,
    required num codigoAluno,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'dia': dia,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<dynamic>(
      Options(method: 'DELETE', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/aula/{chave}/v2/removerDaFila',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch(_options);
    final _value = _result.data;
    return _value;
  }

  @override
  Future<dynamic> professorRemoverDaFila({
    required num codigoHorarioTurma,
    required String dia,
    required num codigoUsuario,
    required num codigoAluno,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'dia': dia,
      r'codigoUsuario': codigoUsuario,
      r'codigoAluno': codigoAluno,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<dynamic>(
      Options(method: 'DELETE', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/psec/aulas/removerDaFilaV2',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch(_options);
    final _value = _result.data;
    return _value;
  }

  @override
  Future<List<AlunoFilaDeEspera>> consultarAlunosNaFila(
    num codAula,
    String dia,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AlunoFilaDeEspera>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/psec/aulas/consultar-fila-espera/${codAula}/${dia}',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AlunoFilaDeEspera> _value;
    try {
      _value = _result.data!
          .map(
            (dynamic i) =>
                AlunoFilaDeEspera.fromJson(i as Map<String, dynamic>),
          )
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<UrlDiscorver> descobrirURLTreino({
    required String chaveUnidade,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'key': chaveUnidade};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<UrlDiscorver>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{DESCOVERY}/find',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late UrlDiscorver _value;
    try {
      _value = UrlDiscorver.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AulaTurma>> consultarAulasColetivasURLpersonalizada({
    required String urlTreino,
    required String chaveEmpresa,
    required String chaveOrigem,
    required String dia,
    required num codigoEmpresa,
    required num matricula,
    required bool aulasFuturas,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chaveOrigem': chaveOrigem,
      r'dia': dia,
      r'empresa': codigoEmpresa,
      r'matricula': matricula,
      r'aulasFuturas': aulasFuturas,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '${urlTreino}/prest/aula/${chaveEmpresa}/consultarAulas',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AulaTurma> _value;
    try {
      _value = _result.data!
          .map((dynamic i) => AulaTurma.fromJson(i as Map<String, dynamic>))
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<AlunoNaAulaTurma>> consultarAlunosColetivaURLpersonalizada({
    required String urlTreino,
    required String chaveEmpresa,
    required num codigoAula,
    required String dia,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'codigoAula': codigoAula,
      r'dia': dia,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<AlunoNaAulaTurma>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '${urlTreino}/prest/aula/${chaveEmpresa}/consultarAlunosDeUmaAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<AlunoNaAulaTurma> _value;
    try {
      _value = _result.data!
          .map(
            (dynamic i) => AlunoNaAulaTurma.fromJson(i as Map<String, dynamic>),
          )
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> desmarcarPresencaAulaURLpersonalizada({
    required String urlTreino,
    required String chaveEmpresa,
    required String chaveOrigem,
    required num codigoHorarioTurma,
    required String matricula,
    required String data,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chaveOrigem': chaveOrigem,
      r'codigoHorarioTurma': codigoHorarioTurma,
      r'matricula': matricula,
      r'data': data,
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '${urlTreino}/prest/aula/${chaveEmpresa}/desmarcarAula',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<MarcacaoAulaTurma> marcarPresencaAulaURLpersonalizada({
    required String urlTreino,
    required String chaveEmpresa,
    required String chaveAluno,
    num? codigoContratoMarcacao,
    num? codigoAula,
    required bool aulaExperimental,
    required String matricula,
    required String dia,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'chaveAluno': chaveAluno,
      r'contrato': codigoContratoMarcacao,
      r'codigoAula': codigoAula,
      r'aulaExperimental': aulaExperimental,
      r'matricula': matricula,
      r'dia': dia,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<MarcacaoAulaTurma>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '${urlTreino}/prest/aula/${chaveEmpresa}/marcarPresencaOrigem?origem=APP_TREINO',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late MarcacaoAulaTurma _value;
    try {
      _value = MarcacaoAulaTurma.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<List<HistoricoAula>> consultarHistoricoAulas(
    num matricula,
    String dataInicial,
    String dataFinal,
    num page,
    num size,
    num contrato,
    num empresaId,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'dataInicio': dataInicial,
      r'dataFim': dataFinal,
      r'page': page,
      r'size': size,
      r'contrato': contrato,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<List<HistoricoAula>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/programa/{chave}/aulasAgendadasPorAluno/${matricula}',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<List<dynamic>>(_options);
    late List<HistoricoAula> _value;
    try {
      _value = _result.data!
          .map((dynamic i) => HistoricoAula.fromJson(i as Map<String, dynamic>))
          .toList();
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> reservarEquipamento({
    required String horarioTurmaId,
    required String dia,
    required num empresaId,
    required String equipamento,
    required num usuarioId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'equipamento': equipamento,
      r'usuarioId': usuarioId,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/agendamento/{chave}/equipamento/${horarioTurmaId}/${dia}',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<String> removerReservaEquipamento({
    required String horarioTurmaId,
    required String dia,
    required num empresaId,
    required String equipamento,
    required num usuarioId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'equipamento': equipamento,
      r'usuarioId': usuarioId,
    };
    final _headers = <String, dynamic>{r'empresaId': empresaId};
    _headers.removeWhere((k, v) => v == null);
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<String>(
      Options(method: 'DELETE', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '{TREINO}/prest/agendamento/{chave}/equipamento/${horarioTurmaId}/${dia}',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<String>(_options);
    late String _value;
    try {
      _value = _result.data!;
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
