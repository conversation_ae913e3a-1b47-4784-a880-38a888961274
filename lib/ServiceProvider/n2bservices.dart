import 'package:app_treino/config/ConfigURL.dart';
import 'package:app_treino/screens/vitio/models/vitioModels.dart';
import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';
part 'n2bservices.g.dart';

@RestApi(baseUrl: '')
abstract class N2BService {
  factory N2BService(Dio dio, {String? baseUrl}) = _N2BService;

  @POST('http://vitio-api.mynutri.com.br/api/patients/{id}/debit_credit')
  Future<String> get_debit_credit(@Path('id') String id, @Body() RetornoSaldoDebito body, @Header('Authorization') String? token);

  @Headers(<String, dynamic>{'Content-Type': 'application/json', 'Accept': 'application/json'})
  @POST('http://vitio-api.mynutri.com.br/api/patients/{id}/add_credit')
  Future add_credit_n2b(@Path('id') String id, @Body() RetornoSaldoDebito body, @Header('Authorization') String? token);

  @GET('${ConfigURL.URLZW}/app/prest/validarProdutoBioTotem')
  Future<RetornoSaldoProdutoBioTotem> validar_produto_bio_totem(@Query('chave') String chave, @Query('empresa') num empresa, @Query('codCliente') num codCliente);

  @GET('http://vitio-api.mynutri.com.br/api/patients/bioimpedances')
  Future<List<AvaliacaoBioimpedancia>> get_bioimpedances_bytoken({@Header('Authorization') String? token});

  @POST('http://vitio-api.mynutri.com.br/api/patients')
  Future patients(@Body() Map<String, dynamic> body, @Header('Authorization') String token);

  @GET('http://vitio-api.mynutri.com.br/api/patients/find')
  Future<List<AuthResponseN2bPatients>> findPatientsByEmailOrCpf(
    @Query('login') String? emailorCpf,
    @Header('Authorization') String token,
  );

  @GET('http://vitio-api.mynutri.com.br/api/patients')
  Future<List<PatientN2B>> getPatientDetails(
    @Header('Authorization') String token,
  );
}
