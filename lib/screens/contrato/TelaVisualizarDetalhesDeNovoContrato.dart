
import 'package:app_treino/Utilitario.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorVendaDePlano.dart';
import 'package:app_treino/model/vendaplanos/PlanoVendidoUnidade.dart';
import 'package:ds_pacto/ds_barraNavegacao.dart';
import 'package:ds_pacto/ds_botaoCircular.dart';
import 'package:ds_pacto/ds_botaoPadrao.dart';
import 'package:ds_pacto/ds_card.dart';
import 'package:ds_pacto/ds_card_expansivel.dart';
import 'package:ds_pacto/ds_fonts.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class TelaVisualizarDetalhesDeNovoContrato extends StatefulWidget {
  TelaVisualizarDetalhesDeNovoContrato({Key? key}) : super(key: key);

  @override
  _TelaVisualizarDetalhesDeNovoContratoState createState() => _TelaVisualizarDetalhesDeNovoContratoState();
}

class _TelaVisualizarDetalhesDeNovoContratoState extends State<TelaVisualizarDetalhesDeNovoContrato> {
  late PlanoVendidoUnidade mPlano;
  final _mControladorVendasDePlano = GetIt.I.get<ControladorVendaDePlano>();
  final _mControladorCliente = GetIt.I.get<ControladorCliente>();
  ExpandableController controllerExpandable = new ExpandableController(initialExpanded: true);

  @override
  void initState() {
    _mControladorCliente.consultarDadosDoUsuario(force: true, carregando: (){}, falha: (error){}, sucesso: (){});
    _mControladorVendasDePlano.consultarPlanosDaUnidade(force: true, carregando: (){}, falha: (error){}, sucesso: (){});
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    mPlano = ModalRoute.of(context)!.settings.arguments as PlanoVendidoUnidade;
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            DSbotaoPadrao(
              onTap: () {
                if(GetIt.I.get<ControladorCliente>().mDadosDoUsuario != null && GetIt.I.get<ControladorCliente>().mDadosDoUsuario?.cpf != null){
                  if(_mControladorCliente.mDadosDoUsuario!.cpf!.isNotEmpty){
                    Navigator.pushNamed(context, '/formDadosDeCartao', arguments: mPlano);
                  } else{
                    UtilitarioApp().showDialogComImagemECallBack(
                      context: context,
                      vaiTerIconeFechar: true,
                      tipoAlerta: TipoAlerta.alerta,
                      tituloMensagem: localizedString('cpf_nao_cadastrado'),
                      subtituloMensagem:  localizedString('subtitulo_cpf_cadastrado')
                    );
                  }
                }
              },
              titulo: localizedString('pasta_contrato.escolher_esse_plano'),
            ),
          ],
        ),
      ),
      appBar: DSappBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        titulo: localizedString('detalhes_do_plano'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Wrap(
            runSpacing: 16,
            children: [
              DScard(
                gradiente: true,
                vaiTerCirculo: true,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(48, 16, 16, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DStextHeadline(UtilitarioApp.sentenseCase('${mPlano.nome}'), eHeavy: true, textoSobFundoGradiente: true),
                      DStextBody('${mPlano.fidelidade} ${mPlano.fidelidade !<= 1 ? localizedString('mes_duracao') : localizedString('meses_duracao')}', eHeavy: true, textoSobFundoGradiente: true)
                    ],
                  ),
                )
              ),
              DScard(
                paddingInterno: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const DSbotaoCircular(
                          altura: 30,
                          icone: TreinoIcon.moneybag_alt,
                          categoria: Categoria.secundario),
                        const SizedBox(width: 10),
                        DStextSubheadline('valores', eHeavy: true,),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Container(
                          width: (MediaQuery.of(context).size.width / 2) - 36,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark
                              ? const Color(0xff000000)
                              : const Color(0xffF0F0F0)
                          ),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 20, 10, 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    DStextCaption1('R\$', eHeavy: false),
                                    DStextDisplay3('${mPlano.mensalidade.toString().split('.').first.length <= 2 ? mPlano.mensalidade.toString().replaceAll('.', ',').substring(0,2) : mPlano.mensalidade.toString().replaceAll('.', ',').substring(0,3)}', eHeavy: true,),
                                    DStextCaption1('${mPlano.mensalidade.toString().split('.').first.length <= 2 ? mPlano.mensalidade.toString().replaceAll('.', ',').substring(2) : mPlano.mensalidade.toString().replaceAll('.', ',').substring(3)}${mPlano.mensalidade.toString().replaceAll('.', ',').substring(3).length == 1 ?  '0' : ''}', eHeavy: false,)
                                  ],
                                ),
                                DStextCaption1('valor_mensal', eHeavy: false,)
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: (MediaQuery.of(context).size.width / 2) - 36,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark
                              ? const Color(0xff000000)
                              : const Color(0xffF0F0F0)
                          ),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(16, 20, 16, 20),
                            child: Column(
                              children: [
                                campoTextoDetalhes(localizedString('adesao'),  'R\$ ${mPlano.adesao.toString()}'),
                                const SizedBox(height: 8),
                                campoTextoDetalhes(localizedString('anuidade'), 'R\$ ${mPlano.anuidade.toString()}'),
                                const SizedBox(height: 8),
                                campoTextoDetalhes(localizedString('pasta_contrato.valor_total'), 'R\$ ${(mPlano.mensalidade! * mPlano.fidelidade!.toInt()).toStringAsFixed(2).replaceAll('.', ',')}')
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: MediaQuery.of(context).size.width - 64,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        color: GetIt.I.get<ControladorApp>().themeMode == ThemeMode.dark
                          ? const Color(0xff000000)
                          : const Color(0xffF0F0F0)
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DStextSubheadline('tipo_cobranca'),
                            const SizedBox(height: 4),
                            DStextCaption1('${mPlano.regimeRecorrencia == true ? localizedString('recorrencia'): localizedString('normal')}'),
                            const SizedBox(height: 4),
                            DStextCaption1(mPlano.regimeRecorrencia == true 
                            ? localizedString('sobre_recorrencia')
                            : localizedString('sobre_cobranca_normal')
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              ),
              DSCardExpansivel(context, null,
                titulo: localizedString('modalidades_inclusas'), 
                body: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      modalidadesExpandidas()
                    ],
                  ),
                ), 
                expandableController: controllerExpandable),

              mPlano.qtdCreditoPlanoCredito == 0 /* || mPlano.qtdCreditoPlanoCredito == null */ ? Container() :
              DScard(
                paddingInterno: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const DSbotaoCircular(
                          altura: 30,
                          icone: TreinoIcon.ticket,
                          categoria: Categoria.secundario),
                        const SizedBox(width: 10),
                        DStextSubheadline(localizedString('credito_contrato') + ' ${mPlano.qtdCreditoPlanoCredito?.toString() ?? '0'}', eHeavy: true,),
                      ],
                    ),
                    const SizedBox(height: 8),
                    DStextCaption1(localizedString('sobre_credito') + ' ${mPlano.qtdCreditoPlanoCredito?.toString() ?? '0'} ${mPlano.qtdCreditoPlanoCredito !<= 1 ? localizedString('tela_detalhes_contrato.credito') : localizedString('creditos')}. ' + localizedString('subtitulo_credito'),
                    eHeavy: false,)
                  ],
                ))
            ],
          ),
        ),
      )
    );
  }

  Widget campoTextoDetalhes(String campo, String valor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: DStextCaption1(
            campo,
            eHeavy: true
          ),
        ),
        DStextCaption1(valor, eHeavy: false),
      ],
    );
  }

  Widget modalidadesExpandidas(){
    return SizedBox(
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemBuilder: (_, index){
          return Row(
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Theme.of(context).primaryColor
                ),
              ),
              const SizedBox(width: 12),
              DStextBody(camelCase(mPlano.modalidades![index].toString()), eHeavy: false,)
            ],
          );
        }, 
        separatorBuilder: (_, index) => const Divider(), 
        itemCount: mPlano.modalidades!.length),
    );
  }
}
