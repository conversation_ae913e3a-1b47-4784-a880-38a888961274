import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/componets/ItemUsuarioQueJaLogou.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/flavors.dart';
import 'package:app_treino/model/doClienteApp/UsuarioKeep.dart';
import 'package:app_treino/screens/novologin/StateFulLogin.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class TelaVisualizarUsuariosRecentes extends StateFulLogin {
  const TelaVisualizarUsuariosRecentes({super.key});

  @override
  State<TelaVisualizarUsuariosRecentes> createState() => _TelaVisualizarUsuariosRecentesState();
}

class _TelaVisualizarUsuariosRecentesState extends State<TelaVisualizarUsuariosRecentes> {
  final _controlladorCliente = GetIt.I.get<ControladorCliente>();
  List<UsuarioKeep> listaFiltrada = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>? ?? {};
    bool removerUsuarioAtual = args['removerUsuarioAtual'] ?? false;
    carregarUsuarios(removerUsuarioAtual);
  }

  void carregarUsuarios(bool removerUsuarioAtual) {
    listaFiltrada.clear();
    listaFiltrada = List.from(_controlladorCliente.mUsuariosSalvos);
    if (removerUsuarioAtual && _controlladorCliente.mUsuarioLogado != null) {
      if (_controlladorCliente.mUsuarioAuth?.uid != null) {
        listaFiltrada.removeWhere(
          (user) => user.firebaseUid == _controlladorCliente.mUsuarioAuth!.uid!,
        );
      }
    }
    setState(() {});
  }

  List<Widget> get mUltimosUsuarios {
    return listaFiltrada.map((element) => celulaUsuarioRecente(element)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      // ignore: prefer_const_constructors
      appBar: DSappBar(
        titulo: 'usuario_recente',
      ),
      body: SafeArea(
        child: ListView.separated(
            padding: const EdgeInsets.all(16),
            itemBuilder: (_, index) {
              return mUltimosUsuarios[index];
            },
            separatorBuilder: (_, __) => const Divider(),
            itemCount: mUltimosUsuarios.length),
      ),
    );
  }

  Widget celulaUsuarioRecente(UsuarioKeep user) {
    return ItemUsuarioQueJaLogou(
      dadosDoLogin: user,
      onTap: () {
        analytic(EventosKey.bem_vindo_entrou_com_usuario_logado);
        analytic(EventosKey.lgn_recent_user);
        if (user.isDependente == true) {
          UtilitarioApp().showDialogCarregando(context);
          _controlladorCliente.logarUsuarioPorSenha(
              codigoUsuarioTreino: user.codigoUsuario, 
              carregando: () {},
              falha: (mensagem) {
                Navigator.pop(context);
                DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: mensagem ?? 'Erro ao fazer login', tituloBotao: 'got_it');
              },
              sucesso: () {
                GetIt.I.get<ControladorApp>().prepararTelasBaseadoNasConfiguracoes(
                    context: context,
                    eColaborador: false,
                    tipoApp: F.appFlavor!);
                Navigator.pop(context);
                Navigator.of(context).pushNamedAndRemoveUntil('/homePageApp', (Route<dynamic> route) => false);
              });
        } else {
          widget.logarUsuarioRecente(user, false);
        }
      },
      apagouUsuario: () {
        setState(() {});
      });
  }
}
