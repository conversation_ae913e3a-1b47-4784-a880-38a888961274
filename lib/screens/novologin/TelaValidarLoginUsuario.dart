import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:app_treino/screens/novologin/StateFulLogin.dart';
import 'package:app_treino/screens/novologin/widgets/InputSenhaWidget.dart';
import 'package:app_treino/screens/novologin/widgets/SelecaoLinkSenhaWidget.dart';
import 'package:app_treino/screens/novologin/widgets/SucessoEnvioLinkWidget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:ds_pacto/ds_pacto.dart'; 
import 'package:flutter/material.dart';

class TelaValidarLoginUsuario extends StateFulLogin {
  const TelaValidarLoginUsuario({super.key});

  @override
  State<TelaValidarLoginUsuario> createState() => _TelaValidarLoginUsuarioState();
}

class _TelaValidarLoginUsuarioState extends State<TelaValidarLoginUsuario> with AfterLayoutMixin<TelaValidarLoginUsuario> {
  bool loadAfter = false;
  late TelaValidarLoginUsuarioViewType currentView;
  Widget get exibirView {
    switch (currentView) {
      case TelaValidarLoginUsuarioViewType.opcoes:
        return const SelecaoLinkSenha();
      case TelaValidarLoginUsuarioViewType.senha:
        return const InputSenhaWidget();
      case TelaValidarLoginUsuarioViewType.envioLink:
        return const SucessoEnvioLinkWidget();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      // ignore: prefer_const_constructors
      appBar: DSappBar(
        titulo: 'novoLogin_localizacao_24',
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(children: [
            Hero(
              tag: 'novoLogin_localizacao_25',
              child: DScard(
                  paddingInterno: const EdgeInsets.all(16),
                  child: Material(
                    type: MaterialType.transparency,
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: CircleAvatar(
                            radius: 16,
                            backgroundImage: CachedNetworkImageProvider(widget.mUsuarioLogar.srcImg ?? ''),
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: MediaQuery.of(context).size.width - 116,
                              child: DStextSubheadline(camelCase('${widget.mControladorLogin.mUsuario.nome ?? ''}'), maximoLinhas: 1, overflow: TextOverflow.ellipsis,)),
                            DStextCaption1(
                              ('${widget.mControladorLogin.mUsuario.email ?? 'sem_email_cadastrado'}').toLowerCase(),
                              eHeavy: false,
                            ),
                            DStextCaption1(
                              (widget.mControladorLogin.mUsuario.perfilUsuario?.isNotEmpty ?? false) ? localizedString('collaborator') : localizedString('member'),
                              maximoLinhas: 1,
                              overflow: TextOverflow.ellipsis,
                              eHeavy: false,
                              ePrimario: false,
                            ),
                          ],
                        )
                      ],
                    ),
                  )),
            ),
            if (loadAfter) exibirView
          ]),
        ),
      ),
    );
  }

  @override
  FutureOr<void> afterFirstLayout(BuildContext context) {
    widget.controladorAp.listenerTheme['TelaValidarLoginUsuario'] = () {
      Future.delayed(const Duration(milliseconds: 400)).then((value) => setState(() {}));
    };
    currentView = (ModalRoute.of(context)?.settings.arguments ?? TelaValidarLoginUsuarioViewType.opcoes) as TelaValidarLoginUsuarioViewType;
    setState(() {
      loadAfter = true;
    });
  }
}
