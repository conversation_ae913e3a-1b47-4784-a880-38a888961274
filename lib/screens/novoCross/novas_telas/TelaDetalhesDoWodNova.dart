import 'dart:async';
import 'dart:ui';

import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_id_video_youtube.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/model/wod/WorkoutOfDay.dart';
import 'package:app_treino/screens/novoCross/modalRankingEPostarResultado/ModalRankingWod.dart';
import 'package:app_treino/screens/novoCross/modalRankingEPostarResultado/PostarResultadoWodView.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class TelaDetalhesDoWodNova extends StatefulWidget {
  TelaDetalhesDoWodNova({Key? key}) : super(key: key);

  @override
  State<TelaDetalhesDoWodNova> createState() => _TelaDetalhesDoWodNovaState();
}

class _TelaDetalhesDoWodNovaState extends State<TelaDetalhesDoWodNova> {
  final ExpandableController _expandableControllerAquecimento = new ExpandableController(initialExpanded: true);

  final ExpandableController _expandableControllerSkill = new ExpandableController(initialExpanded: true);

  final ExpandableController _expandableControllerComplex = new ExpandableController(initialExpanded: true);

  final ExpandableController _expandableControllerAlongamento = new ExpandableController(initialExpanded: true);

  final ExpandableController _expandableControllerAtividade = new ExpandableController(initialExpanded: true);

  final ExpandableController _expandableControllerAparelhos = new ExpandableController(initialExpanded: true);

  final ExpandableController _expandableControllerWod = new ExpandableController(initialExpanded: true);

  final ControladorWod _controladorWod = GetIt.I.get<ControladorWod>();
  double _scrollPosition = 0;
  Timer? _debounce;
  final ScrollController _scrollController = ScrollController();

  _scrollListener() {
    _scrollPosition = _scrollController.position.pixels;
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 1), () {
      setState(() {});
    });
  }

  @override
  void initState() {
    _scrollController.addListener(_scrollListener);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Positioned(
              top: 0,
              right: 0,
              left: 0,
              height: MediaQuery.of(context).size.height / 2,
              child: _controladorWod.wodDetalhado?.urlImagemWod?.isEmpty ?? true
                  ? Image.asset(
                      'assets/images/imageCross/cross1.webp',
                      fit: BoxFit.cover,
                    )
                  : ImageWidget(
                      imageUrl: _controladorWod.wodDetalhado?.urlImagemWod ?? '',
                      fit: BoxFit.cover,
                    )),
          Positioned(
            top: 0,
            right: 0,
            left: 0,
            child: Container(
                height: MediaQuery.of(context).size.height / 2,
                decoration: const BoxDecoration(
                    gradient: LinearGradient(begin: Alignment.bottomCenter, end: Alignment.topCenter, colors: [Color.fromRGBO(11, 11, 18, 1), Color.fromRGBO(11, 11, 18, 0)]))),
          ),
          Positioned(
              top: (200 - (_scrollPosition - 20)) <= 60 ? 60 : (200 - (_scrollPosition - 20)),
              left: 16,
              right: 16,
              bottom: MediaQuery.of(context).viewPadding.bottom == 0 ? 16 : 32,
              child: DScard(
                  child: Stack(
                children: [
                  Positioned(
                      child: SingleChildScrollView(
                    controller: _scrollController,
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: ((_scrollPosition + 120) < 0
                              ? 120
                              : (_scrollPosition + 120) > 300
                                  ? 300
                                  : (_scrollPosition + 120))),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(minHeight: MediaQuery.of(context).size.height),
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                          child: Column(children: [
                            // warmup card
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.aquecimento?.isNotEmpty ?? false),
                                child: Column(
                                  children: [
                                    DSCardExpansivel(context, null,
                                        categoriaCardExpansivel: CategoriaCard.secundario,
                                        body: Padding(
                                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                          child: Align(alignment: Alignment.centerLeft, child: DStextBody(_controladorWod.wodDetalhado?.aquecimento ?? '', eHeavy: false)),
                                        ),
                                        titulo: localizedString('warmup'),
                                        expandableController: _expandableControllerAquecimento),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                            // alongamento / mobilidade
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.alongamento?.isNotEmpty ?? false),
                                child: Column(
                                  children: [
                                    DSCardExpansivel(context, null,
                                        categoriaCardExpansivel: CategoriaCard.secundario,
                                        body: Padding(
                                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                          child: Align(alignment: Alignment.centerLeft, child: DStextBody(_controladorWod.wodDetalhado?.alongamento ?? '', eHeavy: false)),
                                        ),
                                        titulo: localizedString('stretching_mobility'),
                                        expandableController: _expandableControllerSkill),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                            // skill
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.parteTecnicaSkill?.isNotEmpty ?? false),
                                child: Column(
                                  children: [
                                    DSCardExpansivel(context, null,
                                        categoriaCardExpansivel: CategoriaCard.secundario,
                                        body: Padding(
                                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                          child: Align(alignment: Alignment.centerLeft, child: DStextBody(_controladorWod.wodDetalhado?.parteTecnicaSkill ?? '', eHeavy: false)),
                                        ),
                                        titulo: localizedString('skill_parte_tecnica'),
                                        expandableController: _expandableControllerAlongamento),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                            // complex ou emom
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.complexEmom?.isNotEmpty ?? false),
                                child: Column(
                                  children: [
                                    DSCardExpansivel(context, null,
                                        categoriaCardExpansivel: CategoriaCard.secundario,
                                        body: Padding(
                                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                          child: Align(alignment: Alignment.centerLeft, child: DStextBody(_controladorWod.wodDetalhado?.complexEmom ?? '', eHeavy: false)),
                                        ),
                                        titulo: 'Complex / Emom',
                                        expandableController: _expandableControllerComplex),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                            // WOD
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.descricaoExercicios?.isNotEmpty ?? false),
                                child: Column(
                                  children: [
                                    DSCardExpansivel(context, null,
                                        categoriaCardExpansivel: CategoriaCard.secundario,
                                        body: Padding(
                                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                          child: Align(
                                            alignment: Alignment.centerLeft,
                                            child: DStextBody(camelCase(_controladorWod.wodDetalhado?.descricaoExercicios ?? ''), textAlign: TextAlign.start, eHeavy: false),
                                          ),
                                        ),
                                        titulo: 'Wod',
                                        expandableController: _expandableControllerWod),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                            // aparelhos wod
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.aparelhoWodJSON?.isNotEmpty ?? false),
                                child: Column(
                                  children: [
                                    DSCardExpansivel(context, null,
                                        categoriaCardExpansivel: CategoriaCard.secundario,
                                        body: Padding(
                                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                          child: Align(alignment: Alignment.centerLeft, child: DStextBody(bodyCardAparelhos(_controladorWod.wodDetalhado?.aparelhoWodJSON), eHeavy: false)),
                                        ),
                                        titulo: 'Aparelhos',
                                        expandableController: _expandableControllerAparelhos),
                                    const SizedBox(height: 16),
                                  ],
                                )),
                            // atividades wod
                            Visibility(
                                visible: (_controladorWod.wodDetalhado?.atividadeWodJSON?.isNotEmpty ?? false),
                                child: DSCardExpansivel(context, null,
                                    categoriaCardExpansivel: CategoriaCard.secundario,
                                    body: SizedBox(
                                      height: (_controladorWod.wodDetalhado?.atividadeWodJSON?.length ?? 0) * 70,
                                      child: ListView.separated(
                                        physics: const NeverScrollableScrollPhysics(),
                                        padding: const EdgeInsets.only(left: 16, right: 16),
                                        itemCount: _controladorWod.wodDetalhado?.atividadeWodJSON?.length ?? 0,
                                        itemBuilder: (context, index) {
                                          return InkWell(
                                            onTap: () async {
                                              if (_controladorWod.listaAtividadesWod.isEmpty) {
                                                UtilitarioApp().showDialogCarregando(context);
                                                _controladorWod.consultarAtividadesWod(
                                                  carregando: () {},
                                                  sucesso: () async {
                                                    Navigator.of(context).pop();
                                                    _controladorWod.atividadeCrossDetalhada = _controladorWod.listaAtividadesWod
                                                        .where((element) => element.codAtividade == _controladorWod.wodDetalhado?.atividadeWodJSON?[index].codAtividade)
                                                        .first;
                                                    await Future.delayed(const Duration(milliseconds: 500))
                                                        .then((value) => Navigator.of(context).popAndPushNamed('/novaTelaDetalhesAtividadeCross'));
                                                  },
                                                  falha: (mensagem) {
                                                    Navigator.of(context).pop();
                                                    DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Oops', subtitulo: 'ops_no_exercises_found', tituloBotao: 'got_it');
                                                  },
                                                );
                                              } else {
                                                _controladorWod.atividadeCrossDetalhada = _controladorWod.listaAtividadesWod
                                                    .where((element) => element.codAtividade == _controladorWod.wodDetalhado?.atividadeWodJSON?[index].codAtividade)
                                                    .first;
                                                await Future.delayed(const Duration(milliseconds: 500))
                                                    .then((value) => Navigator.of(context).popAndPushNamed('/novaTelaDetalhesAtividadeCross'));
                                              }
                                            },
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Row(
                                                  children: [
                                                    ClipRRect(
                                                      borderRadius: BorderRadius.circular(4),
                                                      child: ImageWidget(
                                                        guardarImagemEmCache: true,
                                                        imageUrl: _controladorWod.wodDetalhado?.atividadeWodJSON?[index].linkVideo?.isEmpty ?? true
                                                            ? 'https://static.vecteezy.com/system/resources/previews/005/337/799/non_2x/icon-image-not-found-free-vector.jpg'
                                                            : 'https://img.youtube.com/vi/${DSidVideo.idVideoYouTube(url: _controladorWod.wodDetalhado?.atividadeWodJSON?[index].linkVideo)}/0.jpg',
                                                        height: 30,
                                                        width: 40,
                                                        fit: BoxFit.cover,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    SizedBox(
                                                        width: MediaQuery.of(context).size.width - 190,
                                                        child: DStextBody(
                                                          _controladorWod.wodDetalhado?.atividadeWodJSON?[index].nomeAtividade ?? '',
                                                          maximoLinhas: 2,
                                                          overflow: TextOverflow.ellipsis,
                                                        )),
                                                  ],
                                                ),
                                                const DSbotaoCircular(
                                                  altura: 30,
                                                  icone: TreinoIcon.angle_right,
                                                  alturaIcone: 20,
                                                )
                                              ],
                                            ),
                                          );
                                        },
                                        separatorBuilder: (BuildContext context, int index) {
                                          return const Padding(
                                            padding: EdgeInsets.only(top: 8, bottom: 8),
                                            child: Divider(),
                                          );
                                        },
                                      ),
                                    ),
                                    titulo: 'Atividades',
                                    expandableController: _expandableControllerAtividade)),
                            const SizedBox(
                              height: 128,
                            )
                          ]),
                        ),
                      ),
                    ),
                  )),
                  Positioned(
                      top: 0,
                      right: 0,
                      left: 0,
                      height: (MediaQuery.of(context).viewInsets.bottom != 0 || _scrollPosition >= 100) ? 83 : 90,
                      child: ClipRect(
                          child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                        child: Container(
                          color: DSLib.theme == ThemeMode.dark ? const Color(0xff202020).withValues(alpha: 0.5) : Colors.white.withValues(alpha: 0.5),
                        ),
                      ))),
                  Positioned(
                      top: 0,
                      right: 0,
                      left: 0,
                      height: 132,
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 24, 0, 0),
                            child: Row(
                              mainAxisAlignment: _scrollPosition >= 110 ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                              children: [
                                _scrollPosition >= 110
                                    ? DSbotaoCircular(
                                        altura: 32,
                                        alturaIcone: 22,
                                        icone: TreinoIcon.angle_left,
                                        categoria: Categoria.secundario,
                                        onTap: () {
                                          Navigator.of(context).pop();
                                        },
                                      )
                                    : Container(),
                                SizedBox(
                                  width: _scrollPosition >= 110 ? MediaQuery.of(context).size.width - 128 : MediaQuery.of(context).size.width - 70,
                                  child: _scrollPosition >= 110
                                      ? Padding(
                                          padding: const EdgeInsets.only(left: 8),
                                          child: Column(
                                            children: [
                                              DStextHeadline(
                                                UtilitarioApp.sentenseCase(
                                                  _controladorWod.wodDetalhado?.nome ?? '',
                                                ),
                                                maximoLinhas: 1,
                                                textAlign: TextAlign.start,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              DStextBody(_controladorWod.wodDetalhado?.tipoWodTabela?.nome ?? ''),
                                            ],
                                          ),
                                        )
                                      : Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            DStextTitle2(
                                              UtilitarioApp.sentenseCase(
                                                _controladorWod.wodDetalhado?.nome ?? '',
                                              ),
                                              maximoLinhas: 1,
                                              textAlign: TextAlign.start,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            DStextBody(_controladorWod.wodDetalhado?.tipoWodTabela?.nome ?? ''),
                                          ],
                                        ),
                                ),
                                _scrollPosition >= 100
                                    ? ValidaCondicoes(
                                        validacaoExtra: ((_controladorWod.wodDetalhado?.diaApresentar != null) && UtilDataHora.dataEigualDaDeHoje(dataString: _controladorWod.wodDetalhado?.diaApresentar!) ||
                                                    UtilDataHora.dataMenorSomenteUmDia(dateTime: UtilDataHora.parseStringToDate(_controladorWod.wodDetalhado?.diaApresentar!))) &&
                                                GetIt.I.get<ControladorCliente>().isUsuarioColaborador ||
                                            (_controladorWod.alunoPossuiModalidadeCross &&
                                                (!(GetIt.I.get<ControladorCliente>().isVisitante ||
                                                        GetIt.I.get<ControladorCliente>().isDesistente ||
                                                        GetIt.I.get<ControladorCliente>().isVencido ||
                                                        GetIt.I.get<ControladorCliente>().isCancelado) ||
                                                    (GetIt.I.get<ControladorCliente>().mLogadoSituacao?.checkInAtivo ?? false))),
                                        child: InkWell(
                                          onTap: () {
                                            showModalBottomSheetRankingWod(context,
                                                ranking: _controladorWod.wodDetalhado?.rankingWod,
                                                viewModal: ViewModalRanking.RANKING_WOD,
                                                wod: _controladorWod.wodDetalhado!,
                                                minutos: 0,
                                                segundos: 0);
                                          },
                                          child: Container(
                                              height: 34,
                                              width: 34,
                                              decoration: const BoxDecoration(shape: BoxShape.circle, color: Color(0xff202020)),
                                              child: const Icon(TreinoIcon.trophy, color: Colors.white)),
                                        ),
                                      )
                                    : Container(),
                              ],
                            ),
                          ),
                          /* Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                        child: Container(height: 42, decoration: BoxDecoration(
                          border: Border.all(width: 1, color: Theme.of(context).dividerColor),
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                          color: DSLib.theme == ThemeMode.dark ? Color(0xff202020).withValues(alpha:0.7) : Color(0xffF0F0F0)
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Container(),
                              Container(height: 26, width: 1, color: Theme.of(context).dividerColor,),
                               Container(),
                              Container(height: 26, width: 1, color: Theme.of(context).dividerColor,),
                               Container(),
                            ],
                          ),
                        ),
                        ),
                      ), */
                          Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: Container(
                              width: double.infinity,
                              height: 1,
                              color: Theme.of(context).dividerColor,
                            ),
                          ),
                        ],
                      ))
                ],
              ))),
          if (_controladorWod.wodDetalhado != null)
            Positioned(
                bottom: 40,
                left: 32,
                right: 32,
                child: Center(
                  child: DSbotaoPadrao(
                    titulo: localizedString('post_result'),
                    desabilitado: !_controladorWod.habilitarPostarResultadoWod,
                    onTap: () {
                      showModalBottomSheetRankingWod(context, viewModal: ViewModalRanking.POSTAR_RESULTADO, wod: _controladorWod.wodDetalhado!, minutos: 0, segundos: 0);
                    },
                  ),
                )),
          _scrollPosition <= 110
              ? Positioned(
                  top: 26,
                  left: 16,
                  right: 16,
                  child: SafeArea(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        DSbotaoCircular(
                          alturaIcone: 25,
                          icone: TreinoIcon.angle_left,
                          categoria: Categoria.secundario,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        ),
                        ValidaCondicoes(
                          validacaoExtra: ((_controladorWod.wodDetalhado?.diaApresentar != null) && UtilDataHora.dataEigualDaDeHoje(dataString: _controladorWod.wodDetalhado?.diaApresentar!) ||
                                      UtilDataHora.dataMenorSomenteUmDia(dateTime: UtilDataHora.parseStringToDate(_controladorWod.wodDetalhado?.diaApresentar!))) &&
                                  GetIt.I.get<ControladorCliente>().isUsuarioColaborador ||
                              (_controladorWod.alunoPossuiModalidadeCross &&
                                  (!(GetIt.I.get<ControladorCliente>().isVisitante ||
                                          GetIt.I.get<ControladorCliente>().isDesistente ||
                                          GetIt.I.get<ControladorCliente>().isVencido ||
                                          GetIt.I.get<ControladorCliente>().isCancelado) ||
                                      (GetIt.I.get<ControladorCliente>().mLogadoSituacao?.checkInAtivo ?? false))),
                          child: InkWell(
                            onTap: () {
                              showModalBottomSheetRankingWod(context,
                                  ranking: _controladorWod.wodDetalhado?.rankingWod, viewModal: ViewModalRanking.RANKING_WOD, wod: _controladorWod.wodDetalhado!, minutos: 0, segundos: 0);
                            },
                            child: Container(
                                height: 36,
                                width: 36,
                                decoration: const BoxDecoration(shape: BoxShape.circle, color: Color(0xff202020)),
                                child: const Icon(TreinoIcon.trophy, color: Colors.white)),
                          ),
                        )
                      ],
                    ),
                  ))
              : Container()
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controladorWod.wodDetalhado = null;
    _controladorWod.objetoPostagemResultadoWod = ObjetoPostagemResultadoWod();
    _scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  String bodyCardAtividades(List<AtividadeWod>? atividadesWod) {
    try {
      String bodyAtividades = '';
      atividadesWod?.forEach((atividade) {
        bodyAtividades += '• ${atividade.nomeAtividade}: ${((atividade.descricaoAtividade?.isNotEmpty ?? false) ? '\n${atividade.descricaoAtividade}' : '')}\n';
      });
      return bodyAtividades;
    } catch (e) {
      return '';
    }
  }

  String bodyCardAparelhos(List<AparelhoWod>? aparelhosWod) {
    try {
      String bodyAtividades = '';
      aparelhosWod?.forEach((atividade) {
        bodyAtividades += '• ${atividade.nomeAparelho}\n';
      });
      return bodyAtividades;
    } catch (e) {
      return '';
    }
  }
}
