import 'package:after_layout/after_layout.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/agenda/Agendamento.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_calendar_carousel/classes/event.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:skeleton_text/skeleton_text.dart';

class TelaReagendar extends StatefulWidget {
  TelaReagendar({Key? key}) : super(key: key);

  @override
  _TelaReagendarState createState() => _TelaReagendarState();
}

class _TelaReagendarState extends State<TelaReagendar> with AfterLayoutMixin<TelaReagendar> {
  Agendamento? mAgendamentoReagendar;
  final ControladorAgendamento _controladorAgendamento = GetIt.I.get();
  final RefreshController _refreshController = RefreshController();
  var chamouStateNoMarker = false;

  DateTime _focusedDay = DateTime.now();
  late final ValueNotifier<List<Event>> _selectedEvents;
  String horarioSelecionado = '';
  bool? telaInicial;


  void _refresh() {
    _controladorAgendamento.consultarHorariosDisponiveisAgendamentos(mAgendamentoReagendar, sucesso: () {
       // ignore: unnecessary_statements
       _getEventsForDay;
      if (!chamouStateNoMarker) {
        setState(() {
          chamouStateNoMarker = true;
        });
      }
      _refreshController.refreshCompleted();
    }, falha: (erro) {
      _refreshController.refreshFailed();
    }, carregando: () {}, dataPesquisar: _focusedDay);
  }

  @override
  void initState() {
  super.initState();

  _selectedEvents = ValueNotifier(_getEventsForDay(_focusedDay));
  }

  @override
  void dispose() {
  _selectedEvents.dispose();
  super.dispose();
  }




  List<Event> _getEventsForDay(DateTime day) {
     var dataTratada =  UtilDataHora.getDataComHorasPersonalizada(dateTime: day, horas: 0, minutos: 0, segundos: 0);
    List<Event> retorno = [];
    for (final element in _controladorAgendamento.horariosDisponiveisReagendar) {
      if (element.horarios!.isNotEmpty) {
        var data = UtilDataHora.parseStringToDate(element.dia!)!;
        if (data == dataTratada ) {
          retorno.add(Event(date: UtilDataHora.parseStringToDate(element.dia!)!));
        } 
      }
    }
    return retorno;
  }

  @override
  Widget build(BuildContext context) {
    telaInicial ??= (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['telainicial'];
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(titulo: 'modais_agenda.reagendar', 
      backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
            child: DSbotaoPadrao(
              onTap: horarioSelecionado.isNotEmpty ? () async {
                analytic(EventosKey.reagendar_confirmou_agendamento);
                _controladorAgendamento.reagendarEvento(mAgendamentoReagendar!, _focusedDay, horarioSelecionado,
                  carregando: () => UtilitarioApp().showDialogCarregando(context),
                  sucesso: (telaInicial ?? false) ? () {
                    _controladorAgendamento.consultarAgendamentos(
                      carregando: () {},
                      disponibilidades: false,
                      falha: (erro) {},
                      sucesso: () async {
                        Navigator.of(context).pop();
                        //await DSalertaSucesso().exibirAlerta(context: context);
                        Navigator.of(context).pop();
                        DSalerta().exibirToast(context, '${localizedString('success')}!', categoria: TipoAlerta.sucesso);
                      },
                    );
                  } 
                  : () {
                    _controladorAgendamento.consultarAgendamentos(
                      carregando: () {},
                      disponibilidades: false,
                      falha: (erro) {},
                      sucesso: () async {
                        Navigator.of(context).pop();
                        //await DSalertaSucesso().exibirAlerta(context: context);
                        Navigator.of(context).pop();
                        Navigator.of(context).pop();
                        DSalerta().exibirToast(context, '${localizedString('success')}!', categoria: TipoAlerta.sucesso);
                      },
                    );
                  },
                  falha: (erro) {
                    Navigator.pop(context);
                    DSalerta().exibirAlertaSimplificado(context: context, titulo: 'Ops!', subtitulo: erro.toString(), tituloBotao: 'Ok');
                  },
                );
              } : null,
              desabilitado: horarioSelecionado.isEmpty,
              titulo: 'modais_agenda.reagendar'),
          )
        ],
      ),
      body: SingleChildScrollView(
          child: Column(
            children: [
              DScalendario(
                dataLimitePassada: DateTime.now().subtract(const Duration(days: 1)),
                onPressed: (date, events) async {
                UtilitarioApp().showDialogCarregando(context);
                 _controladorAgendamento.statusConsultaHorarios = ServiceStatus.Waiting;
                setState(() {
                  _focusedDay = date;                 
                });
                await Future.delayed(const Duration(milliseconds: 600));
                _controladorAgendamento.statusConsultaHorarios = ServiceStatus.Done;
                Navigator.of(context).pop();
              }, dataSelecionada: _focusedDay,
              child: Observer(key: const Key('TelaReagendar'), 
                builder: (_) {
                  if (_controladorAgendamento.statusConsultaHorarios == ServiceStatus.Waiting) {
                    return DScard(
                      paddingExterno: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                      paddingInterno: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SkeletonAnimation(
                            child: Container(
                              height: 18,
                              width: 150,
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: Theme.of(context).dividerColor),
                            )
                          ),
                           SkeletonAnimation(
                            child: Container(
                              height: 20,
                              width: 20,
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: Theme.of(context).dividerColor),
                            )
                          ),
                        ],
                      ),
                    );
                   }
                  try {
                    return (_controladorAgendamento.filtrarHorariosPeloDia(_focusedDay).horarios ?? []).isEmpty
                      ? 
                    Center(
                      child: Padding(padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                          Padding(
                              padding: const EdgeInsets.only(top: 0, bottom: 8),
                              child: DSsvg(imagem: Imagem.busca),
                            ),
                            DStextSubheadline('erro_horarios_disponiveis', textAlign: TextAlign.center,),
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: DStextBody('tente_selecionar_outra_data', eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
                            )
                        ],
                      ),
                                      ),
                    ) : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
                                    child: DStextSubheadline(
                                      'tela_detalhes_agendamento.titulolocalization',
                                      eHeavy: false,
                                    ),
                          ),
                          Wrap(
                              children: _controladorAgendamento.filtrarHorariosPeloDia(_focusedDay).horarios!.map((e) => horarios(e)).toList(),
                            ),
                        ],
                      ); 
                  } catch (e) {
                    return 
                  Center(
                    child: Padding(padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Padding(
                                padding: const EdgeInsets.only(top: 0, bottom: 8),
                                child: DSsvg(imagem: Imagem.busca),
                              ),
                              DStextSubheadline('erro_horarios_disponiveis', textAlign: TextAlign.center,),
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: DStextBody('tente_selecionar_outra_data', eHeavy: false, ePrimario: false, textAlign: TextAlign.center,),
                              )
                          ],
                        ),
                      ),
                  );
                  }
                  },
                )
              ),
            ],
          ),
        )
      );
    }

  Widget horarios(String e){
    bool mHorarioSelecionado = horarioSelecionado == e;
    return InkWell(
      onTap: () {
        setState(() {
           if (mHorarioSelecionado) {
            if(horarioSelecionado == e){
              horarioSelecionado = '';
            }
          } else {
            horarioSelecionado = e;
          }
        });
      },
      child: Padding(
        padding: const EdgeInsets.fromLTRB(0, 6, 8, 6),
        child: Container(
          width: 64,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Theme.of(context).primaryColor),
            color: mHorarioSelecionado ? Theme.of(context).primaryColor : Colors.transparent,
          ),
          child: Padding(
            padding: const EdgeInsets.all(6.0),
            child: Center(
              child: DStextBody('${e.split(' - ').first}', eHeavy: false, corCustomizada: mHorarioSelecionado ? corQuandoFundoForGradiente(context) : null,),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void afterFirstLayout(BuildContext context) {
    setState(() {
      mAgendamentoReagendar = (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['agendamento'];
      _refresh();
    });
  }
}
