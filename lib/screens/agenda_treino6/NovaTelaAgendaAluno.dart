import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/BasicWdigetUtil.dart';
import 'package:app_treino/appWidgets/componentWidgets/TextWidgets.dart';
import 'package:app_treino/config/EventosKey.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:diacritic/diacritic.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:app_treino/controlladores/ControladorAgendamento.dart';
//import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorNotificacoes.dart';
import 'package:app_treino/model/ServiceStatus.dart';
import 'package:app_treino/model/agenda/Agendamento.dart';
import 'package:app_treino/screens/agenda_treino6/TelaConfirmarAgendamento.dart';
import 'package:app_treino/screens/transparency/TelaPermissaoNotificacoes.dart';
import 'package:ds_pacto/ds_alerta_cancelar.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:get_it/get_it.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:skeleton_text/skeleton_text.dart';

class NovaTelaAgendaAluno extends StatefulWidget {
  final bool itemBar;
  NovaTelaAgendaAluno({Key? key, this.itemBar = false}) : super(key: key);

  @override
  State<NovaTelaAgendaAluno> createState() => _NovaTelaAgendaAlunoState();
}

class _NovaTelaAgendaAlunoState extends State<NovaTelaAgendaAluno> {
  bool get wantKeepAlive => !GetIt.I.get<ControladorCliente>().bloquearNavegacao;
  final RefreshController mControllerRefres = RefreshController();
  final ControladorAgendamento mControladorAgendamento = GetIt.I.get<ControladorAgendamento>();
  DateTime mDiaSelecionado = DateTime.now();
  List<TiposAgendamentoServico> mComportamento = [
    TiposAgendamentoServico(titulo: localizedString('physical_evaluation'), icone: TreinoIcon.file_medical_alt, subTitulo: localizedString('texto_avaliacao'), id_titulo: 'Avaliação física'),
    //TiposAgendamentoServico(titulo: 'Reavaliação física', icone: TreinoIcon.file_medical_alt, subTitulo: 'Continue o seu progresso e melhore a performance de seus treinos.'),
    TiposAgendamentoServico(
        titulo: localizedString('prescricao_de_treino'), icone: TreinoIcon.clipboard_notes, subTitulo: localizedString('text_prescicao'), id_titulo: 'Prescrição de treino'),
    TiposAgendamentoServico(titulo: localizedString('renovar_treino'), icone: TreinoIcon.process, subTitulo: localizedString('texto_renovar_treino'), id_titulo: 'Renovar treino'),
    TiposAgendamentoServico(
        titulo: localizedString('contato_interpessoal'), icone: TreinoIcon.comment_verify, subTitulo: localizedString('texto_contato_interpessoal'), id_titulo: 'Contato interpessoal'),
    TiposAgendamentoServico(titulo: localizedString('revisao'), icone: TreinoIcon.sync, subTitulo: localizedString('texto_revisao'), id_titulo: 'Revisão'),
  ];

  void _refresh() {
    mControladorAgendamento.consultarAgendamentos(
        dia: mDiaSelecionado,
        carregando: () {},
        sucesso: () {
          mControllerRefres.refreshCompleted();
          mControladorAgendamento.obterTiposDeAgendamento();
        },
        falha: (erro) {
          mControllerRefres.refreshFailed();
        },
        periodo: 'MES');
  }

  @override
  void initState() {
    _refresh();
    super.initState();
  }

  String corFilter(BuildContext context) {
    return escurecerCor(Theme.of(context).primaryColor, 0.0).toHex(leadingHashSign: true).replaceAll('ff', '').toUpperCase();
  }

  String tipoAgendamento(String nome) {
    switch (nome) {
      case 'Avaliação física':
        return localizedString('tela_avaliacao_fisica.titulolocalization');
      case 'Prescrição de treino':
        return localizedString('prescricao_treino');
      case 'Renovar treino':
        return localizedString('renovar_treino');
      case 'Contato interpessoal':
        return localizedString('contato_interpessoal');
      case 'Revisão':
        return localizedString('revisao');
      default:
        return localizedString('tela_avaliacao_fisica.titulolocalization');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSappBar(
        tipoAppbar: widget.itemBar ? TipoAppBar.tituloNaEsquerda : TipoAppBar.original,
        backgroundColor: Theme.of(context).colorScheme.surface,
        titulo: localizedString('agendamentos'),
      ),
      body: SmartRefresher(
          controller: mControllerRefres,
          physics: const BouncingScrollPhysics(),
          header: const WaterDropHeader(),
          onRefresh: _refresh,
          enablePullUp: false,
          child: SingleChildScrollView(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Observer(
                builder: (_) {
                  switch (mControladorAgendamento.statusConsultaAgendamentos) {
                    case ServiceStatus.Waiting:
                      return carregandoAgendamento();
                    case ServiceStatus.Error:
                      return DScard(
                        paddingInterno: const EdgeInsets.fromLTRB(34, 16, 34, 16),
                        paddingExterno: const EdgeInsets.fromLTRB(14, 0, 14, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            DSsvg(imagem: Imagem.calendario),
                            const SizedBox(height: 8),
                            DStextTitle2('no_appointments', eHeavy: true),
                            const SizedBox(height: 4),
                            DStextBody('marque_novo_agendamento', eHeavy: false, textAlign: TextAlign.center)
                          ],
                        ),
                      );
                    case ServiceStatus.Empty:
                      return DScard(
                        paddingInterno: const EdgeInsets.fromLTRB(34, 16, 34, 16),
                        paddingExterno: const EdgeInsets.fromLTRB(14, 16, 14, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            DSsvg(imagem: Imagem.calendario),
                            const SizedBox(height: 8),
                            DStextTitle2('no_appointments', eHeavy: true),
                            const SizedBox(height: 4),
                            DStextBody('marque_novo_agendamento', eHeavy: false, textAlign: TextAlign.center)
                          ],
                        ),
                      );
                    case ServiceStatus.Done:
                      if (mControladorAgendamento.agendamentosFuturos.isNotEmpty) {
                        return Column(
                          children: [
                            mControladorAgendamento.temAgendamentoHoje
                                ? SizedBox(
                                    height: 174,
                                    width: MediaQuery.of(context).size.width,
                                    child: Swiper(
                                      loop: false,
                                      itemBuilder: (BuildContext context, int index) {
                                        return CardAgendamentosHoje(
                                          agendamento: mControladorAgendamento.agendamentosDeHoje[index],
                                        );
                                      },
                                      itemCount: mControladorAgendamento.agendamentosDeHoje.length,
                                      pagination: new SwiperPagination(
                                          builder: new DotSwiperPaginationBuilder(activeColor: Theme.of(context).primaryColor, size: 6, activeSize: 6, color: Theme.of(context).dividerColor),
                                          alignment: Alignment.bottomCenter,
                                          margin: const EdgeInsets.only(top: 0)),
                                    ),
                                  )
                                : Container(),
                            SizedBox(
                              height: 230,
                              width: MediaQuery.of(context).size.width,
                              child: Swiper(
                                loop: false,
                                itemBuilder: (BuildContext context, int index) {
                                  return CardAgendamento(
                                    agendamento: mControladorAgendamento.agendamentosFuturos[index],
                                    telaInicial: false,
                                  );
                                },
                                itemCount: mControladorAgendamento.agendamentosFuturos.length,
                                pagination: new SwiperPagination(
                                    builder: new DotSwiperPaginationBuilder(activeColor: Theme.of(context).primaryColor, size: 6, activeSize: 6, color: Theme.of(context).dividerColor),
                                    alignment: Alignment.bottomCenter,
                                    margin: const EdgeInsets.only(top: 0)),
                              ),
                            ),
                          ],
                        );
                      } else {
                        return DScard(
                          paddingInterno: const EdgeInsets.fromLTRB(34, 16, 34, 16),
                          paddingExterno: const EdgeInsets.fromLTRB(14, 16, 14, 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              DSsvg(imagem: Imagem.calendario),
                              const SizedBox(height: 8),
                              DStextTitle2('no_appointments', eHeavy: true),
                              const SizedBox(height: 4),
                              DStextBody('marque_novo_agendamento', eHeavy: false, textAlign: TextAlign.center)
                            ],
                          ),
                        );
                      }
                  }
                },
              ),
              const SizedBox(
                height: 32,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: TextHeadLine1('available'),
              ),
              ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: mComportamento.length,
                  padding: const EdgeInsets.only(bottom: 200),
                  itemBuilder: (context, index) {
                    return InkWell(
                        onTap: () {
                          mControladorAgendamento.agruparAgendamentosPorTipo(mComportamento[index].id_titulo!);
                          if (mComportamento[index].titulo!.contains(localizedString('physical_evaluation')) &&
                              !(GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_MOSTRAR_AGENDAR_AVAL_FISICA).habilitado!)) {
                            DSalerta().exibirAlertaSimplificado(
                                context: context, titulo: localizedString('sem_permissao'), subtitulo: localizedString('agenda_avaliacao_permissao'), tituloBotao: 'Ok');
                          } else {
                            DSBottomSheet().exibirAlerta(
                                context,
                                localizedString('tipo_agendamento'),
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                                  child: (mControladorAgendamento.listaAgendamentosPorTipo.length == 0)
                                      ? Center(
                                          child: DStextHeadline(
                                          localizedString('no_appointments'),
                                          eHeavy: false,
                                        ))
                                      : ListView.separated(
                                          shrinkWrap: true,
                                          itemBuilder: (context, i) {
                                            return InkWell(
                                              onTap: () {
                                                Navigator.of(context).pop();
                                                Navigator.push(context,
                                                    MaterialPageRoute(builder: (context) => TelaConfirmarAgendamento(mTipoAgendamento: mControladorAgendamento.listaAgendamentosPorTipo[i])));
                                              },
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  DStextSubheadline(UtilitarioApp.sentenseCase(mControladorAgendamento.listaAgendamentosPorTipo[i])),
                                                  const Icon(TreinoIcon.angle_right)
                                                ],
                                              ),
                                            );
                                          },
                                          separatorBuilder: (context, index) {
                                            return const Padding(
                                              padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
                                              child: Divider(),
                                            );
                                          },
                                          itemCount: mControladorAgendamento.listaAgendamentosPorTipo.length),
                                ));
                            //Navigator.push(context, MaterialPageRoute(builder: (context) => TelaConfirmarAgendamento(mTipoAgendamento: mComportamento[index].id_titulo!)));
                          }
                          //Navigator.pushNamed(context, '/telaConfirmarAgendamento');
                        },
                        child: gridTipoAgendamento((mComportamento[index].titulo!), mComportamento[index].icone!, mComportamento[index].subTitulo!));
                  }),
              const SizedBox(height: 16)
            ],
          ))),
    );
  }

  Widget gridTipoAgendamento(String name, IconData icone, String subTitulo) {
    return InkWell(
      child: DScard(
        paddingInterno: const EdgeInsets.fromLTRB(12, 12, 12, 12),
        paddingExterno: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    DSbotaoCircular(alturaIcone: 20, altura: 50, icone: icone, categoria: Categoria.secundario),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DStextSubheadline('${name}', eHeavy: true),
                          DStextCaption1(
                            '${subTitulo}',
                            eHeavy: false,
                            ePrimario: false,
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
              const DSbotaoCircular(altura: 30, icone: TreinoIcon.angle_right, categoria: Categoria.comBorda)
            ],
          ),
        ),
      ),
    );
  }

  Widget carregandoAgendamento() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: DScard(
        paddingInterno: const EdgeInsets.all(16.0),
        child: Wrap(
          runSpacing: 16,
          children: [
            Row(
              children: [
                SizedBox(
                  height: 30,
                  width: 30,
                  child: SkeletonAnimation(
                    shimmerColor: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(10),
                    shimmerDuration: 1000,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).canvasColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                SizedBox(
                  height: 14,
                  width: 180,
                  child: SkeletonAnimation(
                    shimmerColor: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(10),
                    shimmerDuration: 1000,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).canvasColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                SizedBox(
                  height: 30,
                  width: 30,
                  child: SkeletonAnimation(
                    shimmerColor: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(10),
                    shimmerDuration: 1000,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).canvasColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 10,
                      width: 120,
                      child: SkeletonAnimation(
                        shimmerColor: Theme.of(context).dividerColor,
                        borderRadius: BorderRadius.circular(10),
                        shimmerDuration: 1000,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).canvasColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14,
                      width: 160,
                      child: SkeletonAnimation(
                        shimmerColor: Theme.of(context).dividerColor,
                        borderRadius: BorderRadius.circular(10),
                        shimmerDuration: 1000,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).canvasColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
            SizedBox(
              height: 30,
              width: MediaQuery.of(context).size.width - 64,
              child: SkeletonAnimation(
                shimmerColor: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(10),
                shimmerDuration: 1000,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).canvasColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
            const Divider(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SizedBox(
                  height: 26,
                  width: (MediaQuery.of(context).size.width - 68) / 2,
                  child: SkeletonAnimation(
                    shimmerColor: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(20),
                    shimmerDuration: 1000,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).canvasColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                SizedBox(
                  height: 26,
                  width: (MediaQuery.of(context).size.width - 68) / 2,
                  child: SkeletonAnimation(
                    shimmerColor: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(20),
                    shimmerDuration: 1000,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).canvasColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget gridCarregando() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(14, 0, 14, 0),
      child: GridView.count(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          mainAxisSpacing: 16,
          crossAxisSpacing: 8,
          childAspectRatio: 0.9,
          crossAxisCount: 2,
          children: List.generate(4, (index) {
            return SizedBox(
              width: (MediaQuery.of(context).size.width / 2) - 48,
              child: CardPadrao(
                radius: 8,
                cardShadow: CardShadow.primaria,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: SizedBox(
                        height: 120,
                        width: 140,
                        child: SkeletonAnimation(
                          shimmerColor: Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 32),
                      child: SizedBox(
                        height: 20,
                        width: 130,
                        child: SkeletonAnimation(
                          shimmerColor: Theme.of(context).dividerColor,
                          borderRadius: BorderRadius.circular(10),
                          shimmerDuration: 1000,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).canvasColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            );
          })),
    );
  }

  desmarcarCompromisso(BuildContext context, ControladorAgendamento controladorAgenda, Agendamento? agendamentoAluno) {
    analytic(EventosKey.agenda_pressionou_desmarcar);
    UtilitarioApp().showDialogComImagemECallBack(
        vaiTerIconeFechar: true,
        acaoPrimaria: () {
          analytic(EventosKey.confirmou_desmarcar_agendamento);
          controladorAgenda.cancelarAgendamento(
            agendamentoAluno!,
            carregando: () {
              UtilitarioApp().showDialogCarregando(context);
            },
            sucesso: () async {
              TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.DESMARCOU_COMPROMISSO_ALUNO, fechouModal: () {
                GetIt.I.get<ControladorNotificacoes>().onTapPermissaoNotificacoes = () async {
                  Navigator.of(context).pop();
                  controladorAgenda.consultarAgendamentos(dia: mDiaSelecionado);
                  Navigator.of(context).pop();
                };
              });
            },
            falha: (erro) {
              Navigator.of(context).pop();
              BasicWdigetUtil().showDialogMensagem(context, titulo: 'Ops!', mensagem: erro);
            },
          );
        },
        acaoSecundaria: () async {
          analytic(EventosKey.pressionou_remarcar_agendamento);
          TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.REMARCOU_COMPROMISSO_ALUNO, fechouModal: () {
            Navigator.of(context).pop();
            Navigator.pushNamed(context, '/telaReagendar', arguments: agendamentoAluno).then((value) {});
          });
        },
        context: context,
        tipoAlerta: TipoAlerta.alerta,
        subtituloMensagem: localizedString('you_can_reschedule_or_cancel_this_appointment'),
        tituloBotaoPrimario: localizedString('cancel'),
        tituloBotaoSecundario: localizedString('reschedule'),
        tituloMensagem: localizedString('what_do_you_want_to_do?'));
  }
}

class TiposAgendamentoServico {
  String? titulo;
  IconData? icone;
  String? subTitulo;
  String? id_titulo;

  TiposAgendamentoServico({this.titulo, this.icone, this.subTitulo, this.id_titulo});
}

class CardAgendamento extends StatefulWidget {
  final Agendamento agendamento;
  final bool telaInicial;
  CardAgendamento({key, required this.agendamento, required this.telaInicial});

  @override
  State<CardAgendamento> createState() => _CardAgendamentoState();
}

class _CardAgendamentoState extends State<CardAgendamento> {
  bool shouldShowIcon = true;
  @override
  void initState() {
    final isAvaliacaoFisica = widget.agendamento.comportamentoEnum == 'AVALIACAO_FISICA';
    final isAcoesAvaliacaoFisica = GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.MODULO_ACOES_AVALIACAO_FISICA).habilitado ?? false;
    shouldShowIcon = !isAvaliacaoFisica || (isAvaliacaoFisica && isAcoesAvaliacaoFisica);
    super.initState();  
  }

  @override
  Widget build(BuildContext context) {
    return DScard(
        paddingInterno: const EdgeInsets.all(16.0),
        paddingExterno: const EdgeInsets.fromLTRB(16, 24, 16, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const DSbotaoCircular(altura: 30, icone: TreinoIcon.comment_plus, categoria: Categoria.secundario),
                    const SizedBox(width: 10),
                    DStextSubheadline(
                      'consulta_agendada',
                      eHeavy: true,
                    ),
                  ],
                ),
                shouldShowIcon
                    ? InkWell(
                        onTap: () {
                          modalOpcoesAgendamento(context, widget.agendamento, this.widget.telaInicial, () {});
                        },
                        child: const Icon(TreinoIcon.ellipsis_v, size: 18),
                      )
                    : Container()
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  height: 30,
                  width: 30,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(25), image: const DecorationImage(image: AssetImage('assets/images/avatar_sem_foto.png'))),
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DStextCaption1(
                      '${widget.agendamento.nome!.split('com').first}',
                      eHeavy: false,
                    ),
                    DStextSubheadline(
                      UtilitarioApp.sentenseCase(widget.agendamento.nomeProfessor),
                      eHeavy: false,
                    )
                  ],
                )
              ],
            ),
            const Padding(
              padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
              child: Divider(),
            ),
            Row(
              children: [
                DSchip(
                    icone: TreinoIcon.calender,
                    tipoChip: TipoChip.secundario,
                    posicaoIcone: PosicaoIcone.esquerda,
                    titulo: localizedString('data_horario', namedArgs: {
                      'diaTratado': widget.agendamento.data!.substring(0, 5).split('/').first,
                      'mesTratado': widget.agendamento.data!.substring(0, 5).split('/').last,
                      'horas': widget.agendamento.hora!
                    })),
              ],
            ),
          ],
        ));
  }

  modalOpcoesAgendamento(context, Agendamento? agendamento, bool? veioDaTelaInicial, Function doneAction) {
    DSBottomSheet().exibirAlerta(
        context,
        'consulta_agendada',
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
              child: InkWell(
                onTap: () {
                  DSalerta().exibirAlerta(
                      context: context,
                      titulo: 'cancelar_agendamento',
                      subtitulo: 'deseja_cancelar',
                      tituloBotao: 'confirmar_agendamento',
                      tituloBotaoSecundario: 'nao_cancelar',
                      onTap: () {
                        analytic(EventosKey.confirmou_desmarcar_agendamento);
                        GetIt.I.get<ControladorAgendamento>().cancelarAgendamento(
                          agendamento!,
                          carregando: () {
                            UtilitarioApp().showDialogCarregando(context);
                          },
                          sucesso: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            GetIt.I.get<ControladorAgendamento>().consultarAgendamentos(
                                dia: DateTime.now(),
                                sucesso: () {
                                  Future.delayed(const Duration(milliseconds: 1500)).then((value) {
                                    doneAction();
                                    modalCanceladoAgendamento(context);
                                  });
                                });
                          },
                          falha: (erro) {
                            Navigator.of(context).pop();
                            BasicWdigetUtil().showDialogMensagem(context, titulo: 'Ops!', mensagem: erro);
                          },
                        );
                      });
                },
                child: Row(
                  children: [
                    const Icon(TreinoIcon.calendar_slash),
                    const SizedBox(width: 8),
                    DStextHeadline('cancelar_agendamento', eHeavy: true, ePrimario: true),
                  ],
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Divider(),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
              child: InkWell(
                onTap: () async {
                  analytic(EventosKey.pressionou_remarcar_agendamento);
                  TelaPermissaoNotificacoes().abrirSolicitacaoPermissao(context, fluxo: FluxoPermissaoNotificacoes.REMARCOU_COMPROMISSO_ALUNO, fechouModal: () {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, '/telaReagendar', arguments: {'agendamento': agendamento, 'telainicial': veioDaTelaInicial}).then((value) {
                      doneAction();
                    });
                  });
                },
                child: Row(
                  children: [
                    const Icon(TreinoIcon.calender_rendo),
                    const SizedBox(width: 8),
                    DStextHeadline('modais_agenda.reagendar', eHeavy: true, ePrimario: true),
                  ],
                ),
              ),
            )
          ],
        ));
  }

  modalCanceladoAgendamento(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        Future.delayed(const Duration(seconds: 2)).then((value) => Navigator.pop(context));
        return Container(
          alignment: Alignment.topCenter,
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 32, 16, 16),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
                color: DSLib.theme == ThemeMode.dark ? const Color(0xff202020) : const Color(0xffFFFFFF),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: const Color(0xff34C759), width: 1.5)),
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(
                  TreinoIcon.check_circle,
                  color: Color(0xff34C759),
                ),
                const SizedBox(
                  width: 16,
                ),
                DStextCaption1(
                  'agendamento_cancelado',
                  eHeavy: true,
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

class CardAgendamentosHoje extends StatelessWidget {
  final Agendamento agendamento;
  const CardAgendamentosHoje({key, required this.agendamento});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      child: DScard(
          gradiente: true,
          child: Stack(
            children: [
              Positioned(
                  left: -68,
                  top: -6,
                  bottom: -6,
                  child: CircleAvatar(
                      radius: 102,
                      backgroundColor: escurecerCor(Theme.of(context).primaryColor, 0.10),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: SizedBox(
                          height: 64,
                          width: 64,
                          child: Stack(
                            children: [
                              const CircleAvatar(
                                radius: 48,
                                backgroundImage: AssetImage('assets/images/avatar_sem_foto.png'),
                              ),
                              Positioned(
                                  bottom: 0,
                                  right: 0,
                                  child: DSbotaoCircular(
                                    icone: UtilDataHora.horaDataEstaOcorrendoAgora(dateTime: UtilDataHora.parseStringToDate(this.agendamento.data, minutos: true))
                                        ? TreinoIcon.video
                                        : TreinoIcon.map_marker,
                                    corIcone: Theme.of(context).primaryColor,
                                    altura: 22,
                                    corPrimarioCustomizado: corQuandoFundoForGradiente(context),
                                    alturaIcone: 16,
                                  ))
                            ],
                          ),
                        ),
                      ))),
              Padding(
                padding: const EdgeInsets.only(left: 112, right: 16, top: 16, bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DStextCaption1(
                      '${this.agendamento.nome!.split('com').first}',
                      eHeavy: false,
                      textoSobFundoGradiente: true,
                      ePrimario: false,
                    ),
                    const SizedBox(height: 4),
                    DStextBody(
                      '${this.agendamento.nomeProfessor}',
                      textoSobFundoGradiente: true,
                    ),
                    const Padding(
                      padding: EdgeInsets.fromLTRB(0, 2, 0, 2),
                      child: Divider(),
                    ),
                    DStextCaption1(UtilDataHora.horaDataEstaOcorrendoAgora(dataMilis: this.agendamento.dataLong?.toInt() ?? 0) ? 'aguardando_voce' : 'tem_um_agendamento',
                        eHeavy: false, textoSobFundoGradiente: true, ePrimario: false),
                  ],
                ),
              )
            ],
          )),
    );
  }
}
