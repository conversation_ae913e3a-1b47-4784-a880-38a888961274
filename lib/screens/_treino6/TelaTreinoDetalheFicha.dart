import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:app_treino/ServiceProvider/TreinoService.dart';
import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/ValidaCondicoes.dart';
import 'package:app_treino/appWidgets/componentWidgets/grupos_musculares_widget.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorCliente.dart';
import 'package:app_treino/controlladores/ControladorExecucaoTreino.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/controlladores/controladorPlanner.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/treinoAluno/ResponseTrocaAtividade.dart';
import 'package:app_treino/model/util/UtilDataHora.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoExecucaoListaSeries.dart';
import 'package:app_treino/screens/_treino6/treinowidgets/BotaoTrocaDeAtividade.dart';
import 'package:app_treino/screens/_treino6/treinowidgets/troca_de_atividades.dart';
import 'package:collection/collection.dart';
import 'package:diacritic/diacritic.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:get_it/get_it.dart';
import 'package:pinch_zoom/pinch_zoom.dart';

class TelaTreinoDetalheFicha extends StatefulWidget {
  const TelaTreinoDetalheFicha({key});

  @override
  State<TelaTreinoDetalheFicha> createState() => _TelaTreinoDetalheFichaState();
}

class _TelaTreinoDetalheFichaState extends State<TelaTreinoDetalheFicha> {
  final ScrollController _scrollController = ScrollController();
  final controladorExecucaoTreino = GetIt.I.get<ControladorTreinoAluno>();
  final ControladorTrocaDeAtividade _controladorTrocaDeAtividade = ControladorTrocaDeAtividade();
  StateSetter? _setStateEditarAtividades;
  double _scrollPosition = 0;
  Timer? _debounce;
  Widget? equipamentos;
  List<String> equipamentosFicha = [];

  bool precisaDesabilitarOBotaoIniciar = false;

  @override
  void initState() {
    equipamentos = prepararNomeEquipamentos();
    _scrollController.addListener(_scrollListener);
    _expandableControlleExercicios.addListener(() {
      _setStateEditarAtividades?.call(() {});
    });

    controladorExecucaoTreino.consultarGruposMuscularesDaFicha(ficha: controladorExecucaoTreino.mFichaExibir as Ficha);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  _scrollListener() {
    _scrollPosition = _scrollController.position.pixels;
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 1), () {
      setState(() {
        
      });
    });
  }

  List<GrupoMuscularItem> get gruposMuscularesFicha {
    List<GrupoMuscularItem> gruposMusculares = controladorExecucaoTreino.mListaGrupoMuscular.map((String grupo) {
      GrupoMuscular grupoEnum = GrupoMuscular.values.firstWhere(
        (e) => removeDiacritics(e.toString().toLowerCase()).contains(removeDiacritics('GrupoMuscular.$grupo').toLowerCase().replaceAll(' ', '')),
        orElse: () => GrupoMuscular.nenhumValor,
      );
      return GrupoMuscularItem(gruposMuscular: grupoEnum, intensidade: 3);
    }).toList();
    return gruposMusculares;
  }

  final ExpandableController _expandableControlleMensagem = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleExercicios = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleGrupoMusculares = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleEquipamentos = new ExpandableController(initialExpanded: true);

  @override
  Widget build(BuildContext context) {
    precisaDesabilitarOBotaoIniciar = ModalRoute.of(context)!.settings.arguments as bool? ?? false;
    return PopScope(
      onPopInvokedWithResult: (_, __) {
        GetIt.I.get<ControladorTreinoAluno>().mFichaExibir = null;
      },
      child: Observer(builder: (context) {
        // ignore: unused_local_variable
        AppLifecycleState estadoApp = GetIt.I.get<ControladorApp>().estadoApp;
        return Scaffold(
            body: Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              Positioned(
                top: 0,
                right: 0,
                left: 0,
                height: MediaQuery.of(context).size.height / 2,
                child: ImageWidget(
                  guardarImagemEmCache: true,
                  imageUrl: controladorExecucaoTreino.getImagemPadraoFicha(controladorExecucaoTreino.mFichaExibir),
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                left: 0,
                child: Container(
                    height: MediaQuery.of(context).size.height / 2,
                    decoration: const BoxDecoration(
                        gradient: LinearGradient(begin: Alignment.bottomCenter, end: Alignment.topCenter, colors: [Color.fromRGBO(11, 11, 18, 1), Color.fromRGBO(11, 11, 18, 0)]))),
              ),
              Positioned(
                  top: (200 - (_scrollPosition - 20)) <= 60 ? 60 : (200 - (_scrollPosition - 20)),
                  left: 16,
                  right: 16,
                  bottom: MediaQuery.of(context).viewPadding.bottom == 0 ? 16 : 32,
                  child: DScard(
                      child: Stack(
                    children: [
                      Positioned(
                          child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Padding(
                          padding: EdgeInsets.only(
                              top: ((_scrollPosition + 120) < 0
                                  ? 120
                                  : (_scrollPosition + 120) > 300
                                      ? 300
                                      : (_scrollPosition + 120))),
                          child: ConstrainedBox(
                            constraints: BoxConstraints(minHeight: MediaQuery.of(context).size.height),
                            child: Column(children: [
                              Visibility(
                                visible: (controladorExecucaoTreino.mFichaExibir?.mensagemAluno?.isNotEmpty ?? false),
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(16, 48, 16, 0),
                                  child: DSCardExpansivel(context, null,
                                      categoriaCardExpansivel: CategoriaCard.secundario,
                                      body: Padding(
                                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                        child: Align(
                                            alignment: Alignment.centerLeft,
                                            child: DStextBody(
                                              controladorExecucaoTreino.mFichaExibir?.mensagemAluno ?? '',
                                              eHeavy: false,
                                            )),
                                      ),
                                      titulo: 'mensagem_do_professor',
                                      expandableController: _expandableControlleMensagem),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.fromLTRB(16, (controladorExecucaoTreino.mFichaExibir?.mensagemAluno?.isEmpty ?? true) ? 48 : 16, 16, 0),
                                child: DSCardExpansivel(context, null,
                                    categoriaCardExpansivel: CategoriaCard.secundario,
                                    body: ValueListenableBuilder(
                                      valueListenable: _controladorTrocaDeAtividade.isTrocaDeAtividade,
                                      builder: (BuildContext context, dynamic value, Widget? child) {
                                        return ListView.separated(
                                          scrollDirection: Axis.vertical,
                                          shrinkWrap: true,
                                          itemCount: (controladorExecucaoTreino.mFichaExibir?.atividades?.length ?? 0),
                                          padding: const EdgeInsets.only(top: 0, bottom: 16),
                                          physics: const NeverScrollableScrollPhysics(),
                                          itemBuilder: (context, index) {
                                            var atvFicha = controladorExecucaoTreino.mFichaExibir!.atividades![index];
                                            if ((atvFicha.concluida ?? false) == true) {
                                              atvFicha.series?.forEach((element) => element.concluida = true);
                                            }
                                            ProgramaAtividade atividade;
                                            try {
                                              atividade = controladorExecucaoTreino.mProgramaCarregado!.programa!.atividades!
                                                  .firstWhere((element) => element.cod.toString() == atvFicha.atividade && element.nome!.contains(atvFicha.nomeMetodoExecucao!));
                                            } catch (e) {
                                              try {
                                                atividade =
                                                    controladorExecucaoTreino.mProgramaCarregado!.programa!.atividades!.firstWhere((element) => element.cod.toString() == atvFicha.atividade);
                                              } catch (e) {
                                                return const SizedBox();
                                              }
                                            }
                                            return TelaTreinoItemExercicio(
                                              atividade: atividade,
                                              atividadeFicha: atvFicha,
                                              controladorTrocaDeAtividade: _controladorTrocaDeAtividade,
                                              currentIndex: index,
                                            );
                                          },
                                          separatorBuilder: (BuildContext context, int index) {
                                            return const Padding(
                                              padding: EdgeInsets.only(left: 16, right: 16),
                                              child: Divider(),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                    // titulo: localizedString('exercicios_literalxxx'),
                                    tituloWidget: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      spacing: 16,
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          spacing: 16,
                                          children: [
                                            DStextSubheadline(
                                              localizedString('exercicios_literal'),
                                              maximoLinhas: 1,
                                              textAlign: TextAlign.start,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            TrocaDeAtividade(
                                              controladorTrocaDeAtividade: _controladorTrocaDeAtividade,
                                              onTap: () {
                                                if (!_expandableControlleExercicios.expanded) {
                                                  _expandableControlleExercicios.toggle();
                                                }
                                              },
                                            ),
                                            const Spacer()
                                          ],
                                        ),
                                        SemAtividadesParaTrocar(_controladorTrocaDeAtividade),

                                      ],
                                    ),
                                    expandableController: _expandableControlleExercicios),
                              ),
                              controladorExecucaoTreino.mListaGrupoMuscular.isEmpty && (GetIt.I.get<ControladorApp>().chave ?? '').contains('ee35a19f99abdc8cc54ecdcd96c98d87')
                                  ? const SizedBox(height: 8)
                                  : Padding(
                                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                                      child: DSCardExpansivel(context, null,
                                          categoriaCardExpansivel: CategoriaCard.secundario,
                                          body: Padding(
                                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                    height: 300,
                                                    width: MediaQuery.of(context).size.width,
                                                    child: WidgetGruposMusculares(
                                                      customColor: Colors.blue,
                                                      gruposMusculares: gruposMuscularesFicha,
                                                    )),
                                                DStextCaption1('primarios'),
                                                controladorExecucaoTreino.mListaGrupoMuscular.isNotEmpty
                                                    ? Wrap(
                                                        spacing: 8,
                                                        children: controladorExecucaoTreino.mListaGrupoMuscular
                                                            .mapIndexed((index, element) => DStextBody(
                                                                '${UtilitarioApp.sentenseCaseFirst(controladorExecucaoTreino.mListaGrupoMuscular[index])!}${index == controladorExecucaoTreino.mListaGrupoMuscular.length - 1 ? '.' : ','}',
                                                                eHeavy: false,
                                                                ePrimario: false))
                                                            .toList(),
                                                      )
                                                    : DStextBody('sem_grupos_musculares', eHeavy: false, ePrimario: false)
                                                /* Divider(),
                                                          DStextCaption1('Secundários'),
                                                          DStextBody('Nenhum', eHeavy: false, ePrimario: false,) */
                                              ],
                                            ),
                                          ),
                                          titulo: localizedString('exercicios.grupos_musculares'),
                                          expandableController: _expandableControlleGrupoMusculares),
                                    ),
                              Visibility(
                                visible: equipamentosFicha.isNotEmpty,
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 128),
                                  child: DSCardExpansivel(context, null,
                                      categoriaCardExpansivel: CategoriaCard.secundario,
                                      body: Padding(
                                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                        child: Align(
                                          alignment: Alignment.centerLeft,
                                          child: equipamentos,
                                        ),
                                      ),
                                      titulo: localizedString('equipments'),
                                      expandableController: _expandableControlleEquipamentos),
                                ),
                              ),
                              const SizedBox(
                                height: 100,
                              )
                            ]),
                          ),
                        ),
                      )),
                      Positioned(
                          top: 0,
                          right: 0,
                          left: 0,
                          height: (MediaQuery.of(context).viewInsets.bottom != 0 || _scrollPosition >= 100) ? 136 : 142,
                          child: ClipRect(
                              child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                            child: Container(
                              color: DSLib.theme == ThemeMode.dark ? const Color(0xff202020).withValues(alpha: 0.5) : Colors.white.withValues(alpha: 0.5),
                            ),
                          ))),
                      Positioned(
                          top: 0,
                          right: 0,
                          left: 0,
                          height: 162,
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                                child: Row(
                                  mainAxisAlignment: _scrollPosition >= 110 ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                  children: [
                                    _scrollPosition >= 110
                                        ? DSbotaoCircular(
                                            altura: 32,
                                            alturaIcone: 22,
                                            icone: TreinoIcon.angle_left,
                                            categoria: Categoria.secundario,
                                            onTap: () {
                                              GetIt.I.get<ControladorTreinoAluno>().mFichaExibir = null;
                                              Navigator.of(context).pop();
                                            },
                                          )
                                        : Container(),
                                    SizedBox(
                                      width: _scrollPosition >= 110 ? MediaQuery.of(context).size.width - 128 : MediaQuery.of(context).size.width - 70,
                                      child: _scrollPosition >= 110
                                          ? Padding(
                                              padding: const EdgeInsets.only(left: 8),
                                              child: Column(
                                                children: [
                                                  DStextHeadline(
                                                    UtilitarioApp.sentenseCase(controladorExecucaoTreino.mFichaExibir?.nome?.split('::').first ?? ''),
                                                    maximoLinhas: 1,
                                                    textAlign: TextAlign.start,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                  DStextBody(
                                                    localizedString('prof.') +
                                                        ' ${UtilitarioApp.sentenseCase(UtilitarioApp().retornarApenasOPrimeiroNome(controladorExecucaoTreino.mProgramaCarregado?.programa?.professorMontou ?? ''))}',
                                                    textAlign: TextAlign.center,
                                                    maximoLinhas: 1,
                                                    eHeavy: false,
                                                    ePrimario: false,
                                                  ),
                                                  ValidaCondicoes(
                                                    validacaoExtra: controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou != null &&
                                                        controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou != '' &&
                                                        !(controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou ?? '').contains('<SEM CREF>'),
                                                    child: DStextBody(
                                                      localizedString('CREF ') + ' ${controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou ?? ''}',
                                                      textAlign: TextAlign.center,
                                                      maximoLinhas: 1,
                                                      eHeavy: false,
                                                      ePrimario: false,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                DStextTitle2(
                                                  UtilitarioApp.sentenseCase(controladorExecucaoTreino.mFichaExibir?.nome?.split('::')[0] ?? ''),
                                                  maximoLinhas: 1,
                                                  textAlign: TextAlign.start,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                                DStextBody(
                                                  localizedString('prof.') +
                                                      ' ${UtilitarioApp.sentenseCase(UtilitarioApp().retornarApenasOPrimeiroNome(controladorExecucaoTreino.mProgramaCarregado?.programa?.professorMontou ?? ''))}',
                                                  ePrimario: false,
                                                ),
                                                if (controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou != null &&
                                                    controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou != '')
                                                  DStextBody(
                                                    localizedString('CREF ') + ' ${controladorExecucaoTreino.mProgramaCarregado?.programa?.crefProfessorMontou ?? ''}',
                                                    ePrimario: false,
                                                  ),
                                              ],
                                            ),
                                    ),
                                    Container()
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                                child: Container(
                                  height: 42,
                                  decoration: BoxDecoration(
                                      border: Border.all(width: 1, color: Theme.of(context).dividerColor),
                                      borderRadius: const BorderRadius.all(Radius.circular(8)),
                                      color: DSLib.theme == ThemeMode.dark ? const Color(0xff000000) : const Color(0xffF0F0F0)),
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 8, right: 8),
                                    child: Row(
                                      mainAxisAlignment: GetIt.I.get<ControladorCliente>().isUsuarioColaborador ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        ValidaCondicoes(
                                          apenasAluno: true,
                                          child: Align(
                                            alignment: Alignment.centerLeft,
                                            child: SizedBox(
                                              height: 27,
                                              child: DScalendarioMini(
                                                  categoria: Categoria.primario,
                                                  segunda: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('SG') ?? false,
                                                  terca: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('TR') ?? false,
                                                  quarta: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('QA') ?? false,
                                                  quinta: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('QI') ?? false,
                                                  sexta: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('SX') ?? false,
                                                  sabado: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('SB') ?? false,
                                                  domingo: controladorExecucaoTreino.mFichaExibir?.diaSemana?.contains('DM') ?? false),
                                            ),
                                          ),
                                        ),
                                        ValidaCondicoes(
                                          apenasAluno: true,
                                          child: Padding(
                                            padding: const EdgeInsets.only(left: 4, right: 4),
                                            child: Container(
                                              height: 26,
                                              width: 1,
                                              color: Theme.of(context).dividerColor,
                                            ),
                                          ),
                                        ),
                                        Flexible(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              DStextCaption1(
                                                'category',
                                                eHeavy: false,
                                                ePrimario: false,
                                                textAlign: TextAlign.center,
                                              ),
                                              SizedBox(
                                                  width: 80,
                                                  child: DStextCaption1(
                                                    UtilitarioApp.sentenseCase((controladorExecucaoTreino.mFichaExibir?.categoria?.isEmpty ?? true)
                                                        ? localizedString('sem_categoria')
                                                        : controladorExecucaoTreino.mFichaExibir?.categoria ?? localizedString('sem_categoria')),
                                                    textAlign: TextAlign.center,
                                                    overflow: TextOverflow.ellipsis,
                                                    maximoLinhas: 1,
                                                  )),
                                            ],
                                          ),
                                        ),
                                        Padding(
                                          padding: GetIt.I.get<ControladorCliente>().isUsuarioColaborador ? const EdgeInsets.only(left: 16, right: 16) : const EdgeInsets.only(left: 4, right: 4) ,
                                          child: Container(
                                            height: 26,
                                            width: 1,
                                            color: Theme.of(context).dividerColor,
                                          ),
                                        ),
                                        Flexible(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              FittedBox(
                                                  child: DStextCaption1(
                                                'ultima_execucao',
                                                eHeavy: false,
                                                ePrimario: false,
                                                textAlign: TextAlign.center,
                                              )),
                                              DStextCaption1(
                                                (controladorExecucaoTreino.mFichaExibir?.ultimoTreino ?? localizedString('sem_dados')) == '01/01/1970'
                                                    ? localizedString('sem_dados')
                                                    : controladorExecucaoTreino.mFichaExibir?.ultimoTreino ?? localizedString('sem_dados'),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: Container(
                                  width: double.infinity,
                                  height: 1,
                                  color: Theme.of(context).dividerColor,
                                ),
                              ),
                            ],
                          ))
                    ],
                  ))),
              (GetIt.I.get<ControladorApp>().chave ?? '').contains('ee35a19f99abdc8cc54ecdcd96c98d87')
                  ? Container()
                  : Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Center(
                          child: ValueListenableBuilder(
                        valueListenable: _controladorTrocaDeAtividade.isTrocaDeAtividade,
                        builder: (BuildContext context, trocandoAtividade, Widget? child) {
                          if (trocandoAtividade) {
                            return Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                  colors: [
                                    Theme.of(context).scaffoldBackgroundColor, // Matches the scaffold background color
                                    Theme.of(context).scaffoldBackgroundColor.withAlpha(10), // Transparent at the top
                                  ],
                                ),
                              ),
                              width: double.infinity,
                              height: MediaQuery.of(context).size.height * 0.13,
                              padding: const EdgeInsets.fromLTRB(32, 0, 32, 40),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  ValueListenableBuilder<bool>(
                                    valueListenable: _controladorTrocaDeAtividade.hasAtividadesParaTrocar,
                                    builder: (BuildContext context, bool value, Widget? child) {
                                      return DSbotaoPadrao(
                                        titulo: localizedString(value ? 'troca_atv_original_concluir' : 'troca_atv_original_sair'),
                                    onTap: () {
                                      _controladorTrocaDeAtividade.finalizarTrocaDeAtividade(
                                        onLoading: () {
                                          UtilitarioApp().showDialogCarregando(context);
                                        },
                                        onSucesso: (fezTroca) async {
                                          if (fezTroca) {
                                            Navigator.of(context).pop();
                                            await DSalertaSucesso().exibirAlerta(context: context);
                                          }
                                        },
                                        onFalha: () {},
                                      );
                                    },
                                    tipoBotao: TipoBotao.secundario,
                                      );
                                    },
                                  ),
                                ],
                              ),
                            );
                          }

                          return Padding(
                            padding: const EdgeInsets.fromLTRB(32, 0, 32, 40),
                            child: DSbotaoPadrao(
                              desabilitado: precisaDesabilitarOBotaoIniciar,
                              titulo: localizedString('iniciar_treino'),
                              onTap: precisaDesabilitarOBotaoIniciar
                                  ? null
                                  : () async {
                                      GetIt.I.get<ControladorWod>().consultarPR();
                                      GetIt.I
                                          .get<TreinoService>()
                                          .concluirTreino(
                                              GetIt.I.get<ControladorCliente>().mUsuarioLogado!.username!,
                                              GetIt.I.get<ControladorTreinoAluno>().mProgramaCarregado!.programa!.cod!.toInt(),
                                              controladorExecucaoTreino.mFichaExibir!.cod!.toInt(),
                                              UtilDataHora.getDiaMesAno(dateTime: DateTime.now()),
                                              5,
                                              0,
                                              '',
                                              GetIt.I.get<ControladorCliente>().mUsuarioLogado!.matricula!)
                                          .then((value) async {
                                        GetIt.I.get<ControladorExecucaoTreino>().enviarTreinoFirebase();
                                        GetIt.I.get<ControladorExecucaoTreino>().registrarAcessoNoTreino();
                                        await Future.delayed(const Duration(seconds: 2));
                                        GetIt.I.get<ControladorPlanner>().obterFichaDoDia(sucesso: () {}, falha: (falha) {}, carregando: () {});
                                        GetIt.I.get<ControladorPrescricaoDeTreino>().consultarLinhaDoTempoComFiltro(
                                            idAluno: num.parse(GetIt.I.get<ControladorCliente>().mUsuarioLogado?.matricula ?? '0'),
                                            idEmpresa: GetIt.I.get<ControladorCliente>().mUsuarioLogado!.codEmpresa ?? 1);
                                      }).catchError((onError) async {});
                                      await GetIt.I.get<ControladorExecucaoTreino>().getTreinoEmExecucao();
                                      GetIt.I.get<ControladorExecucaoTreino>().salvarTreinoEmExecucao(fichaEmExecucao: GetIt.I.get<ControladorExecucaoTreino>().limparFicha());
                                      //UtilitarioApp().showDialogCarregando(context);
                                      //await Future.delayed(Duration(seconds: 2)).then((value) =>  Navigator.of(context).pop());
                                      Navigator.of(context).popAndPushNamed('/telaTreinoExecucao');
                                    },
                            ),
                          );
                        },
                      ))),
              _scrollPosition <= 110
                  ? Positioned(
                      top: 26,
                      left: 16,
                      child: Semantics(
                        identifier: 'btn_voltar_tela_suas_fichas',
                        child: SafeArea(
                          child: DSbotaoCircular(
                            alturaIcone: 25,
                            icone: TreinoIcon.angle_left,
                            categoria: Categoria.transparencia,
                            onTap: () {
                              GetIt.I.get<ControladorTreinoAluno>().mFichaExibir = null;
                              Navigator.of(context).pop();
                            },
                          ),
                        ),
                      ))
                  : Container()
            ],
          ),
        ));
      }),
    );
  }

  Widget prepararNomeEquipamentos() {
    List<Widget> widgets = [];
    List<String> equipamentos = [];
    for (final element in controladorExecucaoTreino.mProgramaCarregado?.programa?.atividades ?? []) {
      for (final atividadeFicha in (controladorExecucaoTreino.mFichaExibir?.atividades ?? [])) {
        if (atividadeFicha.atividade == element.cod.toString()) {
          if (element.aparelhos?.isNotEmpty ?? false) {
            element.aparelhos?.split(' / ').forEach((equipamento) {
              if (!equipamento.toLowerCase().contains('teste')) equipamentos.add(equipamento);
            });
          }
        }
      }
    }
    var listaRemovidoDuplicdas = equipamentos.toSet().toList();
    equipamentosFicha.addAll(listaRemovidoDuplicdas);
    for (final element in listaRemovidoDuplicdas) {
      widgets.add(DSchip(
        titulo: UtilitarioApp.sentenseCaseFirst(element)!,
        tipoChip: TipoChip.terciario,
      ));
    }
    return Wrap(direction: Axis.horizontal, runSpacing: 8, spacing: 4, children: widgets);
  }
}

class TelaTreinoItemExercicio extends StatefulWidget {
  final ProgramaAtividade atividade;
  final FichaAtividade atividadeFicha;
  final int currentIndex;
  final ControladorTrocaDeAtividade? controladorTrocaDeAtividade;

  TelaTreinoItemExercicio({
    key,
    required this.atividade,
    required this.atividadeFicha,
    this.controladorTrocaDeAtividade,
    required this.currentIndex,
  });

  @override
  State<TelaTreinoItemExercicio> createState() => _TelaTreinoItemExercicioState();
}

class _TelaTreinoItemExercicioState extends State<TelaTreinoItemExercicio> {
  final controladorExecucaoTreino = GetIt.I.get<ControladorTreinoAluno>();
  List<String> urls = [];
  ProgramaAtividade? atividadeTroca;

  @override
  void initState() {
    super.initState();
    obterFotosLocais();
  }

  String getImageUrl() {
    if ((atividadeExibir.listImgMediumUrls?.isNotEmpty ?? false) && !(atividadeExibir.listImgMediumUrls?.first.professor ?? false)) {
      return atividadeExibir.listImgMediumUrls?.first.linkImg ?? '';
    } else if ((atividadeExibir.imgMediumUrls?.isNotEmpty ?? false)) {
      return atividadeExibir.imgMediumUrls?.first ?? '';
    } else {
      return 'https://prgbrasil.com/wp-content/themes/consultix/images/no-image-found-360x250.png';
    }
  }

  ProgramaAtividade get atividadeExibir => widget.controladorTrocaDeAtividade?.getAtividadeTrocar(widget.atividade) ?? widget.atividade;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: InkWell(
        onTap: () {
          if (widget.controladorTrocaDeAtividade?.isTrocaDeAtividadeValue ?? false) {
            return;
          }
          Navigator.of(context).pushNamed('/telaTreinoDetalheAtividade', arguments: {'dadosAtividade': widget.atividade, 'dadosSerie': widget.atividadeFicha});
        },
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                showDialog(
                    context: context,
                    barrierDismissible: true,
                    builder: (_) {
                      return StatefulBuilder(
                        builder: (context, setState) => Stack(
                          children: [
                            SizedBox(
                              width: double.maxFinite,
                              height: double.maxFinite,
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                                child: PopScope(
                                    canPop: false,
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(16, 80, 16, 80),
                                      child: PinchZoom(
                                        maxScale: 5,
                                        zoomEnabled: true,
                                        onZoomStart: () {},
                                        onZoomEnd: () {},
                                        child: SizedBox(
                                          height: MediaQuery.of(context).size.height * 0.58,
                                          width: MediaQuery.of(context).size.width - 32,
                                          child: (atividadeExibir.imgMediumUrls!.isNotEmpty || urls.isEmpty)
                                              ? ImageWidget(
                                                  guardarImagemEmCache: true,
                                                  placeholder: (_, __) => Container(
                                                    height: MediaQuery.of(context).size.height * 0.46,
                                                    width: double.maxFinite,
                                                    padding: const EdgeInsets.all(20),
                                                    child: const CircularProgressIndicator(),
                                                  ),
                                                  imageUrl: getImageUrl(),
                                                  height: MediaQuery.of(context).size.height * 0.46,
                                                  width: double.maxFinite,
                                                  fit: BoxFit.fitWidth,
                                                )
                                              : Image.file(File(urls.first.replaceAll('####', '')),
                                                  height: MediaQuery.of(context).size.height * 0.46, width: double.maxFinite, fit: BoxFit.fitWidth),
                                        ),
                                      ),
                                    )),
                              ),
                            ),
                            Positioned(
                              right: 16,
                              top: 16,
                              child: Padding(
                                padding: const EdgeInsets.only(top: 0, right: 0),
                                child: DSbotaoCircular(
                                  altura: 34,
                                  alturaIcone: 22,
                                  categoria: Categoria.secundario,
                                  icone: TreinoIcon.times,
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    });
              },
              child: Stack(
                children: [
                  ClipRRect(
                      borderRadius: BorderRadius.circular(8.0),
                      child: ((atividadeExibir.imgMediumUrls?.isNotEmpty ?? false) || urls.isEmpty)
                          ? ImageWidget(
                              guardarImagemEmCache: true,
                              placeholder: (_, __) => Container(
                                width: 80,
                                height: 80,
                                padding: const EdgeInsets.all(20),
                                child: const CircularProgressIndicator(),
                              ),
                              errorWidget: (p0, p1, p2) {
                                return const Icon(Icons.hide_image);
                              },
                              imageUrl: getImageUrl(),
                              height: 43,
                              width: 56,
                              fit: BoxFit.cover,
                            )
                          : Image.file(File(urls.first.replaceAll('####', '')), width: 56, height: 43, fit: BoxFit.cover)),
                  if (atividadeExibir.isAtividadeTroca)
                    Align(
                      alignment: Alignment.center,
                      child: SizedBox(
                        width: 12,
                        height: 12,
                        child: DScard(
                            child: Container(
                          margin: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const SizedBox(
                            height: 8,
                            width: 8,
                          ),
                        )),
                      ),
                    )
                ],
              ),
            ),
            Semantics(
                identifier: 'acessar_detalhes_da_atividade',
              child: Padding(
                padding: const EdgeInsets.only(left: 8),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width - 160,
                  child: Center(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                                width: MediaQuery.of(context).size.width - 190,
                                child: DStextSubheadline(
                                  UtilitarioApp.sentenseCase(atividadeExibir.nome ?? ''),
                                  maximoLinhas: 8,
                                  eHeavy: false,
                                  overflow: TextOverflow.ellipsis,
                                )),
                            (widget.controladorTrocaDeAtividade?.isTrocaDeAtividadeValue ?? false)
                                ? SizedBox(
                                    width: 30,
                                    child: BotaoTrocaDeAtividade(
                                      atividade: atividadeExibir,
                                      onChange: (ResponseTrocaDeAtividade nova) {},
                                      index: widget.currentIndex,
                                      fichaIsIA: false,
                                      controladorTrocaDeAtividade: widget.controladorTrocaDeAtividade,
                                      isEditando: (widget.controladorTrocaDeAtividade?.isTrocaDeAtividadeValue ?? false),
                                      onReplace: () {
                                        setState(() {});
                                      },
                                      atividadeOriginal: widget.atividade,
                                    ),
                                  )
                                : const Icon(TreinoIcon.angle_right)
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(children: [
                            if (widget.controladorTrocaDeAtividade?.isTrocaDeAtividadeValue ?? false)
                              TrocaDeAtividadeTemDisponibilidade(
                                valueListenable: widget.controladorTrocaDeAtividade!.getLoading(atividadeExibir),
                              )
                            else
                              detalhesExercicioChip(
                                  icon: 'assets/images/series_execucoes.svg',
                                  descricao: ((widget.atividadeFicha.series?.isEmpty ?? true)
                                          ? 'Exercício sem série'
                                          : '${widget.atividadeFicha.series!.length} série${widget.atividadeFicha.series!.length > 1 ? 's' : ''}')
                                      .toString()),
                          ]),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void obterFotosLocais() {
    urls.clear();
    controladorExecucaoTreino.obterImagemLocalAtividade(
      mAtividade: widget.atividadeFicha,
      sucesso: (fotos) {
        urls.removeWhere((o) => o.contains('####'));
        if (fotos.isNotEmpty) {
          for (final element in fotos) {
            urls.add(element.path! + '####');
          }
        }
        setState(() {});
      },
    );
  }
}
