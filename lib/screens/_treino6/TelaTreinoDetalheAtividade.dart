import 'dart:async';
import 'dart:ui';

import 'package:app_treino/Utilitario.dart';
import 'package:app_treino/appWidgets/componentWidgets/image_widget.dart';
import 'package:app_treino/appWidgets/componentWidgets/skeletons/SkeletonPadrao.dart';
import 'package:app_treino/controlladores/ControladorApp.dart';
import 'package:app_treino/controlladores/ControladorPrescricaoDeTreino.dart';
import 'package:app_treino/controlladores/ControladorTreinoAluno.dart';
import 'package:app_treino/controlladores/ControladorWod.dart';
import 'package:app_treino/model/doClienteApp/ClienteApp.dart';
import 'package:app_treino/model/treinoAluno/ProgramadeTreino.dart';
import 'package:app_treino/model/treinoAluno/ResponseTrocaAtividade.dart';
import 'package:app_treino/model/util/carga_utils.dart';
import 'package:app_treino/screens/_treino6/MediaDetalhesAtividade.dart';
import 'package:app_treino/screens/_treino6/TelaTreinoExecucaoListaSeries.dart';
import 'package:app_treino/screens/_treino6/treinowidgets/BotaoTrocaDeAtividade.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:detectable_text_field/detector/sample_regular_expressions.dart';
import 'package:detectable_text_field/widgets/detectable_text.dart';
import 'package:ds_pacto/ds_id_video_youtube.dart';
import 'package:ds_pacto/ds_pacto.dart';
import 'package:ds_pacto/fonts/icomoon_icons.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:pinch_zoom/pinch_zoom.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TelaTreinoDetalheAtividade extends StatefulWidget {
  const TelaTreinoDetalheAtividade({key});

  @override
  State<TelaTreinoDetalheAtividade> createState() => _TelaTreinoDetalheAtividadeState();
}

class _TelaTreinoDetalheAtividadeState extends State<TelaTreinoDetalheAtividade> {
  ProgramaAtividade? atividade;
  ProgramaAtividade? atividadeOriginal;
  FichaAtividade? atividadeFicha;

  final ScrollController _scrollController = ScrollController();
  double _scrollPosition = 0;
  Timer? _debounce;
  Widget? equipamentos;

  bool _dadosIniciados = false;

  @override
  void initState() {
    _scrollController.addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  _scrollListener() {
    _scrollPosition = _scrollController.position.pixels;
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 1), () {
      setState(() {});
    });
  }

  String getImageUrl(ProgramaAtividade? atividade) {
    if ((atividade?.listImgMediumUrls?.isNotEmpty ?? false) && !(atividade?.listImgMediumUrls?.first.professor ?? false)) {
      return atividade?.listImgMediumUrls?.first.linkImg ?? '';
    } else {
      return 'https://prgbrasil.com/wp-content/themes/consultix/images/no-image-found-360x250.png';
    }
  }

  final ExpandableController _expandableControlleGrupoMusculares = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleMensagem = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControllerAparelho = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControllerMedia = new ExpandableController(initialExpanded: true);
  final ExpandableController _expandableControlleDetalhes = new ExpandableController(initialExpanded: true);
  final controladorExecucaoTreino = GetIt.I.get<ControladorTreinoAluno>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final _controladorApp = GetIt.I.get<ControladorApp>();

  @override
  Widget build(BuildContext context) {
    atividade ??= (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['dadosAtividade'] as ProgramaAtividade?;
    atividadeOriginal = atividade?.clone();
    atividadeFicha ??= (ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>)['dadosSerie'] as FichaAtividade?;
    int? indexAtividadePrograma = controladorExecucaoTreino.mProgramaCarregado!.programa!.atividades!.indexWhere((element) => element.cod == atividade?.cod);
    
    if (!_dadosIniciados && atividade != null && atividadeFicha != null) {
      _dadosIniciados = true;
      // Garante atualização dos dados logo após atribuição inicial
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {});
      });
    }
    
    return Scaffold(
        body: Scaffold(
          key: _scaffoldKey,
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            right: 0,
            left: 0,
            height: MediaQuery.of(context).size.height / 2,
            child: atividade!.urlVideosProgramaTreino?.isNotEmpty ?? false
                ? ImageWidget(guardarImagemEmCache: true, imageUrl: 'https://img.youtube.com/vi/${DSidVideo.idVideoYouTube(url: atividade!.urlVideosProgramaTreino?.first ?? '')}/maxres2.jpg', fit: BoxFit.cover)
                : ImageWidget(
                    guardarImagemEmCache: true,
                    imageUrl: getImageUrl(atividade),
                    fit: BoxFit.fitHeight,
                  ),
          ),
          Positioned(
            top: 0,
            right: 0,
            left: 0,
            child: ((atividade?.urlVideosProgramaTreino?.isNotEmpty ?? false) ? (atividade?.urlVideosProgramaTreino?.first ?? '').contains('youtu') : false)
                ? GestureDetector(
                    onTap: () {
                      showDialog(
                          context: _scaffoldKey.currentContext!,
                          barrierDismissible: true,
                          builder: (_) {
                            return StatefulBuilder(
                              builder: (context, setState) => Stack(
                                children: [
                                  SizedBox(
                                    width: double.maxFinite,
                                    height: double.maxFinite,
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                                      child: PopScope(
                                          canPop: false,
                                          child: Padding(
                                            padding: const EdgeInsets.fromLTRB(16, 80, 16, 80),
                                            child: SizedBox(
                                                height: MediaQuery.of(context).size.height * 0.58,
                                                width: MediaQuery.of(context).size.width - 32,
                                                child: (DSidVideo.idVideoYouTube(url: atividade!.urlVideosProgramaTreino?.first ?? '').isNotEmpty)
                                                    ? SizedBox(
                                                        width: double.maxFinite,
                                                        height: MediaQuery.of(context).size.height * 0.46,
                                                        child: WebViewWidget(
                                                          controller: WebViewController()
                                                            ..setJavaScriptMode(JavaScriptMode.unrestricted)
                                                            ..loadRequest(Uri.parse('https://www.youtube-nocookie.com/embed/${DSidVideo.idVideoYouTube(url: atividade!.urlVideosProgramaTreino?.first ?? '')}')),
                                                        ),
                                                      )
                                                    : const SizedBox(child: SkeletonPadrao(), height: 200)),
                                          )),
                                    ),
                                  ),
                                  Positioned(
                                    right: 16,
                                    top: 16,
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 0, right: 0),
                                      child: DSbotaoCircular(
                                        altura: 34,
                                        alturaIcone: 22,
                                        categoria: Categoria.secundario,
                                        icone: TreinoIcon.times,
                                        onTap: () {
                                          Navigator.of(context).pop();
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          });
                    },
                    child: Stack(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).orientation == Orientation.landscape ? MediaQuery.of(context).size.height : MediaQuery.of(context).size.height * 0.35,
                          width: double.maxFinite,
                          child: const Center(child: Icon(Icons.play_arrow, size: 50, color: Colors.white60)),
                        ),
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          top: 0,
                          child: Container(
                              decoration:
                                  const BoxDecoration(gradient: LinearGradient(begin: Alignment.centerLeft, end: Alignment.centerRight, colors: [Color(0x50464646), Color(0x001E1E1E)]))),
                        ),
                      ],
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      showDialog(
                          context: _scaffoldKey.currentContext!,
                          barrierDismissible: true,
                          builder: (_) {
                            return StatefulBuilder(
                              builder: (context, setState) => Stack(
                                children: [
                                  SizedBox(
                                    width: double.maxFinite,
                                    height: double.maxFinite,
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
                                      child: PopScope(
                                          canPop: false,
                                          child: Padding(
                                            padding: const EdgeInsets.fromLTRB(16, 80, 16, 80),
                                            child: PinchZoom(
                                              maxScale: 5,
                                              zoomEnabled: true,
                                              onZoomStart: () {},
                                              onZoomEnd: () {},
                                              child: SizedBox(
                                                height: MediaQuery.of(context).size.height * 0.58,
                                                width: MediaQuery.of(context).size.width - 32,
                                                child: CachedNetworkImage(
                                                  imageUrl: getImageUrl(atividade),
                                                  height: MediaQuery.of(context).size.height * 0.46,
                                                  width: double.maxFinite,
                                                  fit: BoxFit.fitWidth,
                                                  errorWidget: (_, __, ___) => const Icon(Icons.hide_image),
                                                ),
                                              ),
                                            ),
                                          )),
                                    ),
                                  ),
                                  Positioned(
                                    right: 16,
                                    top: 16,
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 0, right: 0),
                                      child: DSbotaoCircular(
                                        altura: 34,
                                        alturaIcone: 22,
                                        categoria: Categoria.secundario,
                                        icone: TreinoIcon.times,
                                        onTap: () {
                                          Navigator.of(context).pop();
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          });
                    },
                    child: Container(
                        height: MediaQuery.of(context).size.height / 2,
                        decoration: const BoxDecoration(
                            gradient: LinearGradient(begin: Alignment.bottomCenter, end: Alignment.topCenter, colors: [Color.fromRGBO(11, 11, 18, 1), Color.fromRGBO(11, 11, 18, 0)]))),
                  ),
          ),
          Positioned(
              top: (200 - (_scrollPosition - 20)) <= 60 ? 60 : (200 - (_scrollPosition - 20)),
              left: 16,
              right: 16,
              bottom: MediaQuery.of(context).viewPadding.bottom == 0 ? 16 : 32,
              child: DScard(
                  child: Stack(
                children: [
                  Positioned(
                      child: SingleChildScrollView(
                    controller: _scrollController,
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: ((_scrollPosition + 80) < 0
                              ? 100
                              : (_scrollPosition + 100) > 300
                                  ? 300
                                  : (_scrollPosition + 100))),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(minHeight: MediaQuery.of(context).size.height),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Visibility(
                              visible: (atividade?.descricao?.isNotEmpty ?? false),
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                                child: DSCardExpansivel(context, null,
                                    categoriaCardExpansivel: CategoriaCard.secundario,
                                    body: Padding(
                                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                      child: Align(
                                          alignment: Alignment.centerLeft,
                                          child: DetectableText(
                                            locale: _controladorApp.appLocale,
                                            trimExpandedText: 'Ver menos',
                                            trimCollapsedText: 'Ver mais',
                                            moreStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true),
                                            lessStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true),
                                            basicStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: false),
                                            detectedStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true).copyWith(color: Theme.of(context).primaryColor),
                                            text: (atividade?.descricao?.isEmpty ?? true) ? 'Nenhum' : '${atividade?.descricao}',
                                            detectionRegExp: hashTagAtSignUrlRegExp,
                                            onTap: (tappedText) {
                                              if (Uri.tryParse(tappedText)?.hasAbsolutePath ?? false) {
                                                launchUrl(Uri.parse(tappedText));
                                              }
                                            },
                                          ),
                                        ),
                                    ),
                                    titulo: localizedString("exercise's_description"),
                                    expandableController: _expandableControlleDetalhes),
                              ),
                            ),
                            Visibility(
                              visible: (atividadeFicha!.series?.isNotEmpty ?? false),
                              child: Padding(
                                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                                  child: DSCardExpansivel(context, null,
                                      categoriaCardExpansivel: CategoriaCard.secundario,
                                      body: Padding(
                                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                        child: SizedBox(
                                            child: atividadeFicha!.series == null || (atividadeFicha!.series?.isEmpty ?? true)
                                                ? Padding(
                                                    padding: const EdgeInsets.only(top: 16, bottom: 8),
                                                    child: DSchip(tipoChip: TipoChip.secundario, titulo: 'Exercício sem série'),
                                                  )
                                                : ListView.separated(
                                                    itemCount: atividadeFicha!.series?.length ?? 0,
                                                    shrinkWrap: true,
                                                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                                    physics: const NeverScrollableScrollPhysics(),
                                                    itemBuilder: (context, index) {
                                                      return Column(
                                                        children: [
                                                          Container(
                                                            decoration: BoxDecoration(
                                                                borderRadius: BorderRadius.circular(16),
                                                                color: DSLib.theme == ThemeMode.dark ? const Color(0xff000000) : const Color(0xffF0F0F0)),
                                                            child: Row(
                                                              crossAxisAlignment: CrossAxisAlignment.center,
                                                              children: [
                                                                Container(
                                                                  height: 30,
                                                                  width: 30,
                                                                  decoration: BoxDecoration(
                                                                      border: Border.all(width: 1, color: Theme.of(context).dividerColor),
                                                                      borderRadius: const BorderRadius.all(Radius.circular(25))),
                                                                  child: Center(child: DStextCaption1((index + 1).toString())),
                                                                ),
                                                                Padding(
                                                                  padding: const EdgeInsets.fromLTRB(8, 4, 0, 4),
                                                                  child: SizedBox(
                                                                    width: MediaQuery.of(context).size.width - 154,
                                                                    child:
                                                                        (GetIt.I.get<ControladorPrescricaoDeTreino>().categoria(atividadeFicha?.series?.first.tipoAtividade?.toInt() ?? 0) ??
                                                                                    'SEM CATEGORIA')
                                                                                .contains('AEROBICO')
                                                                            ? Wrap(
                                                                                children: [
                                                                                  detalhesExercicioChip(
                                                                                      icon: 'assets/images/duracao_execucoes.svg',
                                                                                      descricao: (atividadeFicha!.series?[index].valor1 ?? '').isNotEmpty
                                                                                          ? (atividadeFicha!.series?[index].valor1 ?? '')
                                                                                          : '0'),
                                                                                  detalhesExercicioChip(
                                                                                      icon: 'assets/images/velocidade_execucoes.svg',
                                                                                      descricao: (atividadeFicha!.series?[index].velocidade ?? 0).toString()),
                                                                                  detalhesExercicioChip(
                                                                                      icon: 'assets/images/distancia_execucoes.svg',
                                                                                      descricao: (atividadeFicha!.series?[index].distancia ?? '').toString()),
                                                                                ],
                                                                              )
                                                                            : Wrap(
                                                                                children: [
                                                                                  detalhesExercicioChip(
                                                                                      icon: 'assets/images/repeticoes_execucoes.svg',
                                                                                      descricao: (atividadeFicha!.series?[index].repeticaoApp ?? '').isNotEmpty
                                                                                          ? (atividadeFicha!.series?[index].repeticaoApp ?? '')
                                                                                          : '0'),
                                                                                  detalhesExercicioChip(
                                                                                      icon: 'assets/images/carga_execucoes.svg',
                                                                                      descricao: CargaUtils.getDescricaoCarga(atividadeFicha!, index),
                                                                                  ),
                                                                                  detalhesExercicioChip(
                                                                                      icon: 'assets/images/cadencia_execucoes.svg',
                                                                                      descricao: (atividadeFicha!.series?[index].cadencia ?? '').isNotEmpty
                                                                                          ? (atividadeFicha!.series?[index].cadencia ?? '')
                                                                                          : '0'),
                                                                                ],
                                                                              ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                    separatorBuilder: (BuildContext context, int index) {
                                                      return const Divider();
                                                    },
                                                  )),
                                      ),
                                      titulo: 'series',
                                      expandableController: _expandableControlleGrupoMusculares)),
                            ),
                            (atividadeFicha?.series?.isEmpty ?? true)
                                ? Container()
                                : Visibility(
                                    visible: (atividadeFicha?.series?.isEmpty ?? true) || (atividadeFicha?.series?.first.complemento ?? '').isNotEmpty,
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                                      child: DSCardExpansivel(context, null,
                                          categoriaCardExpansivel: CategoriaCard.secundario,
                                          body: Padding(
                                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                            child: Align(
                                                alignment: Alignment.centerLeft,
                                                child: DetectableText(
                                                  locale: _controladorApp.appLocale,
                                                  trimExpandedText: 'Ver menos',
                                                  trimCollapsedText: 'Ver mais',
                                                  moreStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true),
                                                  lessStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true),
                                                  basicStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: false),
                                                  detectedStyle: DStextUtil.stylebody(eHeavy: false, ePrimario: true).copyWith(color: Theme.of(context).primaryColor),
                                                  text: atividadeFicha?.series?.first.complemento ?? '',
                                                  detectionRegExp: hashTagAtSignUrlRegExp,
                                                  onTap: (tappedText) {
                                                    if (Uri.tryParse(tappedText)?.hasAbsolutePath ?? false) {
                                                      launchUrl(Uri.parse(tappedText));
                                                    }
                                                  },
                                                ),
                                              ),
                                          ),
                                          titulo: 'mensagem_do_professor',
                                          expandableController: _expandableControlleMensagem),
                                    ),
                                  ),
                            Visibility(
                              visible: (atividade?.aparelhos?.isNotEmpty ?? true),
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                                child: DSCardExpansivel(context, null,
                                    categoriaCardExpansivel: CategoriaCard.secundario,
                                    body: Padding(
                                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                                      child: Align(
                                          alignment: Alignment.centerLeft,
                                          child: DStextBody(
                                            (atividade?.aparelhos?.isEmpty ?? true) ? 'Nenhum' : '${atividade?.aparelhos}',
                                            eHeavy: false,
                                          )),
                                    ),
                                    titulo: 'aparelhos',
                                    expandableController: _expandableControllerAparelho),
                              ),
                            ),
                            Visibility(
                                visible: (atividade!.hasMedia),
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(16, 24, 16, 24),
                                  child: DSCardExpansivel(context, null,
                                      categoriaCardExpansivel: CategoriaCard.secundario,
                                      titulo: 'Imagens / Vídeos',
                                      body: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: MediaDetalhesAtividade(
                                          atividade: atividade!,
                                          atividadeFicha: atividadeFicha!,
                                        ),
                                      ),
                                      expandableController: _expandableControllerMedia),
                                ))
                          ],
                        ),
                      ),
                    ),
                  )),
                  Positioned(
                      top: 0,
                      right: 0,
                      left: 0,
                      height: (MediaQuery.of(context).viewInsets.bottom != 0 || _scrollPosition >= 100) ? 81 : 92,
                      child: ClipRect(
                          child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                        child: Container(
                          color: DSLib.theme == ThemeMode.dark ? const Color(0xff202020).withValues(alpha: 0.5) : Colors.white.withValues(alpha: 0.5),
                        ),
                      ))),
                  Positioned(
                      top: 0,
                      right: 0,
                      left: 0,
                      height: 142,
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 24, 16, 0),
                            child: Row(
                              mainAxisAlignment: _scrollPosition >= 110 ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                              children: [
                                Visibility(
                                    visible: _scrollPosition >= 110,
                                    child: Row(
                                      children: [
                                        DSbotaoCircular(
                                          altura: 32,
                                          alturaIcone: 22,
                                          icone: TreinoIcon.angle_left,
                                          categoria: Categoria.secundario,
                                          onTap: () {
                                            Navigator.of(context).pop();
                                          },
                                        ),
                                        BotaoTrocaDeAtividade(
                                          atividade: atividade!,
                                          atividadeOriginal: atividadeOriginal!,
                                          fichaIsIA: controladorExecucaoTreino.mProgramaCarregado!.programa!.geradoPorIA ?? false,
                                          margin: const EdgeInsets.only(left: 16, right: 16),
                                          onChange: (ResponseTrocaDeAtividade nova) {
                                              atividade = nova.atividadeBase;
                                              atividadeFicha = nova.atividadeFicha;
                                            setState(() {
                                            });
                                          },
                                          index: indexAtividadePrograma,
                                        ),
                                      ],
                                    )),
                                SizedBox(
                                  width: _scrollPosition >= 110
                                      ? MediaQuery.of(context).size.width - (128 + (GetIt.I.get<ControladorApp>().getConfiguracaoApp(ModuloApp.TDAIA).habilitado! ? 40 : 0))
                                      : MediaQuery.of(context).size.width - 70,
                                  child: _scrollPosition >= 110
                                      ? Padding(
                                          padding: const EdgeInsets.only(left: 8),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: DStextHeadline(
                                                  UtilitarioApp.sentenseCase(atividade?.nome ?? ''),
                                                  maximoLinhas: 2,
                                                  textAlign: TextAlign.start,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      : Row(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            BotaoTrocaDeAtividade(
                                              atividade: atividade!,
                                              fichaIsIA: controladorExecucaoTreino.mProgramaCarregado!.programa!.geradoPorIA ?? false,
                                              margin: const EdgeInsets.only(right: 16),
                                              onChange: (ResponseTrocaDeAtividade nova) {
                                                setState(() {
                                                  atividade = nova.atividadeBase;
                                                  atividadeFicha = nova.atividadeFicha;
                                                });
                                              },
                                              index: indexAtividadePrograma,
                                              atividadeOriginal: atividadeOriginal!,
                                            ),
                                            Expanded(
                                              child: DStextTitle2(
                                                UtilitarioApp.sentenseCase(atividade?.nome ?? ''),
                                                maximoLinhas: 2,
                                                textAlign: TextAlign.start,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                ),
                                Container()
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Container(
                              width: double.infinity,
                              height: 1,
                              color: Theme.of(context).dividerColor,
                            ),
                          ),
                        ],
                      ))
                ],
              ))),
          _scrollPosition <= 110
              ? Positioned(
                  top: 26,
                  left: 16,
                  child: SafeArea(
                    child: DSbotaoCircular(
                      alturaIcone: 25,
                      icone: TreinoIcon.angle_left,
                      categoria: Categoria.transparencia,
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ))
              : Container()
        ],
      ),
    ));
  }
}
