{"resetar_aluno": "<PERSON><PERSON><PERSON>", "alteracao_vencimento_parcela": "Ao trancar o contrato, as prestações pendentes terão o vencimento alterado, adiando a data de vencimento.", "alteracao_vencimento_parcela_aguarde": "<PERSON>to pode levar alguns segundos, aguarde...", "nenhum_video_encontrado": "Nenhum vídeo encontrado", "atv_info_01": "Informações da atividade", "atv_info_02": "Mais informaç<PERSON>", "troca_atv_original_concluir": "Concluir edição", "troca_atv_original_sair": "<PERSON><PERSON> da <PERSON>", "troca_atv_original": "Atividade Original", "troca_atv_0_paratrocar": "Nenhuma atividade para trocar", "troca_atv_disponivel": "Troca disponível", "troca_atv_nao_possivel_consultar": "Não foi possível consultar", "troca_atv_tentar_em_breve": "Tente novamente dentro de alguns minutos", "troca_atv_sem_atvs": "Sem atividades disponíveis", "troca_atv_descricao_ativo": "Se não gostou de algum exercício, pode trocá-lo utilizando o botão de editar. A troca está disponível apenas para exercícios que permitem substituição.", "você_ja_possui_compromissos_no_horario": "Já tem compromissos para as: {}", "observacao_wod_obrigatoria": "O campo 'Descrição do Wod' é obrigatório.", "solucao_blue_01": "Reconectar dispositivo", "solucao_blue_02": "Problemas com a ligação", "solucao_blue_03": "Tentar novamente", "solucao_blue_04": "Abra as Configurações do seu telemóvel\nVá para Bluetooth\nLocalize o seu dispositivo {} na lista\nTocque em \"Esquecer\" ou \"Desemparelhar\"\nReinicie o dispositivo {}\nVolte à aplicação e tente conectar novamente", "solucao_blue_05": "Reconnec {}", "solucao_blue_06": "• Verifique se o Bluetooth está ligado\n• Certifique-se de que o dispositivo está carregado\n• Mantenha o dispositivo próximo do telemóvel\n• Reinicie o telemóvel se o problema persistir", "solucao_blue_07": "Dicas adicionais:", "solucao_blue_08": "A ligar à balança", "solucao_blue_09": "A recolher dados", "solucao_blue_10": "Avaliação concluída com sucesso!", "solucao_blue_11": "Estamos a configurar tudo!\nIsto pode demorar alguns instantes.\nPor favor, aguarde..", "solucao_blue_12": "Finalizámos a avaliação com sucesso!\nAgora pode visualizar os resultados.", "solucao_blue_13": "A recolher dados", "solucao_blue_14": "Finalizar a<PERSON>", "health_screen_evaluations": {"zero": "Não tens nenhuma avaliação no ecrã de saúde", "one": "Tens {} avaliação de bioimpedância no ecrã de saúde", "other": "Tens {} avaliações de bioimpedância no ecrã de saúde"}, "conectar_balanca_47": "<PERSON>ura inválida, deve ser igual ou superior a 1 metro e inferior a 2,5 metros.", "erro_saude_cpf_2": "CPF já cadastrado em outra conta.", "erro_saude_email_2": "E-mail já cadastrado em outra conta.", "erro_saude_email": "E-mail não informado no seu cadastro.", "erro_saude_cpf": "CPF não informado no seu cadastro.", "conectar_balanca_46": "Massa Muscular", "conectar_balanca_45": "Gordura visceral", "scale_bf1000_uso_content": "1. Suba na balança com os pés descalços, posicionando-os corretamente nos eletrodos (se for uma balança com análise de composição corporal).\n2. Fique parado e aguarde até que a medição seja concluída e os resultados apareçam no visor.\n3. Verifique os dados na aplicação de saúde (se conectado) ou anote os resultados diretamente no visor da balança.", "scale_xiaomi_uso_content": "1. Coloque a balança numa superfície plana e estável.\n2. Ligue a balança pressionando levemente com o pé até que ela ligue.\n3. Conecte a balança à aplicação Mi Fit no seu smartphone.\n4. Suba na balança com os pés descalços e fique parado até que a medição seja concluída.\n5. Verifique os resultados na aplicação.", "conectar_balanca_44": "Nova bioimpedância", "conectar_balanca_43": "Altura inválida", "conectar_balanca_42": "* Sedentário (1) a Super Ativo (5)", "conectar_balanca_41": "Nível de treino", "contratos_retry_ultima_tentativa": "Última tentativa... (3/3)", "contratos_retry_tentando_novamente": "A tentar novamente... ({}/3)", "contratos_erro_carregar_timeout": "Não foi possível carregar os contratos. Tente novamente mais tarde.", "contratos_timeout_consulta": "Timeout: A consulta não respondeu em 10 segundos", "contratos_erro_nenhum_contrato": "<PERSON><PERSON>, ainda não há nenhum contrato por aqui", "contratos_escolha_contrato": "Escolha um contrato", "contratos_renovar_contrato": "<PERSON><PERSON> contrato", "contratos_erro_falha_final": "Não foi possível carregar os contratos após 3 tentativas", "conectar_balanca_40": "Grau de atividade física", "conectar_balanca_39": "Dados de treino", "conectar_balanca_38": "Dados Físicos", "conectar_balanca_0": "Lista de dispositivos compativeis", "treino_quse_pronto_content_b": "O treino gerado está aguardando validação de um professor", "ia_em_breve_entegue": "Em breve será entregue", "professor_ira_validar_treino_ia": "Aguarde que um professor ir<PERSON> validar o seu treino e liberar o acesso para começar a treinar.", "treino_quase_pronto": "O seu treino está quase pronto!", "treino_quse_pronto_content": "O treino gerado está aguardando validação do professor, o que pode levar até", "ia_criando_seu_treino": "Nossa inteligencia artificial esta criando seu treino, aguarde um momento", "conectar_balanca_1": "Conectar a balança", "conectar_balanca_2": "Selecionar balança", "conectar_balanca_5": "Erro na avaliação, tente novamente, se o dispositivo não aparecer listado desligue e ligue novamente o bluetooth", "conectar_balanca_7": "Erro: ", "conectar_balanca_8": "Ative a balança e conecte ao seu usuário", "conectar_balanca_9": "Não identificado", "conectar_balanca_10": "Nenhum dispositivo encontrado.", "conectar_balanca_11": "Por favor, ligue o Bluetooth do seu celular e certifique-se de que o dispositivo está ligado.", "conectar_balanca_12": "Ver dispositivos compatíveis", "conectar_balanca_13": "Stream finalizado.", "conectar_balanca_15": "Autorize o aceso ao bluetooth para que o celular possa conectar a balança", "conectar_balanca_16": "<PERSON><PERSON><PERSON> acesso", "conectar_balanca_18": "Instruções", "conectar_balanca_19": "Instruções para o exame", "conectar_balanca_20": "Aguarde a luz do visor acender e oriente o aluno a subir na balança descalço, sem acessórios.", "conectar_balanca_21": "Finalizar bioimpedância", "conectar_balanca_22": "Avaliação finalizada", "conectar_balanca_23": "Peso", "conectar_balanca_24": "Altura", "conectar_balanca_25": "Imc", "conectar_balanca_26": "<PERSON><PERSON>", "conectar_balanca_27": "<PERSON><PERSON><PERSON>", "conectar_balanca_28": "Água", "conectar_balanca_29": "tmb", "conectar_balanca_30": "<PERSON><PERSON>", "conectar_balanca_31": "Falha ao salvar a avaliação física.", "conectar_balanca_32": "A lista de dispositivos compatíveis é atualizada constantemente, por favor, verifique se o seu dispositivo está na lista.", "conectar_balanca_33": "Dispositivos compatíveis:", "conectar_balanca_34": "Beurer BF1000", "conectar_balanca_35": "Xiaomi Scale Composition 2", "conectar_balanca_36": "<PERSON><PERSON><PERSON>", "conectar_balanca_37": "Bluetooth Desativado", "recurso_health_nao_disponivel_8": "Recurso indisponível para esta versão do Android.", "recurso_health_nao_disponivel_7": "Transferir Google Fit", "recurso_health_nao_disponivel_6": "Ok, entendi!", "recurso_health_nao_disponivel_5": "Agradecemos a sua compreensão.", "recurso_health_nao_disponivel_4": "2. Considerar outras formas de acompanhar os seus indicadores de saúde, como a aplicação Google Fit.", "recurso_health_nao_disponivel_3": "1. Verificar se há uma atualização de sistema disponível para o seu dispositivo.", "recurso_health_nao_disponivel_2": "Para continuar a monitorizar a sua saúde, pode:", "recurso_health_nao_disponivel_1": "A nossa aplicação utiliza tecnologias compatíveis com dispositivos Android 10 ou superior para fornecer indicadores de saúde de forma precisa e segura. Infelizmente, algumas versões anteriores do Android não suportam estes recursos.", "sem_recurso_health5": ", atualize o seu sistema para Android 10 ou superior.", "sem_recurso_health4": "Quantidade de passos", "sem_recurso_health3": " e ", "sem_recurso_health2": "Calorias gastas", "sem_recurso_health1": "Para visualizar os seus indicadores de ", "acao_permitir": "<PERSON><PERSON><PERSON>", "heatlh_content": "Para aceder aos dados de saúde, é necessário permitir o acesso aos dados de fitness. Deseja permitir?", "heatlh_titulo": "Autorização necessária", "treino_ia_body_builder": "Sou bodybuilder ou atleta profissional", "treino_ia_entre_1_ano_e_dois": "Já treino entre 1 ano e 2 anos", "treino_ia_menos_6_meses": "Treino há menos de 6 meses", "treino_ia_treino_2_anos": "<PERSON><PERSON> treino há mais de 2 anos", "treino_ia_treino_6_meses": "<PERSON>á treino entre 6 meses e 1 ano", "treino_ia_estouvoltando": "Estou voltando a treinar agora", "treino_ia_treinou_voltando": "<PERSON><PERSON> treinei antes, estou voltando", "treino_ia_nunca_treinou": "<PERSON>unca tre<PERSON>i", "ask_tempo_de_treino": "Quantos minutos por dia tens disponível para treinar?", "splash_acao_user_content": "O seu utilizador está desatualizado. Você precisa logar novamente para que possamos atualizar os seus dados.", "splash_acao_user": "Ação necessária", "espere_splash_screen3": "<PERSON>da estamos a carregar, mais um instante...", "espere_splash_screen2": "A preparar tudo para si...", "espere_splash_screen1": "<PERSON>uase l<PERSON>, só um momento...", "espere_splash_screen0": "A carregar, por favor aguarde...", "aval_fisica_sem_altura_content": "Procure o balcão de atendimento para atualizar a sua altura", "aval_fisica_titulo_sem_altura": "Avaliação incompleta", "splash_preso_recarregar_app": "Recarregar App", "splash_preso_refazer_login": "<PERSON><PERSON><PERSON> login", "splash_preso_mensagem": "Tente fazer login novamente", "splash_preso_titulo": "Não conseguimos carregar o seu utilizador", "desativado_cliente_disclaimer": "A sua conta ou ginásio está inativo neste momento. Para mais informações ou para reativar o seu acesso, por favor, contacte a receção do ginásio.", "desativado_cliente_titulo": "A empresa {} já não usa o Treino", "consultar_aval_recarregar": "<PERSON><PERSON><PERSON><PERSON> dad<PERSON>", "consulta_aval_erro_mensagem": "Para tentar novamente, atualiza a página deslizando para baixo ou toca no botão de recarregar.", "consulta_aval_erro_titulo": "Não foi possível carregar as avaliações físicas!", "n2bContent": "Pergunte no balcão da sua academia como ter consultas online com nutricionistas!", "n2b_titulo_rev": "Sua evolução te aguarda", "n2b_titulo_venda_content": "Tenha consultas online com nutricionistas aqui no seu app", "n2b_titulo_venda_video": "Agende sua consulta", "n2b_euquero_cta": "Eu quero", "com_qual_genero": "Qual é o seu sexo biológico?", "marcar_all": "<PERSON><PERSON>", "desmarcar_all": "<PERSON><PERSON><PERSON> to<PERSON>", "confpushIa_description": "Configure as notificações com base no seu horário de serviço", "confpushIa_titulo": "Personalizar notificaç<PERSON>", "horariostr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configpushdiasSemana": "<PERSON><PERSON> da semana", "diastr": "<PERSON><PERSON>", "configpushdiaselecionado": "Dia já está selecionado em outro agrupamento", "configpushdia": "O número do dia deve estar entre 1 e 7.", "copiandotr": "Copiado", "configpush": "Configurações de push", "trocarExercicio": "Alterar Exerc<PERSON>", "ia.erroTroca": "Não foi possível trocar a atividade.", "ia.tentenovamente": "Tente novamente dentro de alguns minutos.", "profIA": "Treino IA", "search": "<PERSON><PERSON><PERSON><PERSON>", "novoLogin_localizacao_1": "Corrigir dados do utilizador", "novoLogin_localizacao_2": "Experimentar outro método de login", "novoLogin_localizacao_3": "Enviar ligação de login única", "novoLogin_localizacao_4": "Erro ao enviar ligação", "novoLogin_localizacao_5": "Palavra-passe incorreta", "novoLogin_localizacao_6": "Não foi encontrado nenhum utilizador associado a este email", "novoLogin_localizacao_7": "Código incorreto", "novoLogin_localizacao_8": "<PERSON><PERSON><PERSON> expirado", "novoLogin_localizacao_9": "Utilizador não encontrado", "novoLogin_localizacao_10": "Erro ao enviar SMS", "novoLogin_localizacao_11": "Ocorreu um erro ao enviar a ligação de login, por favor tente novamente. Se o erro persistir, experimente outro método de login.", "novoLogin_localizacao_12": "A palavra-passe inserida está incorreta. Por favor, tente novamente ou faça login enviando uma ligação de login única.", "novoLogin_localizacao_13": "O email inserido “<EMAIL>” não está associado a nenhum utilizador na nossa base de dados, por favor verifique o email e tente novamente.", "novoLogin_localizacao_14": "O código inserido está incorreto. Por favor, reveja-o ou tente outra forma de login.", "novoLogin_localizacao_15": "O código inserido atingiu o limite de tempo para confirmação. Por favor, reenvie o código e tente novamente.", "novoLogin_localizacao_16": "O utilizador fornecido não foi encontrado, por favor verifique os dados introduzidos e tente novamente.", "novoLogin_localizacao_17": "Ocorreu um erro ao enviar o SMS com o código de autenticação, por favor tente novamente. Se o erro persistir, experimente outro método de login.", "novoLogin_localizacao_18": "Email enviado com sucesso!", "novoLogin_localizacao_19": "aceitouNovosTermos", "novoLogin_localizacao_20": "refUtilizador", "novoLogin_localizacao_21": "depuração", "novoLogin_localizacao_22": "A conta de utilizador foi desativada por um administrador.", "novoLogin_localizacao_23": "Ups!", "novoLogin_localizacao_24": "Login com email", "novoLogin_localizacao_25": "infoLoginUtilizador", "novoLogin_localizacao_28": "Selecionar utilizador", "novoLogin_localizacao_29": "Validação", "novoLogin_localizacao_30": "Login com conta Apple", "novoLogin_localizacao_31": "Conecte a sua conta Apple a um email real", "novoLogin_localizacao_32": "Para continuar, é necessário associar o seu email a um email real para que possamos encontrar o seu utilizador.", "novoLogin_localizacao_33": "Introduza o seu email", "novoLogin_localizacao_34": "Insira o seu email", "novoLogin_localizacao_35": "btnEditarEmail", "novoLogin_localizacao_36": "btnContinuar", "novoLogin_localizacao_37": "Continuar com Google", "novoLogin_localizacao_38": "Continuar com Facebook", "novoLogin_localizacao_39": "Continuar com Apple", "novoLogin_localizacao_40": "Login com utilizador", "novoLogin_localizacao_41": "Ups!", "novoLogin_localizacao_42": "<PERSON><PERSON>-vindo à {}", "novoLogin_localizacao_43": "A sua jornada de fitness começa agora!", "novoLogin_localizacao_44": "Escolha a sua melhor opção de login, lembre-se de utilizar o mesmo endereço de email fornecido à academia para continuar.", "novoLogin_localizacao_45": "Login com utilizador", "novoLogin_localizacao_46": "Ficou com alguma dúvida?", "novoLogin_localizacao_47": "Validar o código", "novoLogin_localizacao_48": "Informe o código de 6 dígitos que foi enviado para o email \"{}\"", "novoLogin_localizacao_51": "Telefone", "novoLogin_localizacao_53": "Introduza o seu email ou utilizador", "novoLogin_localizacao_54": "Enviar código por email", "novoLogin_localizacao_55": "Opção mais segura", "novoLogin_localizacao_56": "Introduzir palavra-passe", "novoLogin_localizacao_57": "Via SMS", "novoLogin_localizacao_58": "Código enviado", "novoLogin_localizacao_59": "Informe o código de 6 dígitos que foi enviado para o email “{}” ou clique na ligação de login.", "novoLogin_localizacao_60": "Ver todos", "novoLogin_localizacao_61": "Ajuda com o login", "novoLogin_localizacao_62": "Utilizador não encontrado, por favor, reveja os dados inseridos", "novoLogin_localizacao_63": "Não foi encontrado nenhum utilizador associado a este email.", "push.link.titulo": "Esta notificação leva a um link externo", "push.link.disclaimer": "<PERSON><PERSON>, sair<PERSON> da aplicação", "push.link.voltar": "Voltar à aplicação", "push.link.acessar": "Aceder ao link", "text_not_ative_atz": "Ative as notificações e receba as novidades e dicas no seu telemóvel!", "text_not_nao_perca_nenhuma_atz": "Não perca nenhuma atualização importante e esteja sempre a par de tudo no seu negócio.", "text_not_ative_todas_atz": "Ativar todas as notificações", "saude.aval.detalhes.percentualGordura": "Percentual de Gordura total", "Percentual_de_Massa_magra": "Percentagem de Massa Magra", "peso_real_desc_abre": "Considera-se saudável que o seu peso varie entre {}kg e {}kg", "Historico_de_bioimpedância": "Histórico de Bioimpedância", "saudepesoreal": "Peso Real", "saudecomparativodepesos": "Comparação de Pesos", "saudeindicemassacorporeaimc": "<PERSON><PERSON><PERSON> (IMC)", "saudeanalisesegmentar": "<PERSON><PERSON><PERSON><PERSON>", "saudemassamagra": "<PERSON><PERSON>", "saudegorduracorporal": "Gordura corporal", "saudeaguacorporal": "Água corporal", "saudemassaossea": "<PERSON><PERSON>", "desc_comparativo_pesos": "Aqui pode-se comparar o percentual de massa magra e de gordura que compõe seu peso total. Ou seja, do seu peso atual, quanto é gordura e quanto é massa magra. <PERSON>da dentro de gordura, há a diferenciação percentual entre gordura total e gordura visceral (a que merece mais atenção, vez que esta se acumula na cavidade abdominal, abaixo da camada dos músculos, e fica próxima aos órgãos vitais).", "desc_saudePesoReal": "É a junção total de massa acumulada no corpo. É composta por gordura, massa magra, água corporal e tecido ósseo.", "desc_saudeIndiceDeMassaCorporeaIMC": "O Índice de Massa Corpórea é calculado dividindo o seu peso (em quilos) pela sua altura (em metros) ao quadrado. É utilizado para verificar se o peso corporal está acima ou abaixo do ideal. <PERSON><PERSON><PERSON>, esse índice não diz muito, precisamos considerar outros marcadores.", "desc_saudeGorduraCorporal": "É a quantidade total de gordura distribuída por todo o nosso corpo. Ela é medida em percentagem, e o valor é diferente para homens e mulheres. Próteses de silicone podem ser somadas nesse resultado.", "desc_saudeMassaDeGordura": "É a quantidade total, em quilos, de gordura sob a pele, visceral e em volta dos músculos. O valor é diferente para homens e mulheres. Próteses de silicone podem ser somadas ao resultado.", "desc_saudeMassaMagra": "É a quantidade total de massa muscular distribuída pelo nosso corpo. Está presente nos músculos card<PERSON>, viscerais e esqueléticos e é definida em quilos.", "desc_saudeAguaCorporal": "É a quantidade total de água no corpo, incluindo o total de água dentro e fora das nossas células. Pode identificar a retenção de líquidos ou a desidratação.", "desc_saudeGorduraVisceral": "É toda a gordura que protege nossos órgãos vitais. Quando está em excesso, essa gordura se acumula no fígado e no abdômen.", "desc_saudeAnaliseSegmentar": "A análise segmentar mostra como está a distribuição de gordura e massa muscular nos braços, tronco e pernas. Não inclui gordura ou músculo em órgãos internos ou cardíacos.", "desc_saudeTaxaMetabolicaBasalTMB": "É a quantidade de calorias que o corpo precisa para manter as suas funções básicas, como respiração e digestão. É medida em calorias por dia e, quando somada ao nível de atividade física, ajuda o nutricionista a definir sua necessidade calórica e a elaborar um plano alimentar adequado.", "descricao_calculadora": "Altere o peso de referência e veja a percentagem do seu exercício.", "acessar": "Acessar", "unable_to_launch_whatsapp": "Não foi possível abrir o WhatsApp", "mensagem_whatsapp": "<PERSON><PERSON><PERSON>, {}. <PERSON><PERSON> <PERSON> o(a) {}.", "select_your_gym": "Selecione a sua academia", "select_your_box": "Selecione o seu box", "search_for_your_gym": "Procure pela sua academia", "unable_to_find_gym": "Não encontramos o termo pesquisado, verifique e tente novamente.", "gym_units": {"one": "{} unidade", "two": "{} unidades", "many": "{} unidades", "other": "{} unidades"}, "login_with_phone_number": "Aceder com o número de telefone", "continue": "<PERSON><PERSON><PERSON><PERSON>", "advance": "<PERSON><PERSON><PERSON><PERSON>", "submit_form": "Enviar formul<PERSON>", "no_internet_connection": "Sem ligação à internet", "login_with_email": "Aceder com e-mail ou nome de utilizador", "by_clicking_continue": "Ao clicar em continuar, concorda com os", "terms_of_service_and_privacy_policy": "\ntermos de serviço e política de privacidade", "by_tapping_on_swap": "Ao tocar em trocar, será redirecionado para a tela de seleção de academias.", "swap_gym": "Trocar academia", "go_back": "Voltar", "swap_gym?": "Trocar academia?", "got_it": "<PERSON><PERSON><PERSON>", "are_you_not_a_member_yet": "Ainda não é membro?", "user": "E-mail ou nome de utilizador", "password": "<PERSON><PERSON>", "atention": "Atenção", "email_and_password_must_be_fulfilled": "O e-mail e a senha devem ser preenchidos corretamente", "user_not_found": "Utilizador não encontrado. Digitou corretamente o e-mail e a senha?", "incorrect_data": "Dados incorretos!", "or": "OU", "forgot_my_password": "<PERSON><PERSON><PERSON> a senha", "welcome_back": "Que bom ter você aqui", "usuario_recente": "Utilizadores recentes", "login_with_another_user": "Entrar com outro utilizador", "dados_nao_disponiveis": "Dados não disponíveis", "are_you_sure_you_want_to_remove_user": "Tem a certeza que deseja remover este utilizador dos dados guardados?", "delete": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove_user": "Remover utilizador?", "collaborator": "Colaborador", "member": "Membro", "WODSTRINGS": "", "no_wod_registered": "Sem WOD registado", "unfortunately_there_are_no_wods": "Infelizmente, não temos nenhum WOD \ncadastrado até o momento", "your_contract_does_not_allow_you_cross": "O seu contrato não permite que aceda aos dados do Cross. Pode solicitar falando com o seu consultor.", "ops_you_cant_get_access_to_cross": "Ops! Não tem acesso ao Cross", "calendar": "<PERSON><PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>-me", "see_more": "<PERSON><PERSON>", "no_results_for_the_rank": "Sem resultados para o ranking", "be_the_first_one_to_register": "<PERSON>ja o primeiro a registar o seu resultado \ne garanta o seu lugar no ranking.", "notifications": "Notificações", "there_has_been_an_error_processing_your_notifications": "Houve um erro ao processar as suas notificações", "pull_down_to_try_again": "Puxe para baixo para tentar novamente", "there_are_no_notifications_so_far": "Sem notificações disponíveis até agora", "home_training": "Treino em casa", "home_training_title": "Treino em casa", "live": "Ao vivo", "unable_to_load_home_training": "Não foi possível carregar o treino em casa", "check_your_internet_connection_pull_down": "Verifique a sua ligação à internet, puxe para baixo para tentar novamente", "ops_access_denied": "Ops! Acesso Negado", "it_was_not_possible_to_validate_your_situation_with_the_gym": "Não foi possível validar a sua situação com a academia. Entre em contato com a administração para resolver.", "type_the_name_of_the_training": "Digite o nome do treino", "news": "Novidades", "train_again": "Treinar novamente", "where_are_the_videos?": "Onde estão os vídeos?", "unable_to_find_any_video_with_the_filters": "Com os filtros selecionados, não encontramos nenhum vídeo. Que tal experimentar outros filtros?", "we_are_out_of_videos_at_the_moment": "Estamos sem vídeos no momento!", "no_videos_available_right_now": "No momento não há vídeos disponíveis. Aguarde!", "no_class_registered": "No momento não há aula cadastrada.", "there_has_been_an_error_consulting_live_classes": "Houve um erro ao consultar as aulas ao vivo", "we_are_out_of_live_trainings_right_now": "Estamos sem treinos ao vivo no momento", "no_online_training_registered": "Nenhum treino online cadastrado!", "rank": "Ranking", "me": "Eu", "workouts_executed": "Treinos executados", "loading": "A carregar dados", "start_right_now": "Comece agora mesmo", "total_of": "Total de", "total_of_1_workout": "Total de 1 treino", "workouts": "treinos", "with_a_total_of": "Com um total de", "access": "Acessar", "do_you_like_challenges?": "Gosta de desafios?", "participate_in_your_gym's_rank": "Participe no ranking da sua academia! Agora, o impacto do seu treino será muito maior!", "follow_your_progress": "Acompanhe o seu progresso", "and_your_gym_partners'_too": "E também o dos seus colegas. Uma ótima oportunidade para gerar incentivo e motivação!", "a_new_rank_every_month": "Todo mês um novo ranking", "work_out_5_times_and_get_the_bronze_medal": "Realize 5 treinos e ganhe a medalha de bronze; com 15 treinos, a medalha de prata; e acima de 25 treinos, a medalha de ouro.", "did_i_hear_rewards?": "Eu ouvi prêmios?", "your_points_can_be_redeemed_for_rewards": "Os seus pontos podem ser trocados no Clube de Vantagens da sua academia. Quanto mais treinar, mais pontos acumulará!", "my_appointments": "Os meus agendamentos", "available": "Agendamentos disponíveis", "no_appointments": "Sem agendamentos", "no_appointments_available": "Sem agendamentos disponíveis", "there_has_been_an_error_processing_your_appointments": "Falha ao consultar agendamentos", "no_appointments_available_so_far": "Até agora, sem agendamentos disponíveis", "try_other_dates_toggling_the_days_in_the_calendar_above": "Experimente outras datas alterando os dias no calendário acima", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "instructor:": "Instrutor: ", "at_time": "<PERSON><PERSON><PERSON><PERSON>:", "cancel_appointment": "Cancelar agendamento", "schedule": "Agendar", "by_clicking_confirm_you_will_be_scheduled_to_the_selected_appointment": "Ao clicar em confirmar, será adicionado ao agendamento selecionado.", "would_you_like_to_confirm_the_appointment": "Deseja confirmar o agendamento?", "you_can_reschedule_or_cancel_this_appointment": "<PERSON><PERSON> remarcar ou cancelar este agendamento.", "reschedule": "Reagendar", "what_do_you_want_to_do?": "O que deseja fazer?", "month": "mês", "meses": "Meses", "week": "semana", "confirm": "Confirmar", "days_with_available_schedules": "Dias com horários disponíveis", "no_schedules_for_this_day": "Sem horários para este dia", "success": "Sucesso", "appointment_rescheduled": "Agendamento reagendado", "nutrition": "Nutrição", "error": "Erro", "today,": "<PERSON><PERSON>,", "stay_in": "Fique por dentro!", "make_your_diet_totally_customized": "Crie uma dieta totalmente personalizada!", "choose_what_you_will_eat_when": "Escolha o que vai comer, quando comer e quantas refeições deseja fazer por dia. Com base nessas informações, ofereceremos a melhor opção de acordo com seus objetivos.", "step": "Passo", "of": "de", "your_plan_is_ready": "O seu plano está pronto!", "see_more_details_of_your_journey": "<PERSON><PERSON>a mais de<PERSON><PERSON> da sua jornada", "is_your_goal": "é o seu objetivo", "keep_up_the_focus_on_your_plan": "Mantenha o foco total no seu plano para alcançá-lo. Confira também dicas exclusivas no “Fique por dentro”!", "it_will_last_30_days": "Terá a duração de 30 dias!", "enough_time_for_neither_you": "Tempo suficiente para nem você, nem o seu organismo enjoarem", "it_only_depends_on_you": "Depende só de você!", "for_it_to_work_it_takes_100": "Para dar certo, é preciso 100% de compromisso e empenho", "lets_start_off": "Vamos começar!", "conclude": "Concluir", "get_my_plan": "Obter o meu plano", "shopping_list": "Lista de Compras", "my_hydration": "<PERSON>a <PERSON>", "adjust_your_plan": "Ajustar plano", "reminders": "<PERSON><PERSON><PERSON>", "we_need_you_to_set_a_time_gap": "Precisamos que defina um intervalo de tempo para que possamos gerar os ingredientes necessários para o seu plano durante o período.", "set_time_duration": "Definir Intervalo de Tempo", "set_period": "Definir intervalo", "weekly_shopping_list": "*🛒 Lista de Compras da semana* \n", "before_getting_access_to_your_shopping_list": "Antes de aceder à sua lista de compras", "yuo_need_to_start_off_your_nutritional_plan": "Precisa iniciar o seu plano nutricional confirmando as receitas que gerámos para si.", "nutritional_plan": "Plano Nutricional", "begin_plan": "Começar plano", "type_here": "Digite aqui", "modify_quantity": "Alterar quantidade", "it_has_been_a_while_since_you_last_checked": "Já faz algum tempo desde a última vez que conferiu a sua lista de compras. Gostaria de atualizá-la?", "update_list": "<PERSON><PERSON><PERSON><PERSON> lista", "not_now": "<PERSON><PERSON><PERSON> n<PERSON>", "update_your_list": "Atualize a sua lista", "water_consumption": "Consumo de <PERSON>gua", "are_you_sure_you_want_to_leave_without_confirming_the_changes": "Se sair sem confirmar as alteraç<PERSON><PERSON>, elas serão perdidas. Tem a certeza que deseja sair sem confirmá-las?", "leave_without_saving_the_changes": "<PERSON><PERSON> sem confirmar as alteraç<PERSON><PERSON>", "cup's_capacity": "Capacidade do copo", "daily_goal": "<PERSON><PERSON>", "quantity_sufficient_according_to_your_age": "Quantidade suficiente de acordo \ncom a sua idade e peso.", "quantity_insufficient_according_to_your_age": "Quantidade insuficiente de acordo \ncom a sua idade e peso.", "reminder": "Le<PERSON><PERSON>", "tea_cup": "Ex.: X<PERSON><PERSON><PERSON>", "disposable_cup": "Ex.: <PERSON><PERSON>", "small_cup": "Ex.: <PERSON><PERSON>", "mug": "Ex.: Caneca", "medium_cup": "Ex.: <PERSON><PERSON>", "large_cup": "Ex.: <PERSON><PERSON>", "extra_large_cup": "Ex.: <PERSON><PERSON>", "bottle_of_water": "Ex.: Garra<PERSON> de <PERSON>", "squeeze": "Ex.: Squeeze", "copo_duplo_medio": "Ex.: <PERSON><PERSON>", "customized_size": "<PERSON><PERSON><PERSON>", "lose_weight": "Emagrecer", "keep_my_weight": "Manter o peso", "gain_muscle_mass": "<PERSON><PERSON><PERSON> massa muscular", "your_current_weight_is": "O seu peso atual é", "my_goal": "Meu objetivo", "select_your_goal": "Selecione o objetivo", "are_you_sure_you_want_to_change_your_goal": "Tem certeza que deseja mudar o seu objetivo? Vamos precisar recalcular o seu plano", "will_you_change_your_goal": "Você vai mudar o seu objetivo?", "reset_plan": "Resetar plano", "your_current_progress_will_be_lost": "Ao fazer isso, todo o seu progresso será perdido, e você começará o seu plano \ndo zero. Tem certeza que deseja resetá-lo?", "will_you_reset_your_plan": "Você vai resetar o seu plano?", "your_plan_has_been_recalculated": "Seu plano foi recalculado para atender aos seus novos objetivos", "let's_go": "Vamos lá", "objectve_changed_successfully": "Objetivo alterado com sucesso!", "breakfast": "Café da Manhã", "brunch": "<PERSON><PERSON><PERSON> da Man<PERSON>", "lunch": "Almoço", "afternoon_snack": "<PERSON><PERSON><PERSON>", "dinner": "<PERSON><PERSON>", "supper_meal": "<PERSON><PERSON>", "remember_to_drink_water": "Lembre-se de beber á<PERSON>", "regard_the_cooking_time": "Levar em consideração o tempo de preparo", "the_reminder_will_be_sent_to_you": "O lembrete será enviado para você, levando em consideração o tempo de preparo e o horário da sua próxima refeição. Assim, dando tempo para você prepará-las.", "repeat_tutorial:": "<PERSON><PERSON><PERSON> o tutorial guiado", "remake": "<PERSON><PERSON><PERSON>", "guided_tutorial": "Tutorial guiado", "remake_guided_tutorial": "Refazer tutorial guiado", "other_informations": "Outras informações", "rate_this_help": "Avalie esta ajuda", "did_we_get_to_help_you_with_your_doubts": "Conseguimos ajudar com as suas dúvidas?", "explanatory_video": "Vídeo explicativo", "by_signing_up_for_premium": "Ao assinar o recurso premium do seu aplicativo, você garante recursos extras que vão ajudar a potencializar os resultados com os treinos e aulas da sua academia.", "how_to_start_my_nutritional_plan": "Como começar meu plano nutricional?", "to_start_your_customized_nutritional_plan": "Para começar o seu plano nutricional personalizado, siga estes passos:", "i_didn't_like_one_of_the_suggested_recipes": "<PERSON>ão gostei de uma receita sugerida. Posso trocar?", "yes_you_can_after_the_plan_has_been_generated": "Sim! Depois do plano gerado, você chegará na etapa de confirmar as receitas sugeridas pelo aplicativo. Caso uma não te agrade, clique em “N<PERSON> gostei” para navegar por outras opções e selecionar aquelas que mais te agradam, ainda dentro do seu plano adequado às suas necessidades e hábitos alimentares. Se quiser ainda experimentar novas receitas, vá em “Mais receitas” na seção inferior do seu Nutrição e explore as variedades disponíveis!", "how_to_register_my_meal": "Como registrar as minhas refeições?", "when_your_plan_has_been_generated_your_meals": "Com o seu plano gerado, suas refeições em cada período do dia vão aparecer em “Minhas refeições”. De acordo com o horário definido por você, clique em “Registrar” na refeição daquele momento. O app irá perguntar se seguiu a receita indicada pelo plano. Caso não, clique em “Não” e procure pelos alimentos que ingeriu no lugar daqueles indicados.", "how_does_the_shopping_list_works": "Como funciona a lista de compras?", "after_your_plan_has_been_generated_and_your_meals_confirmed": "Após o seu plano ser gerado e as refeições confirmadas, você pode acessar sua lista de compras no ícone superior à direita (︙). Ela é gerada automaticamente, de acordo com as receitas do seu plano atual, e o período de tempo que precisará dos ingredientes. Você confirma os ingredientes já adquiridos e também pode compartilhar a lista para enviar direto para o seu Whatsapp", "how_can_i_follow_my_results": "Onde acompanho meus resultados?", "click_see_more_in_the_your_plan": "Na seção inicial do Nutrição, em “Seu Plano”, clique em “Ver Mais” para conferir seus resultados de acordo com o que você registrou no aplicativo. O próprio aplicativo já faz os cálculos em relação ao que foi registrado e forma as estatísticas e gráficos.", "how_will_i_remember_when_should_i_eat": "Como vou lembrar de quando comer ou beber á<PERSON> para seguir as minhas metas?", "in_the_upper_right_icon_click_reminders": "No ícone superior à direita (︙), clique em “Lembretes” e acione ou desative as notificações que precisar para não esquecer de nenhuma etapa do plano.", "does_the_premium_signature_fit_me": "O Premium é adequado para mim?", "if_you_are_looking_for_a_nutritional_support": "Se você procura um apoio nutricional aliado com seu treino e suas necessidades, quer auxílio para se hidratar da maneira ideal e ter gráficos completos sobre a sua evolução em relação ao seu objetivo fitness, o Premium é mais do que perfeito para você. Com ele você ganha o apoio que precisa para ter foco e alcançar seus objetivos.", "can_i_have_more_than_one_plan": "Posso ter mais de um plano ativo?", "no_just_one_at_a_time": "<PERSON><PERSON>, apenas um por vez. Caso interrompa um e opte por outro, ao terminar este, poderá dar continuidade de onde parou no antigo.", "how_can_i_switch_my_plan": "Como trocar de plano?", "in_more_plans_at_the_end_of_nutrition_page": "Em “Mais planos” nas seções finais do seu Nutrição é possível ver outras opções para seguir. Ao clicar em “Começar”, o seu plano atual será interrompido e este novo começará do zero.", "can_i_change_my_weight_goal": "Posso alterar minha meta de peso?", "yes_you_just_got_to_go_to_the_upper_right_icon": "Sim! Basta ir no ícone superior à direita (︙) e clicar em “Ajustar plano”. <PERSON> quiser, pode ajustar também o seu objetivo no mesmo local.", "can_i_change_my_hydration_goal": "Posso alterar minha meta de consumo de água?", "yes_just_click_my_hydration": "Pode sim! Basta ir no ícone superior à direita (︙) e clicar em “Minha hidratação” para atualizar o seu consumo ideal de água.", "where_can_i_find_the_health_and_nutrition_tips": "Onde tenho acesso às dicas de saúde e nutrição?", "navigate_to_the_lower_section_in_nutrition": "Navegue até a seção inferior do seu Nutrição e clique em “Dicas rápidas” para ficar por dentro de todos os conteúdos e novidades!", "learn_more": "Saiba mais sobre", "what_is_the_workout's_feedback": "O que é o feedback dos treinos?", "through_the_feedback_you_can_view_the_ratings": "No feedback, você pode visualizar a avaliação que seus alunos dão aos treinos que eles executam. Também mostra a porcentagem de treinos executados por eles durante 7 dias, ou seja, você tem dados sobre a satisfação dos seus alunos com os treinos, assim como a consistência deles em seus treinamentos. Lembrando que os relatórios são sobre todos os alunos no geral.", "what_are_the_instructions": "O que são as prescrições?", "this_card_works_as_a_reminder": "Esse card serve como um lembrete. Nele destacamos quais treinos ainda não foram passados e ainda precisam ser feitos (Total a fazer); os treinos que venceram e precisam ser renovados (Renovações); e os alunos que ainda não têm treinos cadastrados (Sem treino).", "what_is_the_members": "O que é alunos?", "on_this_tab_you_can_consult": "Nesta aba, é possível consultar de forma rápida os status dos seus alunos: alunos ativos, alunos inativos e o total de alunos que você tem no aplicativo.", "basic_program": "Programas base", "what_are_the_workouts_exhibited_here": "O que são esses treinos que aparecem aqui?", "these_are_the_pre_set_workout": "<PERSON><PERSON><PERSON> as fichas pré-definidas, fichas com atividades adicionadas. São as fichas pré-definidas que já vêm no app, mas também é possível cadastrar novas fichas pré-definidas para você usar mais tarde.", "how_can_i_create_a_new_pre_set_workout": "Como faço para criar uma nova ficha modelo?", "by_clicking_in_the_plus_icon": "Clicando no símbolo de mais (+), você começa a criar uma nova ficha.\n \nPasso 1: cadastre os dados da ficha (nome, observação, sexo, categoria, nível, tipo de execução) e clique em “Avançar”. \nPasso 2: depo<PERSON> de salvar, você pode editar seu treino, gerar um padrão de série e adicionar atividades. \nPasso 3: adicione atividades clicando em “Adicionar”. Busque as atividades que você quer pelo nome e as adicione clicando no V ao lado. Quando o botão ficar verde, é porque foi selecionado. Quando terminar, clique em “Adicionar \nexercícios” para adicionar as atividades no treino. \nPasso 4: caso queira editar sua atividade, basta clicar no nome da atividade adicionada ao seu treino. Depois, você pode alterar série, repetições, carga, cadência e descanso. <PERSON><PERSON><PERSON> disso, é possível selecionar um método de execução (caso você tenha mais de uma atividade, é possível gerar um padrão para todas, colocando série, repetições e descanso padrão para todas as atividades). Ao finalizar, clique em “Salvar” para adicionar as edições da atividade. \nPasso 5: para terminar, clique em “Salvar” para adicionar seu novo treino. Escolha se você vai adicionar essa ficha a algum aluno ou apenas salvá-la como modelo para usar sempre que precisar.", "can_i_add_this_workout_to_many_members_at_the_same_time": "Posso adicionar essa ficha para vários alunos ao mesmo tempo?", "you_can_only_select_one_member_at_a_time": "Só é possível selecionar 1 aluno por vez para receber sua ficha modelo. Caso queira enviá-la para mais aluno<PERSON>, volte no treino para prescrever a um de cada vez.", "member_details": "Dados do aluno", "how_to_create_a_workout_program": "Como criar um programa de treino?", "after_selecting_a_member": "Depois de selecionar o aluno, no final da página, você encontrará o local para criar o programa de treino. Clique em “Criar novo” e cadastre: nome, aulas previstas, data inicial e final do plano, e objetivos. <PERSON><PERSON>do terminar, clique em “Salvar”.", "how_do_i_register_a_workout_for_one_of_my_members": "Onde prescrevo um treino para meu aluno?", "you_can_access_the_workouts_through_the_workouts_program": "Você pode acessar os treinos pelo programa de treino, ou clicar no botão flutuante escrito “Novo Treino”. Ao clicar nele, abrirá a parte das fichas pré-definidas. Caso não queira nenhum treino pronto, basta clicar em “Cadastrar novo treino” e seguir os passos:\n\nPasso 1: cadastre os dados da ficha (nome, observação, sexo, categoria, nível, tipo de execução) e clique em “Avançar”.\nPasso 2: depois de salvar, você pode editar seu treino, gerar um padrão de série e adicionar atividades.\nPasso 3: adicione atividades clicando em “Adicionar”. Busque as atividades desejadas pelo nome e as adicione clicando no V ao lado. Quando o botão ficar verde, é porque foi selecionado. Quando terminar, clique em “Adicionar atividades” para adicionar as atividades ao treino.\nPasso 4: caso queira editar sua atividade, basta clicar no nome da atividade adicionada ao seu treino. <PERSON><PERSON><PERSON>, você pode alterar série, repetições, carga, cadência e descanso. Além disso, é possível selecionar um método de execução (caso você tenha mais de uma atividade, é possível gerar um padrão para todas, colocando série, repetições e descanso padrão para todas as atividades). Ao finalizar, clique em “Salvar” para adicionar as edições da atividade.", "my_workout_program's_deadline_is_close": "Meu programa de treino está perto de vencer, o que faço?", "you_need_to_wait_until_it_has_passed_its_deadline": "É preciso esperar o programa de treino vencer primeiro para conseguir renová-lo. No perfil do aluno, vá em programa de treino e clique em “Renovar”. Assim, o programa de treino será renovado automaticamente.", "workout_details": "Detalhes do treino", "how_to_add_exercises_to_a_workout": "Como adicionar atividades ao meu treino?", "click_add_in_the_workout_screen_to_navigate_to": "<PERSON><PERSON> da ficha do aluno, clique em “Adicionar” para ir à tela de atividades. A seguir, selecione as atividades que deseja colocar naquele treino. Uma vez selecionados, clique em “Adicionar atividades”. <PERSON><PERSON><PERSON>, você pode alterar séries, repetições e descanso, ou métodos, se preferir, e clique em “Salvar” para salvar as alterações.", "can_i_add_an_performance_method_such_as": "Posso adicionar método de execução como bi-set e tri-set aos meus treinos?", "yes_after_adding_exercises": "Sim. <PERSON><PERSON><PERSON> de adicionar as atividades, ao clicar nelas, você vai para a parte de edição das mesmas. Lá é possível adicionar série, repetições, descanso e métodos de execução, como bi e tri-set, drop-set, entre outros métodos.", "what_are_the_other_options": "O que são ‘Outras Opções’?", "there_are_two_options_available": "Existem duas opções disponíveis. Com a primeira, é possível tornar essa ficha em 'Fichas pré-definidas', assim você poderá usá-la para outros alunos também. Ela ficará salva na aba 'Treinos' no menu inferior do aplicativo. Já com a segunda opção, pode-se gerar padrões de séries para todas as atividades da ficha de uma só vez. Você pode adicionar, por exemplo, 3 séries de 12 repetições a todas as atividades, sem precisar editar uma por uma.", "editing_program": "Editando <PERSON>a", "what_is_the_workout_program": "O que é programa de treino?", "the_workout_program_is_where_all_the_workouts_and_exercises": "O programa de treino é o local onde ficam os treinos e as atividades que você prescreveu para aquele aluno. Nele, você pode editar, excluir e adicionar novos treinos.", "what_are_the_planned_classes": "O que são aulas previstas?", "planned_classes_are_the_classes_that_the_member_is_expected_to_take": "Aulas previstas são a previsão do número de aulas que seu aluno vai fazer, baseada na quantidade de dias por semana dentro do período do programa de treino dele. Supondo que o programa seja de 1 mês e que o aluno irá 2 vezes por semana à academia, com esses dados o sistema vai gerar quantas serão as aulas previstas que o aluno fará dentro daquele período de 1 mês.", "does_the_name_of_the_program_change_anything": "O nome do programa de treino interfere em algo?", "no_the_program's_name_can_be_whatever": "<PERSON><PERSON>, o nome do programa de treino pode ser o que você quiser, ele não altera em nada no objetivo, ou em outro ponto do programa de treino.", "wrong_data": "Dados incorretos.", "member_check_if_your_input_is_correct": "Aluno: Verifique se os dados inseridos estão corretos. Confira com seu personal se os seus dados (telefone, e-mail e senha) estão corretos no sistema.\nPersonal: Verifique se os dados inseridos estão corretos. Confira se os seus dados (telefone, e-mail e senha) estão corretos no sistema.", "i_can't_find_my_sms_code": "Meu código de SMS não chega. O que fazer?", "if_the_code_wasn't_sent_to_you_via_sms": "Se o código de SMS não chegou, g<PERSON><PERSON><PERSON>, o serviço que utilizamos da Google está demorando a enviar o código. <PERSON><PERSON><PERSON> caso, temos as seguintes soluções:\nEspere alguns minutos e tente novamente.\n\nFaça login com seu e-mail e senha cadastrados no sistema.\n\n\nLembre-se: se solicitar o envio de SMS muitas vezes, o seu acesso por telefone será bloqueado por várias tentativas.", "where_do_i_get_my_email": "Onde pego meu e-mail e senha para fazer login?", "member_you_can_get_your_email": "Aluno: Você pode obter seus dados (e-mail e senha) de login com seu personal.\nPersonal: O e-mail e senha cadastrados no formulário de compra são os que você vai usar para fazer login.", "members": "<PERSON><PERSON><PERSON>", "is_it_possible_for_me_to_add_a_member": "É possível adicionar um aluno?", "yes_by_clicking_the_upper_right_button_you_will_navigate_to_the_register_form": "<PERSON>m, clicando no botão superior à direita, você abrirá o formulário de cadastro. Basta preencher com os dados do aluno e salvar. Você também pode enviar um link para seu aluno o convidando. <PERSON><PERSON><PERSON> isso, o aluno aparecerá aqui com algumas informações (nome, idade, sexo e objetivos).", "what_are_these_filters": "O que são esses filtros?", "they_are_the_easiest_way_to_group_members": "Os filtros são caminhos mais fáceis para agrupar alunos por status, o que ajuda na gestão dos alunos e de suas fichas.", "members_added_through_the_browser_training_will_be_shown_here": "Os alunos adicionados pelo treino web aparecerão aqui?", "yes_every_member_will_be_exhibited_here": "<PERSON>m, todos os alunos cadastrados no seu aplicativo aparecerão aqui, independentemente de onde foram cadastrados.", "recommendations": "Indicações", "how_can_i_get_a_bonus_for_recommending_to_friends": "Como posso ganhar o bônus fazendo indicações?", "it_is_pretty_simple_you_send_your_code": "É bem simples. Você envia seu código para seus amigos e colegas pessoais. A cada 2 compras do aplicativo {} usando seu código, você conquista um mês grátis do seu aplicativo. Assim, quanto mais indicar, mais chances você tem de ganhar mais meses gr<PERSON>.", "how_can_i_recommend": "Como posso indicar?", "clicking_the_button_recommend": "Clicando no botão escrito “Indicar”, você irá para a página onde está o seu código. Você pode copiá-lo ou já enviá-lo pelo meio que preferir, selecionando uma opção abaixo (SMS, Whatsapp ou outros).", "how_do_i_follow_my_progress_on_the_prize": "Como fico sabendo quanto falta para ganhar o bônus?", "in_the_recommendation_page_it_will_be_registered": "Na página de indicação, ficará registrado em cada mês bônus o seu progresso e quanto ainda falta para ganhar. Ao clicar em “Detalhes”, você vê as informações sobre quais indicações te fizeram avançar.", "meditation_and_focus": "Meditação e Foco", "the_breathing_exercise_will_help_you": "O exercício de respiração ajuda você a recuperar seu foco. É ideal quando você precisa liberar a mente.", "current_weight": "Peso atual", "hey_this_one_matches_your_plan": "Ei, esta combina com seu plano!", "it_has_the_same_quantity_of_calories": "Possui a mesma quantidade calórica que você precisa na sua Refeição X. Deseja substituir?", "replace_it": "Substituir", "start": "<PERSON><PERSON><PERSON>", "daily_kcal": "kcal diárias", "meals": "refeições", "diversity": "variedade", "more_meals": "<PERSON><PERSON>", "quick_tips": "Dicas <PERSON>", "more_plans": "<PERSON><PERSON>", "insert_the_name_of_the_recipe": "Digite o nome da receita", "ops_no_recipe_found": "Ops, não encontramos \nnenhuma receita!", "check_if_there_are_no_typos": "Verifique se há erro de digitação e tente novamente. Se precisar, utilize outros termos", "results": "Results", "calories_content": "Quantidade de Calorias", "cooking_time": "Tempo de Preparo", "food_restriction": "Restrição Alimentar", "apply_filters": "Aplicar filtros", "up_to": "até", "your_plan": "Seu <PERSON>o", "schedule_plan": "Agendar Plano", "when_do_you_want_to_begin": "Coloque a data que deseja começar!", "goal": "Meta", "bmi": "IMC", "view_your_progress": "Ver progresso", "congratulations_you_reached_your_weight_objective": "Parabéns! Você conseguiu atingir a sua Meta de Peso desejada! 💪", "no_weight": "Sem peso", "kcal_consumed": "kcal consumidas", "valid_until": "v<PERSON><PERSON><PERSON> at<PERSON>", "open_source_libraries": "Bibliotecas de código aberto", "HOMEFIT": "", "create_live_workout": "Criar treino ao vivo", "step_2_of_2": "Passo 2 de 2", "start_now": "Iniciar agora", "select_the_end_time": "Selecione o horário de término", "set_programar": "Programar", "select_the_start_and_end_time": "Selecione a data e o horário de ínicio e término", "unable_to_complete_your_request": "Não foi possível completar a requisição", "create_workout": "<PERSON><PERSON><PERSON>", "save_changes": "<PERSON><PERSON>", "step_1_of_2": "Passo 1 de 2", "add_a_name_to_your_workout": "Adicione um título que descreva seu treino", "workout's_name": "Nome do Treino", "talk_to_your_members_about_the_live": "Fale sobre a live para seus alunos", "description": "Descrição", "live's_url": "URL da Live", "next": "Próximo", "it_was_not_possible_to_delete_the_live": "Não foi possível excluir a live, tente novamente", "add_video": "<PERSON><PERSON><PERSON><PERSON>", "video's_url": "URL do vídeo", "please_insert_the_video's_link": "Por favor, insira o link do seu vídeo", "exercise's_description": "Descrição da atividade", "the_description_will_be_presented_when_the_member": "A descrição será apresentada quando o aluno acessar o vídeo", "record": "<PERSON><PERSON><PERSON>", "share": "Compartilhar", "my_workout_continues_in_the_app": "Meu treino continua pelo App!", "still_fitness_at_home": "Sigo Fit em Casa!", "finished_in": "<PERSON><PERSON><PERSON><PERSON><PERSON> em:", "create_new_tag": "Criar nova tag", "insert_the_name": "Informe o nome:", "type_the_name_of_the_tag": "Digite o nome da tag", "the_item_will_be_exhibited_at_the_tags_category": "O item aparecerá na categoria tags.", "you_need_to_insert_a_name": "Você precisa digitar um nome para continuar", "the_tag_has_been_saved": "A tag foi salva corretamente.", "all_ready": "Tudo pronto!", "details": "<PERSON><PERSON><PERSON>", "difficulty_level": "Nível de Dificuldade", "muscles_worked": "Grupos Musculares", "equipments": "Equipamentos", "create_your_own_tags": "Crie suas próprias tags clicando no botão adicionar (+)", "remove": "Remover", "create": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "online_workout": "Treino Online", "add_the_title_that_best_describes_your_workout": "Adicione um título que descreva sua ficha", "describe_your_workout_for_the_members": "Fale sobre sua ficha para seus alunos", "*details": "*Detalhes", "details_were_not_stated": "Detalhes não definidos", "workout's_header": "Capa do Treino", "pick_an_image_that_describes_the_workout": "Escolha uma imagem que represente o treino", "attention": "Atenção", "the_workout's_details_need_to_be_set": "Os detalhes do treino precisam ser definidos", "workout's_videos": "Vídeos do treino", "add_lower_case": "adicionar", "personal_trainer": "Professor", "workout's_details": "Detalhes do Treino", "workout_with_no_exercises": "T<PERSON>ino sem Atividades", "1_exercise": "1 Atividade", "exercises": "Atividades", "who_completed_the_workout": "Quem realizou o treino?", "it_was_not_possible_to_register_the_execution": "Não foi possível registrar a execução, tente novamente", "finish_workout": "Completar treino", "workout_completed": "<PERSON><PERSON><PERSON> con<PERSON>", "no_videos": "Sem vídeos", "1_video": "1 vídeo", "videos": "vídeos", "be_the_first_one": "<PERSON>ja o primeiro", "1_person_completed": "1 realizou", "people_finished": "real<PERSON><PERSON>", "workout_by": "<PERSON><PERSON><PERSON> por", "workout": "<PERSON><PERSON><PERSON>", "save_in_the_camera_roll": "<PERSON>var no rolo da câmera", "filters": "<PERSON><PERSON><PERSON>", "clean_filters": "<PERSON><PERSON> tudo", "keep_your_weight": "Manter peso", "muscle_building": "<PERSON><PERSON><PERSON>", "learn_more_about": "Aprenda mais sobre:", "at_home_workout": "Treino em casa", "return_to_feed": "Voltar para o feed", "exercise's_details": "Detalhes do exercício", "the_video_has_no_description": "Vídeo sem descrição", "select_the_customers": "Selecione os clientes app", "the_keywords_did_not_match_any_results": "Não encontramos pelo termo pesquisado, verifique e tente novamente.", "select_the_events_for_the_inapp": "Selecione eventos para o InApp", "no_results": "Sem resultados para o termo", "have_i_heard_prizes": "Eu ouvi bônus?", "recommend_the_app_for_other_personal_trainers": "Indique personais para usarem o app e ganhe mensalidades grátis!", "make_as_many_recommendations": "Faça quantas indicações quiser!", "you_get_a_free_month_to_every_two": "A cada dois personais que assinarem o app pelo seu link, você ganha uma mensalidade grátis!", "looks_like_your_membership_is_frozed": "Parece que sua matrícula está trancada. Entre em contato com sua academia para resolver.", "invite_other_personal_trainers_to_the_app": "Convide personais para o {}. A cada 2 assinaturas, você ganha 1 mês grátis!", "copied_to_clipboard": "Código copiado para área de transferência", "share_your_code": "Compartilhe seu código", "join_us_at": "<PERSON><PERSON><PERSON> venha fazer parte do {}, acesse o link:", "ops_seems_like_there_is_no_one": "<PERSON><PERSON>, parece que não tem ninguém por aqui!", "keep_recommending": "Continue indicando para aumentar suas chances de ganhar!", "prize's_validity": "Validade do Bônus", "free_month_is_valid_until": "Mensalidade Gratuita válida até", "check_who_is_using_the_app_after_your_recommendation": "Confira abaixo os personais que já estão usando o app por sua indicação.", "you_have_not_recommended": "Você ainda não possui nenhum indicado, aproveite e compartilhe o seu link de indicação", "recommend": "Indicar", "recommendation": "Indicação", "how_does_it_work": "Como funciona?", "you_share_your_link_however_you_want": "Você compartilha o seu link pessoal de indicação onde preferir, para quantos personais trainers quiser. Quando 2 (dois) personais assinarem o aplicativo ${F.title}, através do seu link, você ganha uma mensalidade gratuita do serviço.\n\nSua mensalidade gratuita ficará ativa no mês seguinte ao do dia da assinatura do segundo personal trainer. Para ver mais detalhes de cada bônus, como data de validade e indicados confirmados, confira na aba ‘Detalhes’ na área de Indicação. \n\nEstão disponíveis 12 meses gratuitos para você conquistar. Quanto mais indicar, mais chances você tem de ganhar suas mensalidades bônus.\n", "campaign's_regulation": "Regulamento da Campanha", "unable_to_validate_the_qr_code": "Não foi possível validar o Qr Code", "acccess_with_qr_code": "Acesso por Qr Code", "position_the_camera_right": "Posicione a câmera", "point_the_camera_to_the_qr": "Aponte a câmera para o QR Code da academia e aguarde a leitura", "select_your_user": "Selecione o seu usuário", "welcome_to_the_workout": "Bem-vindo ao APP Treino", "an_easy_way_to_keep_your_routine": "Uma maneira fácil de controlar sua rotina em busca de resultados e acompanhar sua evolução", "phone_number_not_found": "Telefone não encontrado", "access_with_your_email": "Acessar com e-mail", "you_need_to_save_the_changes": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "developer_mode": "<PERSON><PERSON>", "all_the_urls_must_end_with": "* Todas as urls devem obrigatóriamente terminar com / no final", "turn_on_dev_mode": "Ativar <PERSON>", "login_without_a_password": "<PERSON>gin sem senha", "use_discovery": "Utilizar discovery", "reset_original": "Restaurar original", "select_your_unit": "Selecione sua unidade", "your_search_did_not_match_any_results": "Não foi possível encontrar a unidade pelo termo pesquisado", "forgot_password": "Recuperação de senha", "we_will_send_you_an_email": "Iremos te enviar um e-mail com o código \npara a recuperação da sua senha", "you_will_receive_an_email": "Em instantes você receberá o e-mail com a nova senha!", "alright": "<PERSON><PERSON> certo", "unable_to_find_this_email": "Não encontramos esse e-mail. Verifique se digitou corretamente e tente novamente.", "there_has_been_an_error_sending_the_email": "Ocorreu um erro ao tentar enviar um e-mail. Verifique sua conexão e tente novamente.", "tip_uppercase": "DICA", "check_your_spam_if_you_did_not_get_the_email": "Caso não receba o e-mail com o código, \nverifique sua caixa de SPAM", "users_found": "Usuários Encontrados", "the_phone_number_inserted_is_different": "O telefone informado no login é diferente do que consta no seu cadastro. Solicite a atualização do seu número na recepção e logue com o telefone novamente.", "confirm_the_code": "Confirme o código", "the_6_digits_code": "Informe o código de 6 dígitos enviado por SMS para o número informado.", "resend": "Enviar novamente", "reenviar_codigo": "Reenviar código", "invalid_code": "<PERSON><PERSON><PERSON>", "my_result": "<PERSON><PERSON> resultado", "post_result": "<PERSON><PERSON>", "result_posted_successfully": "Resultado postado com sucesso!", "you_are_in_the_rank": "Você encontra-se na {}° colocação no ranking desse WOD", "fill_out_the_fields": "Preencha os campos para continuar", "category": "Categoria", "rest": "Descanso", "progressive": "Progressivo", "regressive": "Regressivo", "select_a_time_bigger": "Selecione um tempo maior que 00:00", "start_stopwatch": "Iniciar timer", "stopwatch_type": "Tipo", "select_the_type_of_stopwatch": "Selecione o tipo de cronômetro", "time": "Tempo", "gym's_plans": "Planos da academia", "no_plans_available_right_now": "No momento não há planos online disponíveis", "available_plans": "Planos disponíveis", "this_plan_includes": "Esse Plano Inclui :", "buy_now": "COMPRE AGORA", "complete_purchase": "Finalizar compra", "step_2_of_3": "Passo 2 de 3", "my_credit_card": "Meu <PERSON> de crédito", "cards_number": "Número do cartão", "name_on_credit_card": "Nome impresso no cartão", "expiry_date": "Validade", "step_1_of_3": "Passo 1 de 3", "basic_info": "Dados básicos", "name": "Nome", "date_of_birth": "Data nascimento", "contact": "Contato", "phone_numner": "Telefone celular", "address": "Endereço", "zip_code": "CEP", "street_name": "Logradouro", "complement": "Complemento", "fill_out_the_form_correctly": "Preencha o formulário corretamente", "insert_the_quantity_of_rounds": "Informe a quantidade de rounds", "stopwatch": "Timer", "select_a_time_bigger_to_rest": "Selecione um tempo maior que 00:00 no descanso", "exercise": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "the_stopwatch_keeps_going": "O Timer vai continuar rodando enquanto o aplicativo estiver aberto", "stay": "Ficar", "go_back_to_home": "<PERSON>r para home", "percentage_calculator": "Calculadora de Percentagens", "reference_weight": "Peso Referência", "percentage": "Percentagem", "new_record": "Novo Record", "no_description_for_this_exercise": "Sem descrição para essa atividade", "you_have_not_informed_your_pr_yet": "Você ainda não informou o seu PR", "always_keep_your_prs_updated": "Deixe seus PRs sempre atualizados para poder realizar", "so_you_can_calculate_based": "c<PERSON>l<PERSON>los baseados ne<PERSON>, além de melhorar a sua", "getting_rank_points": "pontuação nos rankings", "warmup": "Aquecimento", "stretching_mobility": "Alongamento / Mobilidade", "skill_parte_tecnica": "Parte técnica / Skill", "no_wod's_result": "Sem resultado do wod", "register_your_result": "Registre o seu resultado", "register_pr": "Registrar PR", "personal_records": "Personal \nrecords", "search_for_a_wod": "Busque por um exercício ou WOD", "no_exercises_for_now": "Sem atividades por enquanto", "search_for_another_keyword": "Busque por outro nome", "ops_no_exercises_found": "Ops, não encontramos \nnenhuma Atividade!", "unable_to_load_the_list": "Não foi possível carregar a lista, puxe para baixo para tentar novamente", "you_want_to_leave_without_posting": "Deseja sair sem postar o resultado?", "leave": "<PERSON><PERSON>", "unable_to_process_the_wods_list": "Não foi possível consultar a lista de WOD's", "no_wods_for_now": "Sem Wods no momento", "there_is_no_wod_today": "Ainda não existe nenhum wod para hoje", "standard_stopwatch": "<PERSON><PERSON><PERSON><PERSON>", "new_wod": "Novo Wod", "edit_wod": "<PERSON><PERSON>", "you_must_insert_the_name_wod": "O nome, dia e tipo do WoD devem ser informados antes de continuar", "upload_an_image": "Faça upload de uma imagem", "upload_an_square_image": "para melhor visualização envie imagens quadradas", "replace_image": "Trocar imagem", "wod's_name": "Nome do WOD*", "my_wod": "Meu <PERSON>", "wod's_type": "Tipo de WOD", "date": "Data", "wod's_date": "Data do Wod", "saved": "Salvo", "the_wod_has_been_saved": "O {} foi salvo", "creation": "Criação", "wod's_edition": "<PERSON><PERSON>'s edition", "aparelhos": "Aparelhos", "remarks": "Observações", "stretching": "Alongamento", "cross_equipments": "Aparelhos Cross", "add": "<PERSON><PERSON><PERSON><PERSON>", "search_by_name": "Procure por nome", "error_consulting_the_equipments": "Falha ao consultar os aparelhos", "cross_exercises": "Atividades Cross", "daily_wod_haven't_been_registered": "WOD do dia ainda não foi cadastrado", "click_the_upper_icon_and_register": "Clique no ícone no topo da tela e registre um agora mesmo", "excluir": "Excluir", "has_been_deleted": "{} foi excluído", "duplicate": "Clonar", "type": "Tipo", "unable_to_load_data": "Não foi possível carregar os dados", "no_wods": "Sem Wods!", "register_the_first_wod": "Cadastre o primeiro Wod no botão (+) no topo da tela", "you_can_change_the_settings": "Você pode alterar as configurações do seu app sempre que precisar, para isso acesse as configurações no menu lateral", "customize_later": "<PERSON><PERSON><PERSON> depois", "start_customization": "Iniciar a personalização", "choose_one": "Escolha uma\n", "main_color": "<PERSON><PERSON>", "choose_the_main_color_of_your_app": "Escolha a cor que o app irá utilizar como referência nos botões e menus", "move_on": "<PERSON><PERSON><PERSON><PERSON>", "select_your": "Selecione sua\n", "the_logo_choice": "A escolha da logo serve como uma identidade do seu negócio. Sugerimos que utilize uma imagem com fundo transparente", "move_on_without_a_logo": "Prosseguir sem logo?", "dont_worry_you_can_insert_a_new_logo": "<PERSON>ão se preocupe, você poderá colocar uma nova logo a qualquer momento, basta ir no menu de configurações do aplicativo", "add_an_image_later": "Adicionar imagem depois", "unable_to_save_your_changes": "Não foi poss<PERSON><PERSON> sal<PERSON> as alter<PERSON><PERSON><PERSON><PERSON>, por favor, tente mais tarde", "finally_choose_the": "Para finalizar,\nescolha o", "name_app": " nome ", "of_your_app": " do seu app ", "the_name_will_be_exhibited": "O nome será utilizado para que seus alunos encontrem seu app", "insert_a_name": "Digite um nome", "finish_setting": "Terminar a configuração", "the_app_will_be_restarted_so": "O App vai reiniciar para aplicar a personalização", "you_can_change_the_customization": "Você pode alterar a personalização do seu app sempre que precisar, para isso acesse as configurações no menu lateral", "alright_your_app": "Tudo pronto,\nseu app ", "already_customized": "já está\npersonalizado ", "what_is_you_goal": "Qual o seu objetivo?", "do_you_have_any_food_restriction": "Possui algum tipo de restrição alimentar?", "how_many_meals_a_day": "Quantas refeições por dia?", "3_meals_and_1_snack": "3 refeições e 1 lanche", "3_meals_and_2_snacks": "3 refeições e 2 lanches", "3_meals_and_3_snacks": "3 refeições e 3 lanches", "generating_your_nutritional_plan": "Gerando seu plano de refeições", "wait_a_few_seconds": "Aguarde alguns segundos", "fat_loss": "Definição", "unable_to_load_tips": "Falha ao consultar dicas", "unable_to_load_recipes": "Não foi possível carregar a lista de Receitas e turmas", "no_recipes_found_for_these_keywords": "Não foram encontradas receitas para o termo pesquisado", "my_nutritional_plan": "Meu plano de refeições", "food_restrictions": "Restrições", "quantity": "Quantidade", "thats_the_summary_of_your_nutritional_plan": "Esse é o resumo de sua personalização do plano alimentar, você pode editar a qualquer momento", "ops_there_has_been_an_error_nutrition": "Ops! não foi possível carregar o plano de refeições", "my_day": "<PERSON><PERSON>", "recipes": "Receitas", "week_maiusculo": "Se<PERSON>", "tips": "Dicas", "activate_right_now": "Ative agora mesmo", "enable": "Ativar", "no_idea_of_what_you_should_eat": "Não sabe o que vai comer? Quer experimentar algo diferente sem sair da dieta? Confira as receitas exclusivas feitas por nossa equipe de nutricionistas.", "by": "Por:", "ratings": "Avaliações", "nutritional_information": "Informações nutricionais", "protein": "<PERSON><PERSON><PERSON><PERSON>", "lipids": "Gordura", "net_carb": "Carb. <PERSON>", "ingredients": "Ingredientes", "preparation_method": "Modo de preparo", "responsible": "Responsável", "other_options": "Outras opções", "what_do_you_think_about_this_recipe": "O que achou dessa receita?", "your_feedback_helps_us": "Sua avaliação ajuda a melhorar o cardápio oferecido pelo app", "the_creator_is_unknown": "O autor(a) é desconhecido", "unable_to_load_the_creator": "Não foi possível carregar o autor", "thank_you": "<PERSON><PERSON><PERSON>", "your_subscription_is_already_active": "Sua compra já está ativa e você pode aproveitar todos os recursos do plano contratado", "manage_your_fitness_routine": "<PERSON>e da \nrotina fitness", "all_you_need_to_have_a_healthier": "Tudo em um só lugar para ter um dia mais saudável", "your_workout_allied_with_your_diet": "Dieta aliada \nao treino", "create_nutritional_plans": "Crie planos alimentares alinhados com o seu objetivo", "breathing_and_focus": "Respiração e foco", "relax_and_meditate": "Relaxe e medite para voltar à ativa com força total!", "exclusive_content": "Conteúdo Exclusivo", "access_tips_and_info": "Acesse dicas e informações sobre nutrição, saúde e bem-estar", "the_ideal_hydration": "Nível de hidratação ideal", "set_the_optimal_amount": "Configure a sua dose ideal de água e receba lembretes", "your_purchase_cant_be_restored": "Sua compra não pode ser restaurada pois não está mais ativa, recomendamos comprar um novo plano", "we_cant_detect_your_purchase": "Não identificamos a sua compra", "view_plans": "Conferir planos", "subscribe_1_month": "Assinar 1 mês", "subscribe_months": "Assinar {} meses", "limited_offer": "Oferta limitada!", "you_can_try_the_app": "Teste por 3 dias totalmente gratuitos e depois receba 63% de desconto no plano anual", "benefit_from_the_offer": "<PERSON>ove<PERSON><PERSON>", "check_what_youll_get_access_to": "Veja o que você vai ter acesso", "customized_nutritional_plans": "Planos alimentares personalizados", "automatic_shopping_list": "Listas de compras automáticas", "hydration_alert": "Alertas de água", "recipes_and_diets": "Receitas e dietas", "breathing_control": "Controle de respiração", "step_tracker": "Monitorador de passos", "health_and_nutrition_content": "Conteúdos de saúde e nutrição", "subscriptions_available": "Planos disponíveis", "already_a_member": "Já é assinante?", "restore_purchases": "<PERSON><PERSON><PERSON> compras", "our_advantages": "Nossos diferenciais", "check_how_everything_becomes_easier": "Veja como tudo ficará mais fácil", "feature": "Recurso", "basic": "Básico", "classes": "<PERSON><PERSON>", "workouts_maiusculo": "<PERSON><PERSON><PERSON><PERSON>", "physical_evaluation": "Avaliação física", "nutritional_plans": "Planos nutricionais", "fitness_routine": "R<PERSON>ina fitness", "health_content": "Conteúdos de saúde", "3_days_free": "3 Dias grátis!", "save_63%": "Economize 63%", "/month": "por mês", "its_all_you_needed_to_get": "É tudo o que faltava para atingir os resultados que você vivia se sabotando para conseguir...", "regular_questions": "<PERSON><PERSON><PERSON> frequentes", "what_is_the_premium": "O que é o Premium do App Treino?", "its_the_part_of_the_app": "É a parte do seu aplicativo que agrega ainda mais saúde para o seu dia-a-dia, ao unir a força da nutrição ao seu treino, e ainda organiza sua rotina para te auxiliar a ter hábitos mais saudáveis, o que colabora para que você chegue de maneira mais rápida ao seu objetivo.", "which_features_i_will_get_access": "Quais recursos eu terei acesso?", "youll_have_access_to_customized": "Você terá acesso a planos alimentares personalizados, monitor de passos, receitas e dietas variadas, alertas de água e registro de hidratação, recurso para controle de respiração, conteúdos e dicas de saúde, e toda a sua rotina fitness em um só lugar!", "how_does_my_nutritional_plan_work": "Como funciona o meu plano alimentar?", "to_start_off_a_nutritional_plan": "Para dar início a um plano alimentar primeiro você responde uma anamnese, com suas preferências e necessidades. Com isso, geramos um planejamento com todas as refeições que você precisa fazer e a lista de compras com os ingredientes de cada uma. Depois é só registrar suas refeições e acompanhar sua evolução. Caso queira, você ainda pode trocar a receita por outras opções ainda dentro do seu plano personalizado.", "how_much_does_it_cost": "Quanto custa para usar todos os recursos do Premium?", "you_have_the_following_options": "Você tem as seguintes opções de assinatura: o plano mensal R$9,90 (BRL) ou o plano anual (R$89,90 (BRL) - R$7,49/mês). Sem contratos nem taxas extras. Cancele quando quiser.", "exclusive_nutritional_tips": "Dicas nutricionais exclusivas", "fitness_schedule": "Agenda fitness", "breathing": "Respiração", "drink_water": "<PERSON><PERSON>", "create_customized_nutritional_plans": "Monte planos alimentares personalizados, aliados com seus treinos e necessidades.", "generated_according_to_the_ingredients": "Gerada de acordo com os ingredientes necessários para fazer as refeições do seu plano alimentar.", "quality_content_about_the_fitness": "Conteúdos de qualidade sobre o mundo fitness e wellness feitos por especialistas no assunto.", "access_a_diversity_of_recipes": "Acesse receitas variadas e tipos de dietas diferentes para experimentar quando quiser.", "have_your_whole_fitness_routine": "Tenha toda a sua rotina fitness personalizada em um só lugar. <PERSON><PERSON> o que fazer e quando fazer.", "control_your_breathing_so_you": "Controle sua respiração para ter foco, meditar ou até aliviar possíveis tensões durante o dia.", "set_a_daily_objective": "Estabeleça uma meta diária para depois ver qual foi o seu avanço e quantas calorias você queimou.", "know_the_ideal_amount_of_water": "Saiba a quantidade ideal de água que seu corpo precisa e configure alarmes para te lembrar de se hidratar.", "all_of_the_premium_features_ios": "Neste valor estão inclusos todos os recursos Premium do aplicativo: dicas e conteúdos de nutrição e bem-estar, monitorador de passos, cálculo e alerta de água, plano personalizado de nutrição e receitas exclusiva! Sua assinatura é cobrada em uma única parcela e renovada automaticamente. O cancelamento é gratuito e poderá ser feito quando você quiser. Após o período escolhido, a assinatura do plano será renovada automaticamente pela sua conta do iTunes, a menos que a renovação automática seja desativada pelo menos 24 horas antes do final do período atual na tela de configurações da conta. O pagamento será cobrado na conta do iTunes na confirmação da compra. A assinatura é renovada automaticamente, a menos que a renovação automática seja desativada pelo menos 24 horas antes do final do período atual. A conta será cobrada para renovação dentro de 24 horas antes do final do período atual, o preço de renovação para a assinatura anual é de R$ 59,90 (BRL) e o valor semestral é de R$ 35,90 (BRL). As assinaturas podem ser gerenciadas pelo usuário e a renovação automática pode ser desativada acessando as Configurações da conta do usuário após a compra. Qualquer parte não usada de um período de teste gratuito, se oferecido, será perdida quando o usuário adquirir uma assinatura para essa publicação, quando aplicável.", "all_of_the_premium_features_android": "Neste valor estão inclusos todos os recursos Premium do aplicativo: dicas e conteúdos de nutrição e bem-estar, monitorador de passos, cálculo e alerta de água, plano personalizado de nutrição e receitas exclusiva! Sua assinatura é cobrada em uma única parcela e renovada automaticamente. O cancelamento é gratuito e poderá ser feito quando você quiser. Após o período escolhido, a assinatura do plano será renovada automaticamente pela sua conta da Google Play, a menos que a renovação automática seja desativada pelo menos 24 horas antes do final do período atual na tela de configurações da conta. O pagamento será cobrado na conta da Google Play na confirmação da compra. A assinatura é renovada automaticamente, a menos que a renovação automática seja desativada pelo menos 24 horas antes do final do período atual. A conta será cobrada para renovação dentro de 24 horas antes do final do período atual, o preço de renovação para a assinatura anual é de R$ 59,90 (BRL) e o valor semestral é de R$ 35,90 (BRL). As assinaturas podem ser gerenciadas pelo usuário e a renovação automática pode ser desativada acessando as Configurações da conta do usuário após a compra. Qualquer parte não usada de um período de teste gratuito, se oferecido, será perdida quando o usuário adquirir uma assinatura para essa publicação, quando aplicável.", "all": "Todos", "championship": "Campeonatos", "my_meals": "Minhas refeições", "terms_of_use_eula": "Termos de uso (EULA)", "language": "Idioma", "select_a_language": "Escolha um idioma", "are_you_sure_change_language": "Você tem certeza que quer alterar o idioma do app?", "the_app_will_be_restarted": "O app será reiniciado", "trending": "Em alta 🔥", "prof.": "Prof.", "exercise_video": "Vídeo do Exercício", "breathe_again": "Respirar novamente", "tip": "Dica: ", "stay_put": "\nFique parado em uma posição confortável, mantenha sua mente limpa e preste atenção na sua respiração.", "you_can_come_back": "\nVocê pode voltar aqui sempre que precisar se concentrar.", "well_done": "<PERSON><PERSON> bem", "breathings": "respirações", "breathe_for_at_least": "1. Realize no mínimo 1 minuto de respiração diária para completar os círculos. \n2. <PERSON><PERSON><PERSON> pode escolher a quantidade de minutos navegando pelos botões. \n 3. <PERSON><PERSON><PERSON> iniciar, para cancelar o andamento do exercício, clique no X.", "privacy_and_data": "Privacidade, uso e compartilhamento de dados", "by_turning_this_on": "Ao habilitar essa opção, você terá:", "more_efficient_and_safer": "An<PERSON><PERSON><PERSON> de erros mais eficiente e segura", "the_best_experience": "A melhor experiência possível no aplicativo", "infos_that_match_your_interests": "Comunicações adequadas ao seu interesse", "you_can_change_your_prefferences": "Você pode alterar suas preferências sempre que quiser nas configurações de privacidade no aplicativo Ajustes.", "select_your_country": "Selecione seu país", "would_you_like_to_enter_cpf": "Gostaria de acessar usando o seu CPF?", "mask_cpf": "000.000.000-00", "no_register_found_with_this_cpf": "Não foi encontrado nenhum cadastro \ncom o CPF informado, entre em contato \ncom a sua unidade", "enter_another_cpf": "Informar outro CPF", "contract": "Contrato", "sobre_o_contrato": "Sobre o contrato", "january": "Janeiro", "february": "<PERSON><PERSON>", "march": "Março", "april": "Abril", "may": "<PERSON><PERSON>", "june": "<PERSON><PERSON>", "july": "<PERSON><PERSON>", "august": "Agosto", "september": "Setembro", "october": "Out<PERSON>ro", "november": "Novembro", "december": "Dezembro", "data_por_extenso": "{dia} de {mesExtenso}, {ano}", "muscles": "<PERSON><PERSON><PERSON><PERSON>", "data_nao_disponivel": "Data não disponível", "bones": "<PERSON><PERSON><PERSON>", "fat": "Gordura", "residue": "Resí<PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>", "left": "<PERSON><PERSON><PERSON>", "others": "Outros", "abs": "<PERSON><PERSON><PERSON>", "new_post": "Nova Publicação", "later": "<PERSON><PERSON><PERSON>", "no_permission_camera": "Sem acesso à câmera", "you_need_to_give_permission": "Você precisa acessar o aplicativo Ajustes para conceder permissão à câmera e à galeria de fotos para utilizar esse recurso.", "settings": "<PERSON><PERSON><PERSON><PERSON>", "default": "Original", "sepia": "Sépia", "gray": "Cinza", "violet": "<PERSON><PERSON>", "cold": "<PERSON><PERSON>", "use_filter": "Aplicar filtro", "time_ago_language": "pr_short", "dia_formato_aulas": "{diaTratado}/{mesTratado}", "locale_calendar": "pt-br", "locale_item_mover_entre_dias": "pt", "data_aula_tratada": "{dia}/{mes}/{ano}", "delete_exercise's_image": "Excluir foto da atividade?", "do_you_want_to_delete_img_exercice": "Deseja excluir a foto da atividade {}", "no_equipments": "Sem equipamentos", "series": "Séries", "serie": "Série", "standard_performance": "Execução normal", "reps": "Repetições", "duration": "Duração", "load": "Carga", "speed": "Velocidade", "cadence": "Cadência", "distance": "Distância", "ps": "Obs.: ", "exercise_has_no_series": "Atividade sem séries", "the_exercise_has_no_registered_series": "A atividade não possui séries cadastradas, fale com o seu professor.", "adjusting": "AJUSTANDO", "apply_to_all_series": "Aplicar em todas as séries", "apply_only_to_this_series": "Aplicar apenas nesta série", "change_weight": "Alterar carga", "change_all_series": "<PERSON><PERSON><PERSON> as séries", "skip_rest": "Pular descanso", "workout_paused": "TREINO PAUSADO", "done_maiusculo": "CONCLUÍDO", "total_time": "TEMPO TOTAL", "minutes": "MINUTOS", "my_records": "Meus Records", "timer_finished": "Timer completo", "total_time_lower_case": "Tempo total", "post_your_result": "Poste seu resultado", "finish": "Finalizar", "ops_there_has_been_a_problem": "Opa, houve algum problema!", "your_contract_has_not_been_activated": "A ativação do seu contrato não foi concluída. Revise os dados cadastrados e tente de novo.", "plan_activated_successfully": "Plano ativado com sucesso!", "your_plan_expires_in": "Seu plano{} ir<PERSON> vencer em {} dias.", "renew_your_plan_to_enjoy": "Renove seu plano para voltar a aproveitar tudo o que sua academia tem a oferecer!", "renew_now": "<PERSON><PERSON> agora", "choose_another_plan": "Escolher outro plano", "select_your_level": "Selecione seu nível", "beginner": "Iniciante", "amateur": "Amador", "advanced": "Avançado", "intermediary": "Intermediário", "comment": "<PERSON><PERSON><PERSON><PERSON>", "optional": "Opcional", "weight": "Peso", "level": "Nível", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino", "to": " até ", "congrats_you_finished_your_plan": "Parabéns! Você chegou ao fim do seu plano", "see_results": "Ver resultados", "congrats_name": "<PERSON><PERSON><PERSON><PERSON>, {}!", "now_you_weigh": "A<PERSON>a você pesa", "of_this_plan_was_completed": "deste plano \nfoi concluído!", "lets_improve_ourselves_together": "Vamos melhorar juntos!", "sometimes_it_might_be_hard": "Às vezes pode ser difícil seguir um plano 100% à risca, mas você está no caminho certo. Vamos seguir firmes nessa jornada!", "you_finished_the": "Você concluiu o", "every_little_step": "Cada pequeno passo é importante nessa caminhada. Mostre seu resultado para seus amigos", "choose_a_new_plan": "Escolher um novo plano", "go_back_to_navigation": "Voltar à navegação", "you_have_no_active_plan": "A<PERSON>a você está sem nenhum plano ativo. O que gostaria de fazer?", "you_finished_your_plan": "Você concluiu o seu plano!", "when_we_started": "<PERSON><PERSON><PERSON>", "you_weighed": "você pesava", "confirm_data": "Confirmar dados", "before_you_start_a_new_plan": "Antes de começar um novo plano, precisamos atualizar os seus dados nutricionais!", "begin_a_customized_plan": "Começar um novo plano personalizado", "recommended_plans": "Planos Recomendados", "insert_the_name_of_the_plan": "Digite o nome do plano", "ops_no_plan_found": "Ops, não encontramos \nnenhum plano!", "check_if_there_is_any_typo": "Verifique se há erro de digitação e tente novamente. Se precisar, utilize outros termos", "news!": "Novidades!", "meals_maiusculo": "Refeições", "diversity_maiusculo": "Variedade", "whats_your_initial_goal": "Qual é o seu objetivo inicial?", "whats_your_sex": "Qual é o seu sexo?", "lets_kick_off_with_the_basics": "Vamos começar pelo básico", "your_heigth": "Sua altura", "your_birth_date": "Sua data de nascimento", "birth_date": "Data de Nascimento", "your_current_weight": "Seu peso atual", "whats_your_target_weight": "Qual é sua meta de peso?", "target_weight": "Meta de peso", "how_many_meals_do_you_normally": "Quantas refeições você faz em média por dia?", "whats_the_diversity_of_meals": "Qual a variedade de receitas que você quer para o plano?", "no_diversity": "<PERSON><PERSON><PERSON><PERSON>", "little_diversity": "<PERSON><PERSON>", "big_diversity": "Alta variedade", "select_your_level_of_physical": "Selecione seu nível de atividade física", "sedentary": "<PERSON><PERSON><PERSON><PERSON>", "i_dont_practice_exercises": "Não pratico nenhum exercício", "1_to_2_times_a_week": "Pratico exercícios de 1 a 2 vezes por semana.", "3_to_5_times_a_week": "<PERSON> mé<PERSON>, pratico exercício<PERSON> de 3 a 5 vezes por semana.", "i_practice_everyday": "Pratico todos os dias da semana", "gluten": "<PERSON><PERSON><PERSON><PERSON>", "fructose": "<PERSON><PERSON><PERSON>", "animal_origin_foods": "Alimentos de origem animal", "meats": "<PERSON><PERSON>", "eggs": "Ovos", "seafood": "Frutos do mar", "peanut_and_chestnuts": "Amendoim e Castanhas", "milk_and_its_derivates": "Leite e derivados", "fish": "Peixes", "do_you_have_any_sort_of_restriction": "Possui algum tipo de restrição alimentar?", "change": "Alterar", "meals_time": "Ho<PERSON><PERSON><PERSON> das Refeições", "calories": "Caloria<PERSON>", "average_daily_consumption": "Média de consumo diário", "carbs": "Carboidratos", "proteins": "<PERSON><PERSON><PERSON><PERSON>", "fats": "<PERSON><PERSON><PERSON><PERSON>", "water_goal": "Meta de água: ", "more_info": "<PERSON><PERSON>", "height": "Altura", "history": "Hist<PERSON><PERSON><PERSON>", "minimum_weight": "Peso Mínimo", "maximum_weight": "Peso Máximo", "main_meals": "Refeições Principais", "lunch_and_dinner": "Almoços e Jantares", "breakfasts": "Cafés da Manhã", "supper_meals": "<PERSON><PERSON>", "snacks": "Lanches", "lanches_da_manha_e_da_tarde": "Lanches da Manhã e da Tarde", "dinners": "<PERSON><PERSON><PERSON>", "jantares_e_almocos": "Jantares e Almoços", "more_recipes": "<PERSON><PERSON>", "no_favorite_recipes": "Nenhuma receita favoritada", "the_recipes_you_add_to_favorites": "As receitas que você marcar como favoritas irão aparecer aqui, assim que você favoritá-las", "favorite_recipes": "Receitas Favoritas", "confirm_meals": "Confirmar re<PERSON>", "you_are_almost_there": "Você está quase lá!", "to_start_off_your_plan": "Para começar seu plano é preciso confirmar as receitas em cada refeição que preparamos pra você!", "generating_your_meals": "G<PERSON>do as refeições do seu plano...", "iniciar": "Iniciar", "you_are_out_of_workouts": "Você está sem treino, com treino vencido ou todas as execuções já foram feitas. Fale com seu professor para poder começar a treinar!", "your_workout_is_expired": "O seu treino está vencido. Entre em contato com seu professor para renová-lo", "in_progress": "Em execução...", "view": "<PERSON>er", "your_workout_program": "Seu Programa de treino", "until": "até", "realizados": "Realizados", "expected": "<PERSON>vist<PERSON>", "workouts_performed": "Treinos realizados", "wheres_my_workout": "Cadê meu treino?", "yes_cancel_appointment": "<PERSON><PERSON>, desmar<PERSON>", "so_pode_marcar_min_antes": "Você só pode marcar a aula com {} minutos de antecedência ou menos", "position_the_cam": "Posicione a câmera", "point_the_cam": "Aponte a câmera para o QR Code do aluno e aguarde a leitura", "my_members": "<PERSON><PERSON>", "no_workouts": "<PERSON>m treino", "expired": "<PERSON><PERSON><PERSON><PERSON>", "inactives": "Inativos", "my_wallet": "<PERSON><PERSON>", "no_member_was_found": "<PERSON><PERSON><PERSON> aluno encontrado", "verifique_se_digitou_o_nome_correto": "Verifique se digitou o nome correto e tente novamente.", "crop_image": "Recortar imagem", "serie_set": "Série", "your_weight": "Seu peso", "you_gained": "<PERSON><PERSON><PERSON> ganhou", "you_lost_weight": "<PERSON><PERSON><PERSON> perdeu", "start_a_new_plan": "Começar Novo Plano", "weights_progress": "Progresso de peso", "your_focus_and_determination": "O seu foco e a sua determinação foram indispensáveis para atingir o resultado desejado!", "congrats,": "<PERSON><PERSON><PERSON><PERSON>,", "you_reached_your_goal_before": "Você conseguiu atingir a sua meta de peso antes do término do seu plano nutricional. Agora você pode continuar no plano atual ou mudar os objetivos e começar um novo.", "your_plan_has_reached_the_end": "Seu plano chegou ao fim", "start_a_new_plan_or_choose_an_existing": "Comece agora mesmo um novo plano ou escolha um existente.", "apply": "Aplicar", "fill_up_the_data": "Preencha os dados", "the_sheet_must_have_a_name": "A Ficha precisa ter nome, tipo de execução", "no_goals_registered": "Sem objetivos cadastrados", "unable_to_load_data_try_again": "Não foi possível carregar algumas informações básicas, tente novamente", "age": "Idade: {}", "age_not_informed": "Idade não informada", "number_of_exercises": "Total de atividades:", "from_the_pre_defined": "das fichas pré-definidas?", "from_the_workout_program": "do programa de treino?", "performance_type": "Tipo de execução:", "alternated": "Alternado", "week_days": "<PERSON><PERSON> da semana", "perform": "Executar nos dias:", "go_back_pre_defined_sheets": "Voltar para lista de fichas pré-definidas?", "sim_voltar": "Sim, voltar", "select_the_member": "Selecione o aluno", "the_sheet_has_been_added": "A ficha foi salva", "to_the_member": "no aluno", "to_the_members": "nos alunos", "voltar_ou_add_mais_alunos": "deseja voltar ou adicionar a outro aluno?", "add_to_another_member": "Adicionar a outro aluno(a)", "add_the_sheet": "Adicionar a ficha", "goals": "Objetivos", "register_new_sheet": "Cadastrar nova ficha", "years_old_sex": "anos | Sexo", "expiration_date": "Vencimento: {}", "send_message": "Enviar mensagem", "the_member_has_not_registered_phone_number": "O aluno não possui um número de telefone no cadastro", "unable_to_load_the_workout_program": "Ops! Não foi possível carregar o programa de treino", "try_again": "Tentar novamente", "workout_program": "Programa de Treino", "ops_your_member_has_no_workout": "Ops! Seu aluno ainda está sem treino", "create_a_new_plan": "Crie um novo programa de treino para seu aluno começar a nova rotina de treino imediatamente!", "create_a_new_one": "Criar novo", "move_on?": "Prosseguir?", "prosseguir": "Prosseguir", "by_clicking_next_your_program": "Ao clicar em prosseguir o programa será renovado", "unable_to_renew_your_program": "Não foi possível renovar o programa, erro: {}", "sheet": "ficha", "beginning": "Início", "end": "<PERSON><PERSON><PERSON><PERSON>", "this_workout_program_end": "Este programa de treino chegou ao fim!", "the_changes_will_be_applied_to_your_member": "O que deseja fazer? As alterações escolhidas serão aplicadas imediatamente no perfil do seu aluno!", "renew": "<PERSON><PERSON>", "member:": "<PERSON><PERSON>(a):", "workout_program_name": "Nome do programa de treino", "sheets": "<PERSON><PERSON><PERSON>", "programs": "Programas", "there_has_been_an_error_accessing_workout": "Ocorreu um erro ao tentar acessar esse treino. Arraste a tela pra baixo para atualizar e tente novamente.", "add_sheet": "<PERSON><PERSON><PERSON><PERSON>", "sheets_data": "<PERSON><PERSON>cha", "choose_member": "<PERSON><PERSON><PERSON><PERSON>(a)", "sheets_name": "Nome da ficha:", "category_sheet": "Categoria:", "message_sheet": "Mensagem na ficha:", "exercises_sheet": "Atividades", "add_minusculo": "adicionar", "from_the_workout": "do treino", "no_picture": "Sem foto", "anaerobic": "Anaeróbio", "aerobic": "Aeróbio", "no_sets_registered": "Sem séries cadastradas", "sets": "Séries", "with": "with", "add_a_new_one_exercise": "Adicione uma nova clicando em adicionar", "sheet_with_no_exercises": "Ficha sem atividades", "some_exercises_have_no_sets": "Existem atividades que estão sem séries, deseja salvar mesmo assim?", "yes_save_with_no_sets": "<PERSON>m, salvar sem séries", "no_i_will_add": "Não, vou adicionar", "what_do_you_want_to_do": "O que deseja fazer?", "you_can_save_this_sheet": "Você pode salvar essa ficha como uma ficha pré-definida, ou apenas adicionar a ficha ao aluno", "sheet_saved": "<PERSON><PERSON> salva", "add_sheet_to_member": "Deseja adicionar essa ficha a algum aluno?", "yes_add": "<PERSON>m, adicionar", "no_go_back": "Não, voltar", "save_as_a_pre_defined": "Salvar como ficha pré-definida", "just_add_to_member": "Apenas adicionar ao aluno", "add_to_pre_defined": "Adicionar às fichas pré-definidas?", "sets_pattern": "Padrão de séries", "anaerobico": "Anaeróbico: ", "aerobico": "Aeróbico: ", "video": "vídeo", "select_exercises": "Selecionar atividades", "no_exercises": "Sem atividades", "try_searching_with_another_name": "Tente pesquisar com outros nomes ou categorias", "add_exercise": "Adicionar {} atividade", "add_exercises": "Adicionar {} atividades", "generate_sets_pattern": "<PERSON><PERSON><PERSON> s<PERSON>", "by_clicking_save_all_the_exercises": "Ao clicar em salvar, todas as atividades criadas nesse treino, seguir<PERSON> esse padrão", "for_anaerobic_exercises": "Para Anaeróbicas", "for_aerobic_exercises": "Para Aeróbicos", "performance_method": "Método de execução", "no_sets": "Sem séries", "use_the_options_above": "Utilize as opções acima para criar séries de maneira rápida", "will_be_removed_from_exercise": "será removida da atividade", "speed_km_h": "Velocidade (Km/h)", "distance_in_meters": "Distância em metros", "meters": "Metros", "observacao": "Observação", "apply_to_the_exercise": "Aplicar à atividade", "edit_set": "<PERSON><PERSON>", "select": "Selecione", "the_exercise": "a atividade", "the_exercises": "as atividades", "the_bi_set_method": "O método de execução Bi-SET é composto por duas atividades. Escolha o exercício que {} fará o Set.", "the_tri_set_method": "O método de execução Tri-SET é composto por três atividades. Escolha os outros dois exercícios que {} fará os Sets.", "the_bi_set_method_only_allows": "O método Bi-set permite que o exercício seja relacionado apenas com 1 outra atividade.", "the_tri_set_method_only_allows": "O método Tri-set permite que o exercício seja relacionado apenas com 2 outras atividades.", "do_you_want_to_remove_this_post": "Deseja remover essa postagem?", "select_an_image_that_describes": "Selecione uma imagem que melhor representa o treino.", "your_workout_has_been_edited": "Seu treino foi editado com sucesso.", "your_workout_has_been_created": "Seu treino foi criado com sucesso.", "choose_an_option": "Escolha uma opção.", "to_continue_you_must_add_a_name": "Para continuar, você precisa adicionar um nome e escolher uma foto para a ficha.", "to_continue_you_need_to_add": "Para continuar, você precisa adicionar pelo menos uma atividade.", "your_sheet_was_executed": "Sua ficha foi realizada com sucesso!", "you_already_have_a_plan_in_progress": "Você já tem um plano em andamento.", "save_in_the_gallery": "Salvar na galeria", "are_you_sure_delete_workout": "Tem certeza que deseja excluir seu treino?", "do_you_want_to_remove_wod": "Excluir", "new": "Novo", "workout_program_minusculo": "programa de treino", "programs_name": "Nome do programa", "type_the_name_of_the_program": "Digite o nome do programa, ex. Treino costas", "days_a_week": "Dias por semana", "from_1_to_7": "De 1 a 7 dias", "expected_classes": "<PERSON><PERSON>", "from_1_to_...": "De 1 até...", "start_date": "Data de início", "end_date": "Data de término", "goals:": "Objetivos:", "ao_aluno": "ao aluno", "aos_alunos": "aos al<PERSON>s", "just_one_member": "Ops, apenas um aluno de cada vez.", "you_can_only_add_a_workout": "É permitida a adição de um treino apenas a um aluno por vez.", "facil_recognition": "Reconhecimento Facial", "members_goals": "Objetivos do aluno", "no_goals_set": "Sem objetivos definidos", "challenge": "<PERSON><PERSON><PERSON>", "add_a_new_sheet": "Adicione uma nova ficha ao programa de treino. Se o aluno não tiver um programa, crie um novo e, em seguida, adicione uma nova ficha.", "skip": "<PERSON><PERSON>", "your_plan_doesnt_allow_classes_this_period": "Esta aula não está incluída no horário atual do seu plano. Quer usar uma das suas aulas de teste?", "you_dont_have_access_to_this_class": "Não tem acesso a esta modalidade no seu plano. Quer usar uma das suas aulas de teste?", "this_class_is_not_from_this_company": "Esta aula não é desta unidade", "unable_to_check_out_this_class": "Não é possível fazer o check-out desta aula.", "erro_nao_tem_modalidade": "Ops! Você não possui a modalidade que está tentando agendar, para mais informações entre em contato com a sua unidade", "erro_marcar_aula_com_antecedencia": "<PERSON>ão pode fazer check-in nesta aula ainda. Tente novamente quando estiver mais perto do início.", "erro_confirmando_presenca_aluno": "Não é possível adicionar o membro a esta aula", "erro_aluno_nao_possui_autorizacao": "Acesso a esta aula negado", "nao_pode_desmarcar_mais": "É tarde demais para fazer o check-out desta aula", "sem_permissao_para_add_em_aula_iniciada": "Não tem permissão para adicionar um membro a uma aula que já começou/terminou", "erro_checkin_aula": "Ocorreu um erro ao fazer check-in nesta aula", "erro_ja_foi_marcada_uma_aula": "<PERSON><PERSON> foi agendada uma aula neste dia para a mesma modalidade. O plano não permite mais desta modalidade.", "erro_atingiu_numero_de_aulas_semanais": "O limite de aulas programadas desta modalidade já foi atingido.", "select_a_reason_to_continue": "Selecione um motivo para continuar de férias", "error_scheduling_class": "Não é possível agendar esta aula. Por favor, vá até ao balcão do ginásio e tente fazer o check-in.", "erro_alterar_serie": "Ocorreu um erro", "falha_ferias": "Não é possível tirar férias", "falha_cartao": "Não é possível gerar um token para o cartão", "error_checking_in_class": "Ocorreu um erro ao fazer check-in nesta aula", "here_you_can_edit_info_member": "A<PERSON> você pode editar as informações básicas do seu aluno(a).", "want_to_invite_someone": "Quer convidá-lo para baixar o app? Clique aqui.", "do_you_want_to_get_in_touch": "Precisa entrar em contato com o aluno? Clique aqui.", "here_are_the_members_goals": "Aqui estão os objetivos do aluno. Altere ou adicione clicando em editar.", "check_the_current_workout_program": "Confira o programa de treino atual e sua validade. Caso o aluno não tenha um plano, você pode criar um.", "the_historic_of_workout_executions": "O histórico de execuções de fichas do seu aluno.", "here_are_all_the_remarks_you_added": "<PERSON><PERSON><PERSON><PERSON> aqui todas as observações feitas por você.", "execution_history": "Histórico de execuções", "the_member_hasnt_performed_a_workout": "O aluno ainda não executou nenhum treino.", "there_are_no_remarks": "O aluno não possui observações.", "30_days": "30 dias", "15_days": "15 dias", "7_days": "7 dias", "horario_share_treino": "{horarioBr}", "save": "<PERSON><PERSON>", "choose_if_you_want_to_start": "Escolha se você deseja iniciar seu plano agora ou agendar para uma data futura para se preparar.", "now": "<PERSON><PERSON><PERSON>", "schedule_it": "Agendar", "recipes_confirmed": "Receitas confirmadas!", "confirm_all": "Confirmar to<PERSON>", "confirm_recipes": "Confirmar receitas", "dont_like_it": "<PERSON><PERSON> go<PERSON>", "like_it": "<PERSON><PERSON><PERSON>", "today": "Hoje", "meals_plan": "Plano de Refeição", "switch_recipe": "Alterar Receita", "according_to_your_meals_plan": "De acordo com o seu plano de refeições, a seguir confira a lista com as outras opções de receitas para te ajudar a conquistar o seu objetivo. ", "you_can_only_select_recipes": "Você só pode selecionar {} receita(s) por vez. Desmarque para poder selecionar outras.", "unable_to_process_this_request": "Não é possível processar este pedido.", "unable_to_process_this": "Não é possível processar isto.", "no_member_found": "<PERSON><PERSON><PERSON> aluno encontrado", "frequency": "Frequência", "no_method_selected": "Sem método sele<PERSON>ado", "options": "Opções", "your_sheet_has_been_deleted": "Sua ficha foi excluída com sucesso", "o_aluno_nao_tem_modalidade": "O aluno não possui a modalidade.", "check_in_nao_permitido_nesse_periodo": "{mensagemPortugues}", "important": "Importante", "there_are_no_notes_yet": "Ainda não há observações", "you_can_add_a_new_note_below": "Você pode registrar uma nova observação abaixo", "mark_as_important": "Marcar como importante", "write_your_note": "Escreva sua observação", "ocorreu_um_problema_comentario": "Ocorreu um problema ao processar seu comentario. Arraste para baixo para atualizar e tente novamente.", "up_to_date": "Em dia", "total_a_fazer": "total a fazer", "renewals": "renovações", "new_members": "novos alunos", "from": "", "data_aula_formatada": "{dataPortugues}", "ao_vivo": "ao vivo", "locale_data": "pt_BR", "hello_user": "<PERSON><PERSON><PERSON>, {}", "cross": "Cross", "read_more": "Ver mais", "meals_app": "Refeições", "partnerships_app": "Parcerias", "manage_your_classes": "<PERSON><PERSON><PERSON><PERSON> suas aulas", "next_classes": "Pró<PERSON><PERSON> aulas", "warning_no_classes_today": "Não existem aulas em andamento na data atual.", "see_classes": "<PERSON>er aulas", "falha_carregar_aulas": "Não foi possível carregar a \nlista de aulas", "falha_servidor": "O servidor está demorando para responder. Aguarde alguns instantes e tente novamente.", "agendar_montar_aulas": "Não existem aulas agendadas para a data informada. Aceda às suas aulas e gerencie a sua próxima aula.", "agendar_aulas": "Agendar", "item_refeicao_tela_inicial": "Acelere seus resultados \ncom a melhor dieta para o \nseu tipo físico!", "monitor_passos": "Monitor de passos", "editar_app": "<PERSON><PERSON>", "meta_diaria": "<PERSON><PERSON>", "k_cal": "kcal", "kcal_queimadas": "<PERSON><PERSON><PERSON>", "ative_agora": "Ative agora mesmo!", "text_banner_monitor_passos": "O nosso corpo não foi feito para ficar parado, basta caminhar um minutinho aqui e outro ali para manter seu corpo ativo.", "ativar_app": "Ativar", "definir_meta_diaria": "Qual será a sua meta diária?", "inserir_quantidade": "Insira aqui a quantidade", "passos_app": "Passos", "oms_passos": "De acordo com a OMS, a quantidade recomendada é de 8000 (oito mil) passos diários.", "salvar_app": "<PERSON><PERSON>", "treino_dia": "Treino do dia", "treino_concluido": "<PERSON><PERSON>ino concluído!", "ex_app_singular": "{} <PERSON><PERSON><PERSON><PERSON><PERSON>", "ex_app_plural": "{} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alternado_app": "Alternado", "Compartilhar_resultado": "Compartilhar resultado", "dias_semana": "<PERSON><PERSON> da semana", "strings": "strings", "navibar": {"inicio": "Início", "feed": "Feed", "aulas": "<PERSON><PERSON>", "alunos": "<PERSON><PERSON><PERSON>", "treino": "<PERSON><PERSON><PERSON>", "cross": "Cross", "emcasa": "Em casa", "agenda": "Agenda", "nutri": "Nutrição", "perfil": "<PERSON><PERSON> perfil", "treino_casa": "Treino em casa", "chat": "Chat com o treinador pessoal"}, "tela_feed_strings": "tela_feed_strings", "minhas_pubs": "Minhas Publicações", "instrucao_feed": "Você pode publicar a qualquer momento utilizando o feed", "sem_pubs": "Sem publicações", "load_falha": "Falha ao carregar", "feed_falha": "O feed não pode ser carregado", "internet_off": "Sem conexão com a internet", "item_feed_strings": "item_feed_strings", "comentar_item_feed": "Comentar", "rm_post": "Remover postagem", "atencao_app": "Atenção", "rm_validacao": "Deseja remover essa postagem?", "rm_texto": "Remover", "rm_falha": "Não foi possível completar a ação, tente novamente", "comentar_texto": "Comentar", "denunciar_texto": "<PERSON><PERSON><PERSON><PERSON>", "bloquear_usuario_texto_args": "Bloquear {}", "bloquear_usuario_texto": "Bloquear", "bloquear_usuario_aviso": "Deseja bloquear as postagens? Você não irá mais receber publicações deste usuário", "o_que_gostaria_de_fazer": "O que você deseja fazer?", "cancelar_texto": "<PERSON><PERSON><PERSON>", "menu_perfil": {"configurar": "Configurar app", "cadastrar": "<PERSON><PERSON><PERSON><PERSON>", "matricula_args": "Matrícula: {}", "local": "Estou em:", "minha_conta": "Minha conta", "minhas_publicacoes": "Minhas publicações", "sobre": "Sobre o app", "aviso_logout": "Ao clicar em sair você não estará mais logado no app e os dados não salvos serão perdidos.", "sair": "Logout", "deseja_deslogar": "<PERSON><PERSON><PERSON>?", "sair_app": "<PERSON><PERSON>", "meus_contratos": "Meus contratos", "avaliacao_fisica": "Minha avaliação física", "avaliar_professor": "Avaliar professor", "campanhas": "Campanhas In-App"}, "tela_turmas_strings": "tela_turmas_strings", "lista_filtros": {"todos": "Todos", "boxe": "Boxe", "crossfit": "Crossfit", "natacao": "Natação", "spinning": "Spinning", "pilates": "Pilates", "yoga": "Yoga", "zumba": "<PERSON><PERSON>"}, "titulolocalization": {"aulas_turmas": "<PERSON><PERSON> e <PERSON>", "aulas": "<PERSON><PERSON>"}, "string_pesquisa": "Busque por aula ou por modalidade", "aviso_erro": "Não foi possível carregar a \nlista de aulas", "aviso_timeout": "O servidor está demorando para responder. Aguarde alguns instantes e tente novamente.", "pausa_treino": "Dando uma pausa no Treino?", "contrato_pausado": "Seu contrato está pausado. Neste meio tempo, não é possível marcar, desmarcar ou consultar aulas. Caso tenha mais de um contrato, consulte com um que não esteja de férias", "meu_contrato": "<PERSON>u contrato", "erro_pesquisa_aulas_titulo": "Ops, não encontramos \nnenhuma aula!", "erro_pesquisa_aulas_mensagem": "Verifique os filtros selecionados ou se há erros de digitação e tente novamente.", "string_repor_aulas": " V<PERSON><PERSON> possui {} cré<PERSON><PERSON>{}", "string_repor_aulas_turma": " Voc<PERSON> possui {} aula{} para repor", "sem_aula_hoje": "Ops, não há aulas pra hoje!", "sem_aula_hoje_mensagem": "Não encontramos nenhuma aula pra hoje, tente pesquisar em outra data.", "calendario": "<PERSON><PERSON><PERSON><PERSON>", "trocar_unidade": "Trocar Unidade", "detalhes": "<PERSON><PERSON><PERSON>", "confirmar": "Confirmar", "continuar": "<PERSON><PERSON><PERSON><PERSON>", "checkin_cancelado": "Check-in cancelado com sucesso!", "cancelar_aula_aviso": "Tem certeza que gostaria de cancelar seu check-in nessa aula?", "cancelar_checkin": "Cancelar check-in", "voltar": "Voltar", "vaga_garantida_aula": "Sua vaga está garantida nessa aula, um QR code foi gerado, apresente ele para seu instrutor para confirmar a sua presença", "vaga_confirmada_aula": "<PERSON><PERSON> certo, sua vaga foi confirmada e está garantida nessa aula", "ver_qrcode": "Visualizar QR Code", "checkin_sucesso": "Check-in realizado com sucesso!", "utilizar": "<PERSON><PERSON><PERSON><PERSON>", "turma": "<PERSON><PERSON>", "checkin": "Fazer check-in", "professor": "Professor", "erro_carregar_alunos": "Não conseguimos carregar os alunos", "tentar_novamente": "Tentar novamente", "alunos_matriculados": "+ {} al<PERSON><PERSON> matriculados nesta turma", "disponivel": "Disponível", "ocupado": "Ocupado", "notificacoes": "Notificações", "TelaVerMiaProgramaTreino": "TelaVerMaisProgramaTreino", "seu_programa_treino_esta_vencido": "O seu programa de treino está vencido. Entre em contato com seu professor para renová-lo", "seu_programa_de_treino": "Seu Programa de treino", "em_execucao": "Em execução...", "ver": "<PERSON>er", "fichas": "<PERSON><PERSON>", "treinos_realizados": "Treinos realizados", "concluido": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ate_args": "{} até {}", "treinos_previstos": "<PERSON><PERSON><PERSON><PERSON>", "cartao_credito": "Cartão de Crédito", "boleto": "Boleto", "debito_em_conta": "Débito em conta", "tela_configuracoes": {"titulolocalization": "Configurações", "configuracoesPersonal": {"tema": "<PERSON><PERSON>", "modos": {"sistema": "Sistema", "dark": "Escuro", "light": "<PERSON><PERSON><PERSON>"}, "descanso": "Descanso", "descanso_info": "Habilita o descanso entre séries na execução do treino.", "medida": "Unidade de medida", "medida_info": "Selecione a unidade de medida \npadrão do aplicativo.", "aviso_sonoro": "Aviso sonoro", "aviso_sonoro_info": "Emitir aviso sonoro sempre que concluir um exercício", "countdown_info": "Contagem antes de finalizar o cronômetro de um exercício", "meus_dados": "Meus dados", "meus_dados_info": "<PERSON><PERSON><PERSON> que outros usuários visualizem dados como sua altura, peso e idade", "status_de_atividade": "Status de atividade", "status_de_atividade_info": "<PERSON><PERSON><PERSON> que outros usuários visualizem suas atividades"}, "titulo_widget_tema": "Escolha um tema"}, "unidade_medida": {"quilos": "<PERSON><PERSON><PERSON> (Kg)", "libras": "Libras (Lbs)"}, "tela_treinosModelo": {"mensagem_erro": "Não foi possível carregar algumas informações básicas, tente novamente", "texto_acao": "Entendido", "titulo_appBar": "Fichas e programas", "sem_fichas": "Sem fichas pré-definidas", "nada_encontrado": "Nada encontrado pelo termo pesquisado", "erro_filtros_aplicados": "Não há fichas para os filtros aplicados", "criar_novo": "Clique no botão + para criar um novo", "limpar_filtros": "Limpar filtros", "sucesso": "Sucesso!", "ficha_preDefinida": "A ficha pré-definida foi removido", "modelo_treinoEmUso": "Este modelo faz parte de um plano de treino em uso e não pode ser excluído.", "erro_informacoesBasicas": "Não foi possível carregar algumas informações básicas, tente novamente", "nehuma_ficha_disponivel": "Ops, não encontramos \nnenhuma ficha disponível", "msg_nenhuma_ficha": "Você pode tentar pesquisar por outros termos ou selecionar filtros diferentes."}, "tela_filtrosTreino": {"erro_mensagem": "Falha ao consultar os filtros", "tentar_novamente": "Tentar novamente", "titulo_appBar": "Filtrar treinos", "sexo": "Sexo", "sexos": {"masculino": "<PERSON><PERSON><PERSON><PERSON>", "feminino": "Feminino", "nao_informado": "Não informado"}, "nivel": "Nível", "categoria": "Categoria", "objetivos": "Objetivos"}, "tela_manterTreino": {"titulo_true": "Editar ficha de treino", "titulo_false": "Nova Ficha", "dados_basicos": "Dados básicos", "nome_ficha": "Nome da ficha", "ficha_hipertrofia": "Ex: <PERSON><PERSON>", "obs": "Observações", "complementar_atividades": "Ex: Procure completar todas atividades", "tipo_execucao": "Tipo de execução", "escolha_dias": "Escolha os dias"}, "tela_campanhas": {"vigencia": "Vigência:", "horariostr": "<PERSON><PERSON><PERSON> entre hor<PERSON>rios:", "status_args": "Status campanha: {}", "erro_consulta": "Não foi possível consultar", "titulolocalization": "Sem Ad-InApp", "mensagem": "Ainda não há Ad's In-App"}, "tela_manterInApp": {"titulolocalization": "{} <PERSON><PERSON><PERSON>", "titulo_erro": "Não posso mostrar por que", "campanha_ativa": "<PERSON><PERSON><PERSON>", "passos": {"1": "Passo 1: Upload da imagem, envie imagens quadradas", "2": "Passo 2: defina o nome da campanha", "3": "Passo 3: Defina a periodicidade e quantidade de impressão por usuário", "4": "Passo 4: link/tela abrir", "5": "Passo 5: defina a vigência da campanha", "6": "Passo 6:, range de horarios em que deve ser exibido", "7": "Passo 7:, eventos de exibição", "8": "Passo 8: Publico do in app", "3_tip": "Deixe como 0 para ilimitado, quando for por click outros no mínimo 1 click", "extra": "(Extra) Bloqueio de empresas/mostrar apenas em empresas"}, "inicio": "Início", "fim": "Fim", "range": "Novo Range", "mostrar": "Mostrar entre:", "mensagem_erro": "Ao menos um range deve ser mantido", "eventos": "Selecionar evento(s)", "mostrar_evento": "Mostrar no evento:", "publico": "Selecionar público", "mostrar_aoPublico": "Mostrar ao público:", "bloquear": "Bloquear in App nas empresas", "add_cliente": "Adicionar cliente app", "erro_mensagem": "Quando existem apps liberados não é possível selecionar apps para bloquear", "erro_mensagem_liberar": "Quando existem apps bloqueados não é possível selecionar apps para liberar", "mostrar_app": "Mostrar no app:", "mostrar_apenas": "Mostrar apenas in App nas empresas", "inApp_sucesso": "In App mantido com sucesso", "salvar_campanha": "<PERSON><PERSON>", "cadastrar": "Cadastrar"}, "tela_perfil": {"graduacao": "Graduação", "avaliacao": "Avaliação Física", "titulolocalization": "<PERSON><PERSON>", "codigo": "Código: {}", "idade": "{} anos", "pontuação": "Pontuação: {}", "resumo_avaliacao_fisica": "Resumo da avaliação física"}, "tela_progresso_graduacao": {"titulolocalization": "Progresso", "atual": "Atual", "erro_progresso": "Falha ao consultar o progresso", "tente_novamente": "Puxe para baixo para tentar novamente", "graduacao_nao_ativada": "Módulo de graduação não ativado", "erro_graduacao": "Não encontramos nenhuma graduação ativa recentemente"}, "tela_detalhes_assinatura": {"titulolocalization": "Minhas assinaturas", "planos": {"id": "Plano: {}", "data_inicio": "Assinatura feita em {}", "data_fim": "Assinatura válida até {}", "ativa": "Assinatura ativa: {}"}, "sim": "<PERSON>m", "nao": "Não", "erro_consulta": "Falha ao consultar assinaturas", "erro_historico": "Sem histórico de compras disponível até o momento", "consulte_mais_tarde": "Consulte mais tarde", "conhecer_planos": "<PERSON><PERSON><PERSON> planos"}, "altura": "Altura", "peso": "Peso", "gordura": "% de gordura", "retornar_texto": "Retornar", "tela_detalhes_contrato": {"msg_compra_plano": "Para poder treinar e aproveitar seu app com tudo o que sua academia tem a oferecer, escolha um plano para começar.", "titulo_compra_plano": "Prepare-se para começar!", "titulolocalization": "Contratos", "vigencia_contrato": "Vigência do contrato", "titulo_modal": "Selecione um contrato", "codigo": "Código", "erro_contratos": "Falha ao consultar contratos", "titulo_sem_contratos": "Sem contratos por aqui", "msg_sem_contratos_args": "Você ainda não firmou nenhum contrato com a sua academia. {}", "value": "<PERSON>azer contrato", "titulo_dialog_ferias": "Voltar d{} {}", "voltar_do": "Voltar do {}", "msg_dialog_ferias": "Ao voltar, o seu contrato terá a situação alterada para ativo", "sucesso_solicitacao": "A solicitação foi atendida com sucesso", "ferias_agendadas": "<PERSON><PERSON><PERSON><PERSON>", "voltar_ativo": "Ao voltar, o seu contrato terá a situação alterada para ativo", "trancamento_agendado": "Trancamento agendado", "parcelas": "<PERSON><PERSON><PERSON><PERSON>", "_sem_parcelas": "Sem parcelas", "parcela_args": "<PERSON><PERSON><PERSON> {}", "validade_args": "Validade: {}", "valor_base": "Valor base do contrato", "contrato": "Valor do contrato", "valor_nao_informado": "Valor não informado", "duracao_contrato": "Duração do contrato", "dias_args": "{} dias", "horario": "<PERSON><PERSON><PERSON><PERSON>", "condicao_pagamento": "Condição de pagamento", "modalidade": "Modalidade", "vez_args": "{} Vez{}", "trancamento": "Trancamento", "ferias": "<PERSON><PERSON><PERSON><PERSON>", "renovar": "<PERSON><PERSON>", "credito": "<PERSON><PERSON><PERSON><PERSON>", "acoes_rapidas": "Ações <PERSON>"}, "valor_contrato": "Valor do contrato", "duracao": "Duração", "valor": "Valor", "validade": "Validade", "assinatura_ativa": "Assinatura ativa", "assinatura_inativa": "Assinatura inativa", "assinatura_feita_em": "Assinatura feita em:", "assinatura_valida_ate": "Assinatura válida até:", "tela_trancamento_contrato": {"titulolocalization": "Trancamento", "infos_contrato": "Informações de contrato", "num_contrato": "Número do contrato", "plano": "Plano", "preco_contrato": "Preço do contrato", "dias_contrato": "Dias contratados", "dias_bonus": "<PERSON><PERSON> <PERSON>", "configurar": "Configurar trancamento", "selecione_produto": "Selecione o produto", "motivo_trancamento": "Selecione um motivo para o trancamento", "aviso_trancamento": "Seu contrato será trancado amanhã", "aviso_erro_produto": "É preciso selecionar um produto", "aviso_erro_motivo": "É preciso selecionar um motivo para o trancamento", "avancar": "<PERSON><PERSON><PERSON><PERSON>", "falha_consulta_trancamento": "Falha ao consultar trancamento"}, "dropDown_titulo": "Selecione uma opção", "tela_renovar_contrato": {"erro_plano_alterado": "O plano foi alterado e já não é compatível para renovação através da aplicação.", "verifique_o_balcao": "Dirija-se ao balcão de atendimento para mais informações", "titulolocalization": "Simulação de Renovação", "aviso_renovacao": "A negociação relacionada a esta operação será exatamente igual ao contrato anterior, mantendo as condições de pagamento, duração, modalidades e todos os demais itens referentes ao contrato em questão. Se você tiver alguma dúvida, entre em contato com sua academia", "sucesso_renovacao": "Seu contrato foi renovado com sucesso", "confirmar_renovacao": "Con<PERSON><PERSON><PERSON>", "falha_simular_renovacao": "Falha ao realizar uma simulação de renovação"}, "tela_contrato_ferias": {"titulolocalization": "<PERSON><PERSON><PERSON><PERSON>", "configurar_periodo": "Configurar per<PERSON><PERSON> de fé<PERSON>", "periodo_ferias": "Informe o período que deseja tirar férias", "motivo_afastamento": "Selecione um motivo para o afastamento", "descricao": "Descrição", "aviso": "<PERSON><PERSON> confirmar as suas férias, declara estar ciente e concordar com todas as condições descritas nos", "termos_condicoes": "Termos e Condições", "sucesso_solicitacao": "Sua solicitação de férias foi feita com sucesso", "confirmar": "Confirmar f<PERSON><PERSON>", "falha_consulta": "Falha ao consultar férias", "ferias_info": "Informações de férias", "dias_permitidos": "Dias permitidos para férias", "dias_utilizados": "Dias utilizados", "dias_restantes": "<PERSON>as restantes", "dias_minimos": "Dias mínimos para férias"}, "notificacoes_novo": "O que há de novo?", "pensando": "No que estás a pensar?", "botao_denunciar_post": "Denunciar publicação", "aviso_denuncia": "Este post será analisado e, se encontrarmos alguma violação, iremos removê-lo e notificar o utilizador.", "tela_denunciar_titulo": "<PERSON><PERSON><PERSON>", "tela_comentario_titulo": "Comentários", "comente_algo": "Comente algo", "comecar_treino": "<PERSON><PERSON><PERSON>", "finalizar": "Finalizar", "finalizar_execucao": "Deseja finalizar a execução deste treino?", "finalizar_treino": "<PERSON><PERSON><PERSON>", "fim_execucao": "Finalizar a execução?", "equipamentos": "Equipamentos", "sem_equipamentos": "Sem Equipamentos", "preparese": "Prepara-te", "sem_notificacoes": "Sem notificações disponíveis até ao momento", "tela_sobre": {"titulo": "Sobre", "about_args": "O {} é uma ferramenta que te vai auxiliar nos teus objetivos fitness. Através dele, terás total controlo sobre os teus treinos e acesso a uma infinidade de ferramentas que vão garantir que atinges uma qualidade de vida melhor.", "politica_privacidade": "Política de privacidade", "topicos_politica_privacidade": "Ver todos os tópicos da nossa política de privacidade", "versao": "Vers<PERSON>", "nome_empresa": "WAGI Tecnologia LTDA", "biliotecas": "Bibliotecas de código aberto", "bibliotecas_utilizadas": "Bibliotecas que utilizamos"}, "modais_agenda": {"o_que_deseja_fazer": "O que deseja fazer?", "aviso_reagendar_desmarcar": "Pode reagendar ou desmarcar este agendamento", "reagendar": "Reagendar", "desmarcar": "<PERSON><PERSON><PERSON>", "confirmar_agendamento": "Confirmar o agendamento?", "aviso_confirmacao": "Ao clicar em confirmar, será adicionado ao agendamento"}, "tela_agendar": {"titulolocalization": "Agenda"}, "dias_horararios_disponiveis": " Dias com horários disponíveis", "erro_horarios_disponiveis": "Ops, não encontramos \nnenhum horário disponível", "tente_selecionar_outra_data": "Tente selecionar outra data", "aulasE_turmas": {"selecione_numero_contato": "Selecione o número para contato", "telefone": "Telefone", "launchWhatsApp_message_args": "<PERSON><PERSON><PERSON> {}, aqui <PERSON> {}", "novo_modelo_confirmacao": "Novo modelo de confirmação", "aviso_confirmar_presenca_qrcode": "Agora pode confirmar a presença do aluno utilizando o QR Code.", "confianca": "Confiança ", "novo_modelo_alunos_presentes_confirmado": "Com o novo modelo, apenas os alunos presentes serão confirmados.", "permissao_camera": "Permissão à câmera", "subtitulo_permissao_camera": "Para utilizar o recurso, precisamos do acesso à câmera", "recurso_indisponivel": "Recurso indisponível", "necessario_permissao_camera": "O recurso precisa de permissão da câmera e acesso ao armazenamento do celular para ser utilizado", "encerrar_ou_continuar_confirmando_presencas": "<PERSON><PERSON><PERSON> encerrar ou continuar confirmand<PERSON> as presença<PERSON>?", "continuar_confirmando": "<PERSON><PERSON><PERSON><PERSON>ando", "encerrar": "En<PERSON><PERSON>", "presenca_confirmada": "Presença confirmada com sucesso!", "posicione_camera": "Posicione a câmera", "aponte_camera": "Aponte a câmera para o QR Code do aluno e aguarde a leitura", "deseja_cancelar_args": "Realmente deseja des<PERSON> {} da aula?", "sim_desmarcar": "<PERSON><PERSON>, desmar<PERSON>", "escolha_data": "Escolha uma data", "turmas_agendadas": "<PERSON><PERSON><PERSON>", "selecione_unidades_args": "Selecione uma das {} unidades", "add_a_args": "{} foi adicionado a {}", "add_aluno": "<PERSON><PERSON><PERSON><PERSON>", "add_aluno_aula": "<PERSON><PERSON><PERSON><PERSON>", "pesquise_por_nome": "Pesquise por nome", "sem_alunos": "Sem alunos disponíveis", "sem_alunos_encontrados": "Sem alunos encontrados", "aulas_agendadas": "<PERSON><PERSON>", "apresente_qrcode_confirme_presenca": "Apresente esse código ao seu professor\npara confirmar sua presença na aula", "confirmar_presenca_titulo": "<PERSON><PERSON><PERSON><PERSON>", "não_encontramos_alunos": "Ops, não encontramos \nnenhum aluno!", "minhas_aulas_args": "<PERSON><PERSON>", "em_andamento": "Em andamento", "proximas_aulas": "Pró<PERSON><PERSON> aulas", "finalizadas": "Finalizadas", "sem_aulas_turmas_dia": "Sem aulas/turmas para o dia", "puxe_para_baixo_att": "Puxe para baixo para atualizar"}, "confirmar_presenca": "<PERSON><PERSON><PERSON><PERSON>", "tela_avaliacao_agendamento": {"titulolocalization": "Agendamento", "selecione_horario": "Selecione um horário"}, "tela_avaliacao_fisica": {"titulolocalization": "Avaliação física", "ultima_avaliacao_args": "Última avaliação física realizada em {}", "proxima_avaliacao_args": "Próxima avaliação física agendada para {}", "agendar_avaliacao": "Agendar <PERSON>", "resumo_avaliacao": "Resumo avaliação", "falha_consulta_avaliacoes": "Falha ao consultar avaliações", "nao_possui_avaliacoes_args": "{} não possui avaliações físicas", "itens_dropdown": {"direito": "<PERSON><PERSON><PERSON>", "esquerdo": "<PERSON><PERSON><PERSON>", "outros": "Outros"}, "perimetria": "Perimetria", "dobras_cutaneas": "<PERSON><PERSON><PERSON> cut<PERSON>", "resistencia_muscular": "Resistência muscular", "bracos": "Braços", "abdomen": "<PERSON><PERSON><PERSON>", "imc": "IMC - <PERSON><PERSON><PERSON>", "resultado": "<PERSON><PERSON><PERSON><PERSON>", "situacao": "Situação", "comparativo_pesos": "Comparativo de pesos", "peso_atual": "Peso Atual", "peso_ideal": "Peso Ideal", "peso_gordo": "Peso Gordo", "peso_magro": "Peso Magro", "composicao_corporal": "Composição corporal"}, "info_IMC": {"indice_massa_corporal": "<PERSON><PERSON><PERSON> de massa corporal", "seu_imc_args": "Seu IMC: {}", "texto_imc": "O índice de massa corporal (IMC) é uma medida da gordura corporal com base na sua altura e peso", "abaixo_peso": "Abaixo do Peso", "peso_normal": "Peso Normal", "sobrepeso": "Sobrepeso", "obesidade": "Obesidade"}, "tela_detalhes_agendamento": {"titulolocalization": "<PERSON><PERSON><PERSON><PERSON><PERSON> di<PERSON>oní<PERSON>", "confirmar_agendamento_args": "Ao confirmar o agendamento, será gerada uma parcela no valor de {}", "sucesso_agendamento": "Agendamento realizado com sucesso", "marcar": "<PERSON><PERSON>"}, "tela_feedback": {"como_esta_sendo_sua_xp_com_app": "Como está sendo a sua experiência com o seu app?", "isso_ajudara_entregarO_melhor_para_vc": "<PERSON><PERSON> a<PERSON> a entregar sempre o melhor para você.", "gostaria_de_deixar_um_comentario": "Gostaria de deixar um comentário?", "responder_depois": "Responder depois", "seu_feedback_e_importante_para_nos": "Seu feedback é importante para nós", "enviar_feedback": "<PERSON><PERSON><PERSON>", "o_que_esta_errado": "O que está errado?", "como_podemos_melhorar": "Como podemos melhorar?", "motivos": {"suporte": "Suporte", "treinos": "<PERSON><PERSON><PERSON><PERSON>", "lentidao": "Len<PERSON>dão", "travamentos": "Travamentos", "usabilidade": "Usabilidade", "outros": "Outros"}}, "avaliar_professor": {"selecione_professor": "<PERSON><PERSON><PERSON><PERSON> um(a) professor(a)", "alterar_professor": "<PERSON><PERSON><PERSON>(a)", "orientacao_avaliacao": "A quantidade de estrelas deve ser entre 0 e 5", "sua_avaliacao_foi_salva": "Sua avaliação foi salva :)", "titulo_lista_professor": "<PERSON><PERSON><PERSON><PERSON> o professor", "pesquise_por_nome": "Pesquise por nome", "nenhum_professor_encontrado": "Nenhum professor en<PERSON><PERSON><PERSON>"}, "badges": {"titulolocalization": "30 dias concluídos", "medalha_30_dias": "Medalha obtida por treinar 30 dias consecutivos na academia."}, "beber_agua": {"informe_quantidade": "Informe a quantidade", "titulo_tela_beber_agua": "<PERSON><PERSON>", "vamos_comecar": "<PERSON><PERSON><PERSON> começar", "configurar_meta_diaria": "Para utilizar o beber água é preciso configurar sua meta diária. Vamos começar?", "vamos_la": "Vamos lá", "objetivo_args": "Objetivo: {}", "estatisticas_semana": "Estatísticas da semana", "data_nascimento": "Data de nascimento", "acorda_as": "Acorda às", "sua_meta_diaria": "Sua meta diária", "meta_diaria_indicada_args": "Meta diária indicada é de {}", "ativar_lembretes": "Ativar lembretes?", "o_app_ira_exibir_lembretes": "O aplicativo irá exibir lembretes para você beber <PERSON>.", "horarios_lembretes": "Faixa ho<PERSON><PERSON><PERSON> dos <PERSON>", "intervalo_minimo_lembretes": "Intervalo m<PERSON>", "beber_agua_esta_pronto": "O beber água está pronto para ser usado", "tela_configurar_beber_agua_titulo": "Estatísticas", "tela_configurar_beber_agua_subtitulo": "Relatório semanal de consumo", "hidratacao": "Hidratação", "semana": "Se<PERSON>", "meta_semanal": "<PERSON>a semanal", "consumido": "<PERSON><PERSON><PERSON><PERSON>", "sem_estatistcas": "Sem estatísticas até o momento"}, "editarColaborador": {"tela_minha_conta_titulo": "Minha conta", "trocar_foto_perfil": "Trocar foto de perfil", "informacoes": "Informações", "nome": "Nome", "nome_de_usuario": "Nome de usuário", "codigo_empresa": "<PERSON>ódigo <PERSON>", "formas_de_pagamento": "Formas de pagamento", "meios_registrados": "Meios de pagamento registrados", "nenhum_meio_registrado": "Nenhum meio de pagamento registrado", "add_novo_cartao": "Adicionar novo cartão", "nao_foi_possivel_carregar_os_dados": "Não foi possível carregar os dados", "verifique_conexao_tente_novamente": "Verifique sua conexão com a internet, puxe para baixo para tentar novamente", "conta_agencia_banco_args": "Conta: {} Ag: *** *** ***", "tela_manter_senha_titulo": "<PERSON><PERSON><PERSON>", "senha_nao_pode_ser_vazia": "A senha digitada não pode ser vazia", "senha_nao_bate": "A senha digitada não confere com a confirmação de senha", "senha_atual_deve_ser_inserida": "A senha atual deve ser inserida", "senha_alterada_com_sucesso": "Senha alterada com sucesso", "senha_atual": "<PERSON><PERSON> atual", "nova_senha": "Nova Senha", "confirmacao_nova_senha": "Confirmação da nova Senha", "tela_dados_contato_titulo": "Dados de contato", "email_deve_ser_valido": "O email deve ser um endereço válido", "telefone_deve_ser_valido": "O Telefone deve ser válido", "dados_alterados_com_sucesso": "Dados Alterados com sucesso", "cadastrar_cartao": "<PERSON>ada<PERSON><PERSON>", "editar_cartao": "<PERSON><PERSON>", "meu_cartao_de_credito": "Meu <PERSON> de crédito", "numero_cartao": "Número do cartão", "nome_impresso_cartao": "Nome impresso no cartão", "adica_edica_cartao_realizada_args": "A {} do cartão foi realizada", "tela_editar_perfil_titulo": "<PERSON><PERSON>", "trocar_foto": "Trocar foto", "digite_nome_aluno": "Digite o nome do aluno", "nome_aluno": "Nome", "digite_email": "Digite o email do aluno", "e-mail": "Email", "numero_telefone": "Número do telefone celular", "celular": "<PERSON><PERSON><PERSON>", "data_nascimento": "Data Nascimento", "tudo_pronto": "<PERSON><PERSON> pronto", "dados_editados_sucesso": "Os dados foram editados com sucesso", "tela_creditos_contrato_titulo": "Créditos do contrato", "sem_extratos_para_o_filtro": "Sem extratos para o filtro", "tela_confirmar_trancamento_titulo": "Confirmar o trancamento", "infos_trancamento": "Informações de trancamento", "taxa_trancamento": "Taxa de trancamento", "dias_trancados": "Dias trancados", "dias_usados": "Dias usados", "contrato_trancado_sucesso": "O seu contrato foi trancado com sucesso", "confirmar_trancamento": "Confirmar trancamento", "tirar_foto": "Tirar foto", "escolher_foto": "Escolher foto", "remover_foto": "Remover foto", "data_nasc": "Data nasc."}, "editar_perfil": {"tela_editar_perfil_titulo": "<PERSON><PERSON> perfil", "dados_pessoais": "<PERSON><PERSON> pessoais", "dados_basicos": "Dados básicos", "dados_corporal": "Informação corporal", "data_nascimento": "Data de Nascimento", "senha_atual": "<PERSON><PERSON> atual", "nova_senha": "Nova Senha", "confirmacao_nova_senha": "Confirmação da Nova Senha", "senhas_nao_coincidem": "As senhas não coincidem", "e-mail": "Email", "insira_email_valido": "Insira um email válido", "celular": "<PERSON><PERSON><PERSON>", "telefone": "Telefone", "adicionar_cartao_credito": "Adicionar <PERSON> de crédito", "adicionar_cartao": "<PERSON><PERSON><PERSON><PERSON>", "editar_cartao": "<PERSON><PERSON>", "erro_ao_solicitar_servico": "Ocorreu um erro ao solicitar o serviço", "sexo_biologico": "Sexo biológico", "vencimento": "Vencimento", "nome_impresso_cartao": "Nome impresso no cartão", "data_vencimento_formato_invalido": "Formato de data de vencimento inválido", "data_vencimento_ja_expirou": "Data de vencimento já expirou", "treinos_por_semana": "T<PERSON><PERSON>s por semana"}, "crm": {"titulo_tela_detalhes_notificacoes": "Visualizar", "sua_resposta": "Sua resposta", "preciso_texto_para_enviar_como_resposta": "É preciso colocar algum texto para enviar como resposta"}, "conversorDeMassa": {"titulolocalization": "Conversor", "converta_kg_lbs": "Converta Kg para Libras e vice-versa", "kg": "Quilograma (Kg)", "lbs": "Libras (Lb)"}, "chat": {"voce_pode": "Você pode iniciar uma nova conversa a qualquer momento", "sem_conversas": "Sem conversas", "tela_contatos_chat_titulo": "Nova conversa", "nao_ha_resultados": "Não há resultados", "pesquise_por_outro_nome": "Pesquise por outro nome"}, "cadastro_de_aluno": {"editando_aluno": "<PERSON><PERSON><PERSON>", "cadastrar_aluno": "<PERSON><PERSON><PERSON><PERSON>", "foto": "foto", "trocar_foto": "Trocar foto", "aluno_ativo": "<PERSON><PERSON> ativo", "se_desmarcado": "Se desmarcado, o aluno será inativado", "os_campos_com": "Os campos com * são obrigatórios", "convite": "Vamos convidar {} para usar o seu app", "convidar_via": "Convidar via", "salvar_edicoes": "<PERSON>var <PERSON>", "sexo": "* Sexo", "tela_manter_objetivo_titulo": "Selecione um objetivo", "erro_conculta_objetivos": "Ops! Não foi possível consultar os objetivos", "sem_objetivos": "Sem objetivos Registrados", "objetivo_aluno_salvo": "O Objetivo do aluno foi salvo", "salvar_objetivo": "Salvar o objetivo"}, "pasta_contrato": {"titulo_form_dados_cartao": "Informe os dados de cobrança", "num_cartao": "Número do cartão *", "titular_cartao": "Titular do cartão *", "nome_que_consta_no_cartao": "Digite aqui o nome que consta no cartão", "cpf_titular": "CPF do Titular do cartão", "cpf_invalido": "CPF inválido", "validade": "Validade *", "mes_ano": "Mês / Ano", "validade_invalida": "Validade inválida", "digite_aqui": "Digite aqui", "campos_obrigadorios": "Responda às perguntas obrigatórias (*) para poder prosseguir.", "infos_adicionais_titulo": "Informações adicionais", "legenda": "Confirme e informe os dados necessários", "vencimento_fatura": "Vencimento de fatura", "selecione_dia_do_venvimento": "Selecione o dia do vencimento", "todo_dia": "Todo dia", "cupom_desconto": "Cupom de desconto", "form_dados_pessoais_titulo": "Informe os dados", "nome_completo": "Nome completo", "data_nascimento": "Data de nascimento", "cpf": "CPF", "email_exemplo": "<EMAIL>", "num_celular": "Número de telefone celular", "todos_os_campos_com_sao_obrigatorios": "Todos os campos com * são obrigatórios", "resumo_seu_pedido": "Resumo do seu pedido", "plano_selecionado": "Plano selecionado", "1_parcela": "1° parcela", "moeda_mes_args": "R$ {} / mês", "duracao_contrato_args": "{} dias", "1_parcela_args": "R$ {}", "valor_matricula_args": "R$ {}", "valor_anuidade": "R$ {}", "detalhar_parcelas": "<PERSON>al<PERSON> parcelas", "ocultar_parcelas": "Ocultar parcelas", "parcela_args": "{}° parcela {}", "dados_pessoais": "<PERSON><PERSON>", "num_de_telefone": "Número de telefone", "dados_cartao": "Dados do cartão", "tipo_cobranca": "Tipo de cobrança", "recorrencia_mensal": "Recorrência mensal", "ativar_agora": "Ativar Agora", "li_e_aceito_os_termos_do_contrato": "Li e aceito os termos de contrato", "leia_contrato": "Leia o contrato deste plano", "erro_acao": "Não foi possível realizar a ação, tente novamente mais tarde", "deseja_abandonar_compra_de_plano": "Deseja abandonar a compra de plano?", "passo_args": "Passo {} de 4", "informe_dados_pessoais": "Informe os dados necessários", "tela_contrato_do_plano_titulo": "Contrato do plano", "erro_consulta_contrato": "Não foi possível consultar o contrato", "erro_tente_novamente": "Tivemos um problema ao consultar o contrato, puxe para baixo para tentar novamente", "TelaOpcoesDeCompraNovoContrato_titulo": "Selecione o melhor plano", "essas_sao_as_acoes": "<PERSON><PERSON><PERSON> as opções de plano da sua academia. Escolha o plano que desejar!", "erro_consulta_dados": "Não foi possível consultar os dados", "sem_planos": "Sem planos", "nao_ha_planos_disponiveis": "No momento não há planos disponíveis para compra", "mes": "/ mês", "duracoes": "Dura<PERSON> de {} meses", "duracao": "<PERSON><PERSON><PERSON> de {} mês", "anuidade_args": "Anuidade {}", "meses_arg": "{} mês", "meses_args": "{} meses", "esse_plano_inclui": "Esse plano inclui", "com_esse_contrato": "Com este contrato, você adquire {} créditos. Verifique com a sua academia as condições de uso.", "escolher_esse_plano": "Escolher este plano", "parcelas_vencidas": "<PERSON><PERSON><PERSON><PERSON> venc<PERSON>", "valor_dolar_real": "R$ {}", "total": "Total", "pagar_parcelas": "Pagar parcelas", "valor_total": "Valor total", "pagamento_recusado_subtituloMensagem": "Verifique as suas informações de pagamento e tente novamente.", "pagamento_recusado_tituloMensagem": "Ops, algo deu errado…", "pagamento_aprovado_subtituloMensagem": "Agora você não tem mais nenhuma parcela em atraso", "pagamento_aprovado_tituloMensagem": "Pagamento realizado com sucesso!", "pagar_agora": "Pagar agora", "que_sera_cobrado": "O que será cobrado", "forma_pagamento": "Forma de pagamento"}, "showDialog_contrato": {"subtitulo_vencido_naoRenovar": "Vá até a recepção para renovar seu plano e começar com tudo!", "subtitulo_vencido": "Renove seu plano para voltar a aproveitar tudo o que sua academia tem a oferecer!", "titulo_vencido": "Seu plano {} venceu!", "renove_agora": "<PERSON><PERSON> agora", "outro_plano": "Escolher outro plano", "subtitulo_Avencer": "Lembre-se de renová-lo para não perder acesso aos serviços da sua academia. Você pode fazer isso quando quiser direto do seu perfil.", "subtitulo_Avencer_naoRenovar": "Lembre-se de renová-lo para não perder acesso aos serviços da sua academia. Você pode fazer isso direto na recepção ou você pode escolher outro plano para começar.", "titulo_avencer_dias": "Seu plano {} ir<PERSON> vencer em {} dias", "titulo_avencer_dia": "Seu plano {} ir<PERSON> vencer em {} dia", "titulo_avencer_hoje": "Seu plano {} ir<PERSON> vencer hoje", "subtitulo_visitante": "Para poder treinar e aproveitar tudo que sua academia tem a oferecer, antes de tudo você precisa escolher um plano para começar", "titulo_visitante": "Prepare-se para começar!", "visitante_recepcao": "Vá na recepção de sua academia e escolha o melhor plano para você", "subtitulo_cancelado": "Volte com foco total em conquistar uma vida mais saudável e escolha um novo plano.", "subtitulo_cancelado_desistente": "Volte com foco total em conquistar uma vida mais saudável! Vá até a recepção e ative seu plano.", "titulo_cancelado": "Seu plano venceu!", "conferirplano": "Conferir planos", "escolha_seu_plano": "Escolha seu plano"}, "historico_parcela": {"em_aberto": "<PERSON>", "canceladas": "Canceladas", "pagas": "Pagas", "histórico_parcela": "Histórico de parcelas", "sem_parcela": "Sem Parcela<PERSON> no momento", "text_sem_parcelas": "Você não tem nenhuma parcela nessa situação.", "parcela": "<PERSON><PERSON><PERSON>", "validade": "Validade"}, "drawer_esquerdo": {"ver_perfil": "<PERSON><PERSON>", "meu_dia": "<PERSON><PERSON>", "minha_agenda": "<PERSON><PERSON>", "deseja_deslogar": "<PERSON><PERSON><PERSON>?", "ao_clicar_em_sair": "Ao clicar em sair você não estará mais logado no app", "deslogar": "<PERSON><PERSON><PERSON>"}, "exercicios": {"desativar": "Desativar", "mensagem_app_bar": "{} do treino", "sem_foto": "Sem foto", "sem_nome": "Sem nome", "anaerobico": "Anaeróbico", "aerobico": "Aeróbico", "com_args": "{} com {}", "da_biblioteca_de_atividades": "{}, da biblioteca de atividades", "biblioteca_de_imagens": "Biblioteca de imagens", "add_imagem_args": "Adicionar {} imagem{} ao exerc<PERSON>cio", "grupos_musculares": "Grupos musculares", "selecione_os_grupos": "Selecione os grupos para o exercício", "parece_que_vc_esta_sem_conexao": "Parece que você está sem conexão com a internet", "tipo_atividade": "Tipo da atividade", "treino_livre": "T<PERSON>ino livre", "com_equipamento": "Com equipamento", "link_video_youtube": "<PERSON> do vídeo (YouTube)", "metodo_de_execucao": "Método de execução", "fotos_e_videos": "Fotos e vídeos", "remover_video": "Remover Vídeo?", "grupos_musculares_s": "Grupo(s) muscular(es)", "sem_grupo_definidos": "Sem grupo definido", "exercicios.preencha_os_campos_corretamente": "Preencha os campos e tente novamente", "atividade_foi_salva": "A atividade foi salva", "cadastrar_outra_atividade": "Cadastrar outra atividade", "remover_imagem": "Remover imagem?", "TODAS": "TODAS", "todos": "Todos", "SQ": "SQ", "CQ": "CQ", "sem_equipamento": "Sem equipamento", "sem_exs_encontrados": "Sem exercícios encontrados", "tente_pesquisar_por_outro_termo": "Tente pesquisar por outro termo", "mensagem_args": "A atividade {} foi desativada"}, "dasBoardColaborador": {"media_execucas_dos_treinos": "Média de execuções de treinos", "nos_ultimos_30_dias": "nos últimos 30 dias", "resultados_ultimos_7_dias": "Resultados nos últimos 7 dias", "porcentagem_realizados_args": "{} realizados ", "treinos_executados": "Treinos executados", "porcentual_execucao_por_aluno": "Percentual de execução por aluno", "alunos_encontrados_args": "{} <PERSON><PERSON><PERSON> encontrados ", "treinos_concluidos_args": "{} de {} <PERSON><PERSON><PERSON><PERSON> conc<PERSON>", "todas_avaliacoes": "Todas avaliações", "numero_telefone_aluno_invalido": "O Número de telefone desse aluno é inválido", "avaliacao_dos_treinos_ultimos_7_dias": "Avaliações dos treinos\nnos últimos 7 dias", "ola_args": "<PERSON><PERSON><PERSON>, {}", "falha_consulta_dashboard": "Falha ao consultar dashboard", "parabens_vc_concluiu_os_desafios": "Parabéns!\nVocê concluiu todas os desafios", "seja_mestre_no_args": "Seja mestre no {}!", "realizar_proximo_desafio": "Realizar próximo desafio", "meus_programas_de_treino": "Meus programas de treino", "a_renovar": "A vencer", "programas_de_treino": "Programas de treino", "da_minha_carteira": "da <PERSON>ha carteira", "nivel_de_satisfacao": "Nível de Satisfação", "presquicoes": "Prescrições", "total_a_fazer": "Total a fazer ", "renovacoes": "Renovações ", "sem_treino": "<PERSON><PERSON> ", "vc_possui": "<PERSON><PERSON><PERSON> possui ", "alunos_args": "{} al<PERSON>s ", "feedback_dos_treinos": "Feedback dos Treinos", "em_dia": "Em dia"}, "tela_concluir_treino": {"vc_concluiu_seu_treino_de_hoje": "Você concluiu seu treino de hoje!", "avalie_seu_treino_de_hoje": "Avalie seu treino de hoje!", "comente_algo": "Comente algo"}, "ainda_nao_ha_novidades": "Ainda não há novidades, mas continue acompanhando para não perder nada", "sucesso": "Sucesso!", "gordura_simples": "Gordura", "obrigado": "<PERSON><PERSON><PERSON>", "depois": "<PERSON><PERSON><PERSON>", "genero": "<PERSON><PERSON><PERSON><PERSON>", "ajuda": "<PERSON><PERSON><PERSON>", "acesso": "Ace<PERSON>", "senha": "<PERSON><PERSON>", "usuario": "<PERSON><PERSON><PERSON><PERSON>", "contato": "Contato", "celular": "<PERSON><PERSON><PERSON>", "e-mail": "E-mail", "endereco": "Endereço", "bairro": "Bairro", "CEP": "CEP", "numero": "Número", "complemento": "Complemento", "valiade": "Validade", "nome": "Nome", "extrato": "Extrato", "creditos": "C<PERSON>dit<PERSON>", "periodo": "<PERSON><PERSON><PERSON>", "convidar": "<PERSON><PERSON><PERSON>", "proximo": "Próximo", "resumo": "Resumo", "matricula": "<PERSON><PERSON><PERSON><PERSON>", "anuidade": "Anuidade", "CPF": "CPF", "desconto": "Desconto", "contratos": "Contratos", "opcoes": "Opções", "adesao": "Adesão", "novo": "Novo", "atividade": "Atividade", "adicionar": "<PERSON><PERSON><PERSON><PERSON>", "selecionar": "Selecionar", "atividades": "Atividades", "aguardando": "Aguardando", "em_dia": "Em dia", "vencidos": "<PERSON><PERSON><PERSON><PERSON>", "revisados": "Revisados", "execucoes": "Execuções", "enviar": "Enviar", "parabens": "<PERSON><PERSON><PERSON><PERSON>", "titulo_tela_imc": "<PERSON><PERSON><PERSON> de massa corporal", "seu_imc": "Seu IMC:", "o_imc_e_uma_medida": "O índice de massa corporal (IMC) é uma medida da gordura corporal com base na sua altura e peso", "abaixo_do_peso": "Abaixo do Peso", "baixo": "Baixo", "peso_normal": "Peso Normal", "sobrepeso": "Sobrepeso", "obesidade": "Obesidade", "informe_a_altura": "Informe a altura", "informacoes_nutricionais": "Informações Nutricionais", "adequado_para_dietas": "Adequado para dietas", "essa_combinou_com_seu_plano": "Hey, essa combinou com seu plano!", "possui_a_mesma_quantidade_calorica": "Possui a mesma quantidade calórica que você precisa na sua Refeição X. Deseja substituir?", "foods": "Ali<PERSON><PERSON>", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "portion": "Porção", "portions": "Porções", "fibers": "Fi<PERSON><PERSON>", "adicionar_legenda": "Adicionar legenda", "the_note_cant_be_empty": "A observação não pode ser vazia", "novo_pr": "Novo PR", "todays_result": "Resultado de hoje", "rankings": "Rankings", "your_current_plan_will_be_stoped": "O seu plano atual será interrompido e substituído por esse plano, deseja continuar?", "nutritional_content": "Teor nutritivo", "guidelines": "Orientações", "plans_rating": "Avaliação do Plano", "some_recipes": "Algumas receitas do plano", "meals_a_day": "Refeições por dia", "lasts_for_x_days": "Dura<PERSON> de \n{} dias", "seg": "Seg", "ter": "<PERSON><PERSON>", "qua": "<PERSON>ua", "qui": "<PERSON>ui", "sex": "Sex", "sab": "<PERSON>b", "dom": "Dom", "no_message": "Sem mensagem", "new_terms_and_policy": "Novos termos e políticas", "in_order_to_use_the_app_features": "Atualização dos Termos e Políticas Prezamos pela total transparência. Por isso, para continuar usando seu app é preciso estar de acordo com os novos termos de uso e políticas de privacidade.", "i_agree_with_the": "Concordo com os ", "terms_and_conditions": "Termos e condições", "concordo_com_as": "Concordo com as ", "privacy_policies": "Política de privacidade", "politicas_de": "Políticas de", "accept": "<PERSON><PERSON>", "could_not_add_workout": "Não foi possível gravar seu treino. Tente novamente", "could_not_delete_workout": "Não foi possível excluir o treino", "check_your_position_in_the_rank": "Cheque sua posição no ranking", "edit_result": "<PERSON><PERSON> resultado", "register_result": "Registrar resultado", "no_register_yet": "Nenhum registro ainda", "be_the_first_to_register_a_result": "<PERSON><PERSON> o primeiro a registrar", "register": "Registrar", "there_are_no_registers": "Não há registros para esses filtros e datas", "so_all_of_the_app_features": "Para proteger sua privacidade e garantir a segurança de suas informações pessoais, ao continuar, você concorda com a:", "data_processing": "Processamento de dados", "Execucoes_de_treino": "Execuções de treino", "manha": "Manhã", "tarde": "Tarde", "noite": "Noite", "maior_movimentacao": "<PERSON><PERSON>", "menor_movimentacao": "<PERSON><PERSON>", "nenhuma_execucao": "Nenhuma execução ainda", "text_empty_execucao": "assim que um aluno executar uma ficha que você cadastrou ela aparecerá aqui.", "class": "<PERSON><PERSON>", "lista_presenca": "Lista de presença", "nenhum_aluno": "Nenhum aluno registrado ainda", "ativos": "Ativos", "inativos": "Inativos", "agendadas": "<PERSON><PERSON><PERSON>", "avaliacoes_em_dias": "Avaliações realizadas:", "ativos_sem_avaliacao": "Ativos sem avaliação", "there_are_no_events": "Não há eventos", "matricula_arguments": "Matrícula: {}", "prof_abreviacao": "Prof. {}", "no_trainer": "Sem professor", "argument_years_old": "{} anos", "hello_argument": "<PERSON><PERSON><PERSON>, {}", "get_in_touch": "Entrar em contato", "member_history": "Histórico do aluno", "no_goals": "Sem objetivos", "objetivo": "Objetivo", "member_level": "Nível do aluno", "member_level_formated": "Nível do aluno", "no_level": "<PERSON><PERSON> n<PERSON><PERSON>", "levels": "Níveis", "workouts_history": "Histórico de treinos", "SEG": "SEG", "TER": "TER", "QUA": "QUA", "QUI": "QUI", "SEX": "SEX", "SAB": "SAB", "DOM": "DOM", "procure_por_nome_ou_matricula": "Procure por nome ou matr<PERSON>cula", "escreva_sua_obs": "Escreva aqui sua observação", "remark": "Observação", "dias_que_treinou": "Dias que treinou", "treino_em_dia": "Treino em dia", "treino_vencido": "<PERSON><PERSON><PERSON> vencido", "treinou": "Treinou", "agendamento": "Agendamento", "alterou_status": "Alterou Status do Agendamento", "renovou_treino": "Renovou Treino", "acabou_treino": "Acabou Treino", "notificacao": "Notificação", "montou_treino": "<PERSON><PERSON>", "mudou_de_nivel": "<PERSON><PERSON><PERSON>", "fez_aula": "<PERSON><PERSON>", "realizou_avaliacao": "Realizou Avaliação", "registrou_wod": "Registrou Wod", "agendou_aula_booking": "Agendou Aula Via Booking", "massa_magra": "<PERSON>a magra", "massa_gorda": "<PERSON>a gorda", "abdominal": "Abdominal", "supra_iliaca": "Supra-ilíaca", "peitoral": "Peitoral", "triceps": "Tríceps", "coxa_medial": "Coxa Medial", "subescapular": "Subescapular", "axilar_media": "Axilar média", "biceps": "Bíceps", "antebraco_dir": "<PERSON><PERSON><PERSON><PERSON><PERSON> dir.", "braco_dir_relaxado": "Braço relaxado dir.", "braco_dir_contraido": "Braç<PERSON> contraído dir.", "coxa_media_dir": "<PERSON>a média dir.", "panturrilha_dir": "<PERSON><PERSON><PERSON><PERSON> dir.", "coxa_distal_dir": "<PERSON>a distal dir.", "coxa_proximal_dir": "<PERSON>a proximal dir.", "antebraco_esq": "Antebraço esq.", "braco_esq_relaxado": "Braço relaxado esq.", "braco_esq_contraido": "Braço contraído esq.", "coxa_media_esq": "Coxa média esq.", "panturrilha_esq": "Panturrilha esq.", "coxa_distal_esq": "Coxa distal esq.", "coxa_proximal_esq": "Coxa proximal esq.", "pescoco": "Pescoço", "ombro": "Ombro", "quadril": "Quadril", "torax_busto": "Tórax / Busto relaxado", "cintura": "Cintura", "gluteo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "circunferencia_abdominal": "Circunferência abdominal", "segunda_manha": "Segunda de manhã", "segunda_tarde": "Segunda de tarde", "segunda_noite": "Segunda de noite", "terca_manha": "<PERSON><PERSON><PERSON>", "terca_tarde": "<PERSON><PERSON><PERSON> de <PERSON>", "terca_noite": "<PERSON><PERSON><PERSON> de no<PERSON>", "quarta_manha": "Quarta de man<PERSON>", "quarta_tarde": "Quarta de tarde", "quarta_noite": "Quarta de noite", "quinta_manha": "<PERSON><PERSON><PERSON> de <PERSON>", "quinta_tarde": "<PERSON><PERSON><PERSON> de tarde", "quinta_noite": "<PERSON><PERSON><PERSON> de noite", "sexta_manha": "Sexta de manhã", "sexta_tarde": "Sexta de tarde", "sexta_noite": "Sexta de noite", "sabado_manha": "Sábado de <PERSON>", "sabado_tarde": "Sábado de tarde", "sabado_noite": "Sábado de noite", "domingo_manha": "<PERSON>", "domingo_tarde": "Domingo <PERSON>", "domingo_noite": "Domingo de no<PERSON>", "minha_carteira": "<PERSON><PERSON>", "na_academia": "Na academia", "na_academia_a_vencer": "Na academia com treino a vencer", "na_academia_vencido": "Na academia com treino vencido", "na_academia_minha_carteira": "Na academia da minha carteira", "na_academia_sem_treino": "Na academia sem treino", "sem_treino": "<PERSON>m treino", "bracos": "Braços", "excelente": "Excelente", "abaixo_da_media": "<PERSON><PERSON><PERSON><PERSON> da média", "acima_da_media": "<PERSON><PERSON><PERSON> da média", "media": "Média", "weak": "Fraca", "status_do_aluno": "Status do aluno", "evento": "Evento", "seu_programa": "Seu Programa", "adicionar_ao_aluno": "Adicionar ao aluno", "programa_de_treino_adicionado": "Programa de treino adicionado ao aluno com sucesso.", "nao_foi_possivel_remover_ficha": "Não foi possível excluir a ficha selecionada, tente novamente em alguns instantes.", "ops_nao_foi_possivel_excluir_a_ficha": "Ops! Não foi possivel excluir a ficha", "ao_clicar_em_excluir_a_ficha_sera_excluida": "Ao clicar em excluir, a ficha será excluída da base e não poderá ser desfeito.", "atividades_args": {"one": "{} atividade", "two": "{} atividades", "many": "{} atividades", "other": "{} atividades"}, "sem_categoria": "Sem categoria", "salvo_automaticamente_as": "Salvo automaticamente às {}", "selecionar_ficha_predefinida": "Selecionar ficha predefinida", "nenhuma_ficha_cadastrada": "<PERSON>enhuma ficha cadastrada ainda", "comece_adicionando_sua_primeira": "Comece adicionando sua primeira ficha aqui para montar o seu programa de treino", "adicionar_ficha": "<PERSON><PERSON><PERSON><PERSON>", "programa_criado_com_sucesso": "Programa criado com sucesso", "o_que_fazer": "O que fazer?", "cadastrar_nova_ficha": "Cadastrar nova ficha", "seu_treino": "<PERSON><PERSON>", "se_voce_sair_sem_salvar": "Se você sair sem salvar as alter<PERSON><PERSON><PERSON><PERSON>, elas serão perdidas. Tem certeza que deseja sair sem salvá-las?", "sair_sem_salvar_alteracoes": "Sair sem salvar?", "voce_quer_add_a_ficha_a_aluno": "Você quer adicionar a ficha a um aluno?", "salvo_com_sucesso_as": "Salvo com sucesso às {}", "vincular_atividades": "Vincular atividades", "series_args": {"zero": "0 séries", "one": "1 série", "other": "{} séries"}, "sem_serie": "Sem série", "alterar_metodo": "<PERSON><PERSON><PERSON>", "editar_nome": "Editar nome", "adicionar_nome_atividade": "Adicionar nome na atividade", "ficha_salva_automaticamente_as": "Ficha salva automaticamente às {}", "selecione_as_atividades_para": "Selecione as atividades para {}", "selecione_as_atividades_para_com": "{} com {} e {}", "metodo_com_atividades": "{} com {}", "sem_serie_cadastrada": "Sem série cadastrada", "velocidade_abreviado": "vel", "nada_por_aqui_ainda": "Nada por aqui ainda", "comece_adicionando_atividades": "Comece adicionando atividades aqui para montar a sua ficha de treinos", "adicionar_atividades": "Adicionar ativida<PERSON>", "configurar_padrao_de_serie": "Configurar <PERSON> de Série", "rep": "Repetição", "gerar_as_series_nesta_atividade": "<PERSON><PERSON><PERSON> as séries nesta atividade", "nao_gerar_padrao": "<PERSON>ão gerar padr<PERSON>", "adicionar_ao_treino": "Adicionar ao treino", "pesquisar_por_nome": "Pesquisar por nome", "exibir_padrao_de_serie": "<PERSON><PERSON><PERSON> s<PERSON>", "esconder_padrao_de_serie": "Esconder padrão de série", "ops_nao_encontramos_atividade": "Ops, não encontramos nenhuma atividade!", "salvar_ficha": "<PERSON><PERSON>", "programa_de_treino_excluido": "Programa de treino excluído!", "busque_pelo_nome_programa": "Busque pelo nome do programa", "ops_nao_encontramos_programa": "Ops, não encontramos nenhum programa!", "resultado_args": {"zero": "<PERSON><PERSON><PERSON><PERSON>", "one": "<PERSON><PERSON><PERSON><PERSON>", "two": "Resul<PERSON><PERSON>", "other": "Resul<PERSON><PERSON>"}, "nenhum_programa_cadastrado_ainda": "Nenhum programa cadastrado ainda", "comece_criando_sua_primeira_ficha": "Comece criando sua primeira ficha aqui para montar a sua biblioteca", "comece_criando_seu_primeiro_programa": "Comece criando seu primeiro programa aqui para montar a sua biblioteca", "criar_nova_ficha": "Nova ficha de treino", "criar_novo_programa_de_treino": "Novo programa de treino", "nao_foi_possivel_excluir_programa_tente_novamente": "Não foi possível excluir o programa selecionado, tente novamente em alguns instantes.", "nao_foi_possivel_excluir_programa": "Ops! Não foi possível excluir o programa", "ao_clicar_excluir_o_programa_sera_excluido": "Ao clicar em excluir, o programa será excluído da base e não poderá ser desfeito.", "ficha_args": {"zero": "0 fichas", "one": "1 ficha", "other": "{} fichas"}, "todos_os_generos": "Todos os Gêneros", "voce_precisa_preencher_nome": "Você precisa preencher o nome antes de continuar", "voce_precisa_preencher_quantidade": "Você precisa preencher a quantidade de aulas previstas antes de continuar", "cade_o_nome": "Oops, cadê o nome?", "cade_as_aulas": "Oops, cadê as aulas previstas?", "criar_programa": "Criar programa", "salvar_alteracoes": "<PERSON><PERSON>", "nome_do_programa": "Nome do programa*", "dias_por_semana": "Dias por semana*", "aulas_previstas": "<PERSON><PERSON>*", "o_programa_foi_predefinido": "O programa de treino foi predefinido com sucesso!", "voce_precisa_editar_o_nome_programa": "Você precisa editar o nome do programa, salvar e depois predefinir novamente.", "ja_existe_programa_com_esse_nome": "Oops, já existe um programa com este nome.", "predefinir_esse_programa": "Predefinir esse programa", "voce_precisa_preencher_o_nome": "Você precisa preencher o nome antes de continuar", "criar_ficha": "<PERSON><PERSON><PERSON> <PERSON>", "nome_da_ficha": "Nome da ficha*", "a_ficha_foi_predefinida": "A ficha foi predefinida com sucesso!", "voce_precisa_editar_nome_da_ficha": "Você precisa editar o nome da ficha, salvar e depois predefinir novamente.", "ja_existe_ficha_com_esse_nome": "Oops, já existe uma ficha com este nome.", "predefinir_ficha": "Predefinir ficha", "selecionar_programa": "Selecionar Programa", "atividade_sem_foto": "Atividade sem foto", "atividade_sem_nome": "Atividade sem nome", "aplicar_padrao_nas_outras_series": "Aplicar este padrão nas outras séries", "remover_serie_atual": "Remover série atual", "adicionar_serie": "Adicionar <PERSON>", "digite_aqui_obs_exercicio": "Digite aqui a observação do exercício", "nenhuma_obs_ainda": "Nenhuma observação ainda", "cadastre_uma_nova_observacao": "Cadastre uma nova observação sobre esse aluno", "nenhum": "<PERSON><PERSON><PERSON>", "piramide_decrescente": "Pirâ<PERSON><PERSON>", "piramide_crescente": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "circuito": "Circuito", "isometrico": "Isométrico", "super_serie": "Super-série", "bi_set": "BI-Set", "tri_set": "TRI-Set", "drop_set": "Drop-Set", "ondulatorio": "On<PERSON>lat<PERSON><PERSON>", "progressao_dupla": "Progressão Dupla", "de_lorme": "<PERSON>", "erpad": "Erpad", "parcelado": "<PERSON><PERSON><PERSON><PERSON>", "duplamente_parcelado": "<PERSON><PERSON><PERSON><PERSON> parcelado", "triplamente_parcelado": "<PERSON><PERSON><PERSON>", "puxe_empurre": "Puxe-Empurre", "repeticao_roubada": "Repetição roubada", "repeticao_forcada": "Repetição Forçada", "repeticao_parcial": "Repetição Parcial", "pique_de_contracao": "Pique de Contração", "tensao_lenta_continua": "Tensão Lenta e Contínua", "set_descendente": "Set descendente", "isolamento": "Isolamento", "serie_composta": "Série Composta", "super_set_multiplo": "Super-Set <PERSON>", "pré_exaustao": "Pré-Exaustão", "serie_gigante": "Série gigante", "super_circuito": "Super-Circuito", "musculacao_intervalada": "Musculação Intervalada", "pliometrico": "Pliométrico", "repeticao_negativa": "Repetição Negativa", "pos_exaustao": "Pós-Exaustão", "exaustao": "Exaustão", "contracao_isometrica": "Contração Isométrica", "continuo": "<PERSON><PERSON><PERSON><PERSON>", "combinado": "Combinado", "alternado_simultaneo": "Alternado + Simultâneo", "2tempos": "2 Tempos", "3tempos": "3 Tempos", "ponto_zero": "Ponto Zero", "execucao_normal": "Execução normal", "busque_pelo_nome_do_aluno": "Busque pelo nome do Aluno", "busque_pelo_nome_da_ficha": "Busque pelo nome da ficha", "nao_encontramos_nenhuma_ficha": "Ops, não encontramos nenhuma ficha!", "ficha_de_treino": "<PERSON>cha de treino", "justificativa": "Justificativa", "escreva_sua_justificativa": "Escreva sua justificativa", "nao_foi_possivel_fazer_checkin_limite_pessoas": "Não foi possível fazer check-in na aula, to<PERSON> as vagas estão preenchidas", "contratos_a_vencer": "Contra<PERSON> a vencer", "porcentagem_alunos": "{}% dos alunos", "a_data_inicio_nao_pode_ser_depois": "A data de início não pode ser depois da data de fim", "sua_academia_nao_permite_ferias_retroativas": "Sua academia não permite o lançamento de férias retroativas", "gostaria_de_habilitar_descanso": "Gostaria de habilitar o descanso entre séries e gerenciar o seu tempo de descanso durante o treino?", "descanso_entre_series": "Descanso entre séries", "voce_pode_habilitar": "Você pode ativar ou desativar essa opção a qualquer momento no menu 'Configurações'", "habilitar": "Habilitar", "sign_here": "Assine aqui", "ficha_sem_atividades": "Sua ficha do dia não possui atividades", "peca_ao_personal_para_add_atividades": "Peça ao seu personal para adicionar atividades à ficha", "aula_cheia": "<PERSON><PERSON><PERSON>", "continuar_e_responder": "Continuar e responder", "sair_sem_responder": "<PERSON>r sem responder", "sair_sem_responder_formulario": "Sair sem responder o formulário?", "certeza_sair_sem_responder_formulario": "Tem certeza que deseja sair sem responder o formulário? Se preferir, você pode falar com seu instrutor para obter mais informações", "passo_x_de_y": "Passo {} de {}", "adicionar_observacao": "Adicionar observação", "editar_observacao": "Editar observa<PERSON>", "limpar_assinatura": "<PERSON><PERSON>", "assine_o_questionario_para_continuar": "Assine o questionário para continuar", "preencher": "<PERSON><PERSON><PERSON>", "formulario_preenchido_com_sucesso": "Formulário preenchido com sucesso", "este_questionario_te_dira": "Este questionário te dirá se é necessário que você procure aconselhamento do seu médico ou de um profissional qualificado antes de se tornar mais ativo fisicamente", "questionario_de_prontidao": "Questionário de prontidão para atividade física", "responder": "<PERSON><PERSON><PERSON><PERSON>", "responder_agora": "Responder agora", "sim": "<PERSON>m", "SIM": "SIM", "NO": "NÃO", "acompanhar": {"titulolocalization": "Acompanhando", "todos": "Todos", "executando_treino": "Executando treino", "nenhum_ aluno": "Nenhum aluno aqui ainda", "adicione_aluno_aqui": "Adicione alunos aqui na sua guia de acompanhamentos e veja mais de perto a evolução deles", "adicionar_alunos": "<PERSON><PERSON><PERSON><PERSON>", "excluir": "Excluir", "nenhum_aluno_treinando": "<PERSON>enhum aluno treinando agora", "inicie_treino": "Inicie o treino de um de seus alunos, para que ele apareça aqui", "adicionar_obs": "Adicionar observação", "treino_finalizado": "Treino finalizado com sucesso!", "deseja_finalizar_treino": "Deseja finalizar o treino desse aluno?", "finalizar_treino": "<PERSON><PERSON><PERSON> treino", "aluno_sem_programa": "Ops! Aluno sem programa de treino.", "aluno_com_treino_execucao": "<PERSON>uno já tem um treino em execução!", "executar_treino": "Executar treino", "aluno_selecionado": "<PERSON><PERSON>", "alunos_selecionados": "al<PERSON>s selecionados", "busque_pelo_nome": "Busque pelo nome do aluno", "nao_encontramos_aluno": "Ops, não encontramos nenhum aluno!", "verifique_digitacao": "Verifique se há erro de digitação e tente novamente. Se precisar, utilize outros termos", "ficha_sem_atividade": "Ops! Ficha sem atividade cadastrada.", "selecionar_ficha": "Selecionar ficha", "aluno_adicionado_sucesso": "<PERSON>uno adicionado com sucesso!", "aluno_excluido_sucesso": "Aluno excluido com sucesso", "deseja_remover_alunos": "Deseja remover todos os alunos?", "sem_ficha": "Programa de treino sem ficha cadastrada"}, "parece_que_todas_as_vagas_estao_preenchidas": "<PERSON><PERSON><PERSON> que todas as vagas já estão preenchidas", "deseja_entrar_na_fila": "Deseja entrar na fila de espera dessa aula?", "voce_foi_inserido_na_fila": "Você foi inserido na fila", "seu_plano_esta_expirando": "Seu plano está próximo\n do vencimento", "seu_plano_ira_expirar_args": "Seu plano {}\nirá expirar dentro de {} dias. Gostaria\nde renovar ele agora?", "renovar_meu_plano": "<PERSON><PERSON> meu plano", "renovacao": "Renovação", "renovar_contrato": "<PERSON><PERSON> contrato", "por_mes": "/ mês", "duracao_meses": {"zero": "Dura<PERSON> de {} meses", "one": "<PERSON><PERSON><PERSON> de {} mês", "many": "Dura<PERSON> de {} meses", "other": "Dura<PERSON> de {} meses"}, "inicio_do_contrato": "Início do contrato", "meses_args": "{} meses", "fim_do_contrato": "Fim do contrato", "personal_data": "<PERSON><PERSON> pessoais", "renovando_contrato": "<PERSON><PERSON><PERSON> contrato", "so_um_momento": "Só um momento enquanto cuidamos de tudo", "plano_renovado_com_sucesso": "Plano renovado com sucesso!", "continue_aproveitando_o_melhor_da_academia": "Continue aproveitando o melhor da sua academia sem se preocupar.", "tem_versao_nova_disponivel": "Tem versão nova disponível", "atualize_seu_app": "Atualize seu app e tenha acesso às novas \nfuncionalidades disponíveis", "atualizar_app": "Atualizar App", "atualizacao_necessaria": "Atualização disponível", "nao": "Não", "proxima_aula_meia_hora": "A sua próxima aula irá começar em meia hora", "permitir_notificacoes": "Permitir <PERSON>", "agora_nao": "<PERSON><PERSON><PERSON> n<PERSON>", "ative_notificacoes_rotina_saudavel": "Ative as notificações e receba todos os lembretes e dicas para avançar ainda mais na sua rotina saudável!", "com_lembretes_longe": "<PERSON><PERSON> le<PERSON>, você vai mais longe", "ative_notificacoes_treinos": "Ative as notificações para ficar sempre em dia com seus treinos.", "fique_ligado": "Fique ligado no seu seu treino!", "ative_notificacoes_por_dentro": "Ative as notificações e fique por dentro de tudo que acontece no seu app!", "quem_interagiu": "Saiba quem interagiu com você!", "ative_notificacoes_aulas": "Ative as notificações para receber lembretes de todas as aulas e não perder o horário!", "aula_nao_perder": "Essa aula não dá pra perder!", "ative_notificacoes_aulas_coletivas": "Ative as notificações para receber lembretes de todas as aulas e não perca a hora das suas aulas coletivas!", "a_turma_vai_sentir": "A turma vai sentir sua falta…", "ative_notificacoes_turbina_treino": "Ative as notificações para receber novidades, lembretes e dicas que turbinam a sua rotina de treino!", "treino_mais_inteligente": "O seu Treino mais inteligente", "ative_notificacoes_wods": "Ative as notificações para receber novidades, lembretes e dicas que turbinam a sua rotina de WODs!", "ative_notificacoes_por_dentro_box": "Ative as notificações e fique por dentro de tudo que acontece no seu box!", "acompanhe_records_box": "Acompanhe os recordes do Box!", "ative_notificacoes_evoluir": "Ative as notificações e receba todos os lembretes e dicas para evoluir ainda mais com a ajuda do seu app!", "ative_notificacoes_compromisso": "Ative as notificações para receber todos os lembretes e não perder nenhum compromisso!", "esse_e_imporante": "Esse é importante…", "ative_notificacoes_melhorar_rotina_saudavel": "<PERSON><PERSON><PERSON>, já ative as notificações para ficar por dentro de tudo no seu app e melhorar sua rotina saudável!", "vai_ficar_para_proxima": "Vai ficar pra próxima?!", "ative_notificacoes_compromissos": "Ative as notificações para receber lembretes dos seus compromissos!", "memoria_falha_app_nao": "A memória falha, seu app não", "ative_notificacoes_plano": "Ative as notificações para receber todos os lembretes e concluir seu o plano com sucesso!", "lembre_se_proxima_refeicao": "Lembre-se da próxima refeição", "ative_notificacoes_alcancar_objetivo": "Ative as notificações para receber lembretes e dicas que te ajudam a alcançar o seu objetivo!", "ajuda_bem_vinda": "Uma ajuda extra é sempre bem-vinda!", "ative_notificacoes_lembretes_dicas": "Ative as notificações e receba todos os lembretes, novidades e dicas para avançar ainda mais na sua rotina!", "voce_vai_mais_longe": "Com app, você vai mais longe", "ative_notificacoes_acontece_app": "Ative as notificações e fique por dentro de tudo o que acontece no seu app!", "saiba_tudo_o_que_esta_rolando": "<PERSON>ba tudo o que tá rolando!", "ative_notificacoes_nao_perder_horario": "Ative as notificações para receber os lembretes de todas as aulas e não perder o horário!", "acompanhe_aulas": "Acom<PERSON><PERSON> as suas aulas", "ative_notificacoes_participar_aulas": "Ative as notificações para saber quais alunos vão participar das suas aulas e muito mais!", "fique_atualizado": "Fique atualizado(a)!", "ative_notificacoes_facilitar_rotina": "Ative as notificações e receba todas as novidades e lembretes para facilitar a sua rotina!", "acompanhe_atualizacoes": "Acompanhe as atualizações", "wow_turbine_app": "Wow! Turbine o seu app também", "ative_notificacoes_novidades_box": "Ative as notificações e fique por dentro de tudo que acontece no seu box!", "acompanhe_recordes_box": "Acompanhe os recordes do Box!", "fique_ligado_wod": "Fique ligado no WOD", "ative_notificacoes_concluir_plano": "Ative as notificações para receber todos os lembretes e concluir o seu plano com sucesso!", "fechar": "<PERSON><PERSON><PERSON>", "escolher_plano": "Escolher plano", "conferir_planos": "Conferir planos", "registrar_refeicao": "Registrar Refeição", "adicionar_mais": "<PERSON><PERSON><PERSON><PERSON> mais", "obter_meu_plano": "Obter meu plano", "reposicoes_args": {"zero": "{} reposiç<PERSON>es", "one": "{} reposição", "other": "{} reposiç<PERSON>es"}, "treino_em_casa": {"nao_possivel_validar": "Não foi possível validar a sua situação com a empresa, Entre em contato com a administração para resolver.", "contato_trancado": "Seu contrato se encontra, trancado. Entre em contato com a administração para resolver", "nao_possivel_autorizar": "Você não possui autorização para acessar os Treinos em casa. Entre em contato com a administração para resolver", "contrato_ferias": "Seu contrato se encontra de férias. Entre em contato com a administração para resolver", "contrato_atestado": "Seu contrato se encontra com atestado. Entre em contato com a administração para resolver"}, "water": "Água", "user_found": "<PERSON><PERSON><PERSON><PERSON> encontrado", "data_maior_ou_igual": "A data de início informada é maior ou igual a data de término. Verifique e tente novamente.", "erro_agendamento": "Você já marcou a quantidade máxima de agendamentos permitidas no intervalo de tempo configurado pelo professor.", "validacao_aluno_agendamento_erro": "Você já marcou a quantidade máxima de agendamentos permitidas no intervalo de tempo configurado pelo professor.", "conta_excluida_inativa": "Esta conta está inativa", "reativar_conta": "Reativar conta", "conta_excuida_termos": "Você solicitou a exclusão definitiva dela e o processo está em andamento.", "excluir_conta": "Excluir conta", "nao_excluir_conta": "Não excluir", "excluir_conta_termos": "Este termo estabelece as condições para a exclusão da sua conta em nossa plataforma. Por favor, leia atentamente os itens abaixo antes de continuar esta ação: \n\n  1. Ao solicitar a exclusão da sua conta, ela será marcada como inativa pelos próximos 30 dias. Durante esse período você poderá reativar os recursos e serviços associados à sua conta; \n\n  2. Após o período de 30 dias, sua conta será excluída permanentemente juntamente com todos os dados e informações associadas a ela. Essa exclusão é irreversível e não será possível recuperá-la após isso; \n\n  3. A exclusão da conta não cancela seu contrato com a academia. Você continua sujeito aos termos e condições do contrato principal e as obrigações financeiras acordadas nele permanecem da mesma forma. Caso você deseje cancelar o contrato com a academia, é necessário entrar em contato diretamente com o departamento responsável.", "nenhum_treino_vencido": "<PERSON>enhum treino vencido", "nenhum_treino_avencer": "<PERSON><PERSON><PERSON> treino a renovar", "nenhum_treino_emdia": "Nenhum treino em dia", "nenhum_treino_vencido_mensagem": "No momento não existe nenhum treino vencido.", "nenhum_treino_avencer_mensagem": "No momento não existe nenhum treino a renovar.", "nenhum_treino_emdia_mensagem": "No momento não existe nenhum treino está em dia.", "termo_polica": "Termos e políticas", "texto_termos_politicas": "Para continuar usando nosso aplicativo você deve concordar com os Termos e Condições e com as Políticas de Privacidade. Ao clicar no botão Aceitar você concorda com todos os termos descritos nos documentos acima.", "codigo_verificacao": "Código de verificação", "consulta_agendada": "Consulta agendada", "sem_programa_treino": "Sem programa de treino", "agenda": "Agenda", "seu_professor_ainda_nao_criou_seu_programa": "seu professor ainda não criou seu programa de treino ou ele está vencido. \nFale com ele para começar a treinar.", "em_casa": "Em casa", "outros": "Outros", "treinos_na_semana": "Treinos na semana", "treine_em_casa": "Treine em casa", "veja_mais_treinos_para_fazer": "<PERSON>eja mais treinos para fazer", "ver_treinos": "<PERSON>er treinos", "unidade": "Unidade", "expandir_calendario": "Expandir calen<PERSON>", "retrair_calendario": "Retrair calend<PERSON>", "aulas_disponiveis": "<PERSON><PERSON>", "filtrar": "Filtrar", "ver_records": "Ver registos", "em_execucao_": "Em curso", "iniciar_timer": "Iniciar <PERSON>", "treinar_acompanhado": "Tre<PERSON>r acompanhado é ainda melhor!", "convide_seus_amigos": "Convite seus amigos para treinar e ganhe benefícios exclusivos", "convidar_agora": "Convidar agora!", "nao_seguir": "<PERSON><PERSON> seguir", "seguir": "<PERSON><PERSON><PERSON>!", "cafe_da_manha": "Café da Manhã", "vem_seguir_sua_dieta": "Vem seguir a sua dieta para ter resultados incríveis!", "historico_fitness": "Histórico Fitness", "indicadores": "Indicadores", "verificar_agendamento": "Verificar agendamento", "verificar_disponibilidade": "Verificar disponibilidade", "composicao_corporal": "Composição corporal", "elevado": "<PERSON><PERSON><PERSON>", "ver_mais_dados": "Ver mais dados", "minha_evolucao": "Minha evolução", "comparativo_antes_depois": "Comparativo de antes e depois", "peso_ideal": "Peso Ideal", "metabolismo_basal": "Metabolismo Basal", "idade_corporal": "<PERSON>de corporal", "a_taxa_metabolica_basal": "A taxa metabólica basal (TMB) é a quantidade de energia necessária para a manutenção das funções vitais do organismo ao longo de 24 horas.", "a_idade_corporal_representa": "A idade corporal representa a sua taxa metabólica basal média comparada à de outras pessoas que têm a mesma idade cronológica que a sua.", "valido_entre": "<PERSON><PERSON><PERSON><PERSON> entre", "criado_por": "<PERSON><PERSON><PERSON> por", "configurar_o_app": "Configurar o app", "encerrar_sessao": "<PERSON><PERSON><PERSON>", "aparencia": "Aparência", "unidades_de_medidas": "Unidades de medidas", "execucao": "Execução", "privacidade": "Privacidade", "peso_corporal": "Peso corporal", "normal": "Normal", "vencimento_da_fatura": "Vencimento da fatura", "creditos_disponivel": "Créditos disponíveis", "parcelas_vencidas": "<PERSON><PERSON><PERSON><PERSON> venc<PERSON>", "parcelas_em_atraso": "Parcelas em atraso", "pagar_todas": "<PERSON><PERSON> todas", "parcela_atual": "<PERSON><PERSON><PERSON> at<PERSON>", "ver_historico_parcel": "<PERSON>er histó<PERSON><PERSON>", "pagar_com_boleto": "Pagar com boleto", "plano_atual": "Plano Anual", "vitio": {"plano_alimentar": "Plano alimentar", "plano_alimentar_info": "Desbloqueie o próximo nível na vida\nsaudável. Tenha o controle da sua dieta!", "proteinas": "<PERSON><PERSON><PERSON><PERSON>", "carboidratos": "Carboidratos", "lipidios": "Lipídios", "controle_de_hidratacao": "Controle de Hidratação", "suporte": "Suporte", "suporte_info": "Precisa de ajuda com algo?", "controlar_doenca": "<PERSON><PERSON>", "ganho_de_massa": "<PERSON><PERSON><PERSON>", "qualidade_de_vida": "Qualidade de\nvida"}, "nivel_muscular_info": {"baixo": "Aumente a massa muscular com dieta proteica e exercícios de hipertrofia.", "normal": "Seu percentual de massa muscular encontra-se em um nível normal", "bom": "Seu percentual de massa muscular encontra-se em um nível normal", "muito_bom": "Seu percentual de massa muscular está ótimo, continue assim"}, "nivel_gordura_corporal": {"bom": "Baixo percentual de gordura: atenção, exceto atletas.", "normal": "Percentual normal, continue assim!", "alto": "Percentual normal, continue assim!", "muito_alto": "Alto percentual de gordura: risco à saúde."}, "residual_info": "Residual corresponde ao percentual dos componentes corporais, excluindo gordura, músculos e ossos", "nivel_imc": {"baixo": "Seu IMC está baixo (18.5), assim como a taxa de gordura corporal", "normal": "Seu IMC está normal (25), assim como a taxa de gordura corporal", "elevado": "Seu IMC excede o limite (30), assim como a taxa de gordura corporal", "muito_elevado": "Seu IMC excede muito o limite (35), assim como a taxa de gordura corporal"}, "texto_peso_ideal": "O seu peso ideal é de {} kg. No entanto, continua sendo considerado saud<PERSON><PERSON> se variar entre {} e {} kg.", "termos_do_contrato": "Termos do contrato", "historico_parcel": "Histórico de parcelas", "ano": "ano", "anos": "anos", "informe_seu_telefone": "Informe o seu telefone", "informe_seu_telefone_info": "Informe o seu telefone usado no cadastro de sua academia para acessar o app", "usuarios_recentes": "Usuários recentes", "fichas_de_treino": "<PERSON><PERSON>s de treino", "meus_wods": "<PERSON><PERSON>", "movimento": "Movimento", "vaga": "vaga", "vagas": "vagas", "criado_por_prof": "<PERSON><PERSON><PERSON> por: Prof. {}", "exercicios_literal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iniciar_treino": "<PERSON><PERSON>ar tre<PERSON>", "primarios": "Primários", "secundarias": "Secundários", "visualizar_por": "Visualizar por", "horario": "<PERSON><PERSON><PERSON><PERSON>", "modalidade": "Modalidade", "concluir_serie": "Concluir séries", "adicionar_minha_imagem": "<PERSON><PERSON><PERSON><PERSON>m", "cadastrar": "Cadastrar", "encerrar_treino": "<PERSON><PERSON><PERSON> treino", "tem_certeza_encerrar_treino": "Tem certeza que gostaria de encerrar seu treino sem concluir todos os exercícios?", "desfazer": "<PERSON><PERSON><PERSON>", "cronometro": "<PERSON><PERSON><PERSON><PERSON>", "emom": "<PERSON><PERSON>", "tabata": "<PERSON>bat<PERSON>", "progressivo": "Progressivo", "regressivo": "Regressivo", "tempo": "Tempo", "nutricionista": "Nutricionista", "escolha": "Escolha", "sair_sem_salvar": "<PERSON><PERSON> sem salvar", "sair_sem_salvar_registro": "Tem certeza que gostaria de sair sem salvar esse registro? ele será perdido", "manter_registro": "Manter Registro", "sem_usuario": "sem usuário", "salvar_imagem": "<PERSON><PERSON> imagem", "com_prof": "com Prof.", "historico_atividades": "Histórico de atividades", "renove_seu_plano": "Renove seu plano para voltar a aproveitar tudo o que sua academia tem a oferecer!", "renove_seu_plano_na_recepcao": "Renove seu plano na recepção para voltar a aproveitar tudo o que sua academia tem a oferecer!", "para_poder_treinar": "Para poder treinar e aproveitar seu app com tudo o que sua academia tem a oferecer, escolha um plano para começar.", "para_poder_treinar_na_recepcao": "Para poder treinar e aproveitar seu app com tudo o que sua academia tem a oferecer, escolha um plano na recepção para começar.", "volte_com_foco_total": "Volte com foco total em conquistar uma vida mais saudável e escolha um novo plano.", "volte_com_foco_total_na_recepcao": "Volte com foco total em conquistar uma vida mais saudável e escolha um novo plano na recepção.", "aceitar": "Aceitar", "aconteceu_algum_problema": "Aconteceu algum problema?", "seu_feedback_nos_ajuda": "Seu feedback nos ajuda a melhorar o app.", "relatar_um_problema": "Relatar um problema", "agitar_o_telefone_para_relatar_um_problema": "Agitar o telefone para relatar problema", "mova_o_switch_para_desabilitar": "Mova o switch para desabilitar", "calorias_gastas": "Calorias \nGastas", "quantidade_de_passos": "Quantidade \nde passos", "meu_peso": "Meu <PERSON>", "consumo_de_agua": "Consumo \nde <PERSON>gua", "seu_programa_esta_sendo_criado": "Seu programa de treino está sendo criado!", "sua_anamnese_foi_enviada": "Sua anamnese foi enviada para o seu professor e em alguns minutos você receberá a sua ficha de treino.", "qual_o_seu_objetivo_atual": "Qual o seu objetivo atual?", "emagrecimento": "Emagre-\ncimento", "emagrecimento_nao_espacado": "Emagrecimento", "objetivos": "Objetivos", "ganho_de_massa": "<PERSON><PERSON><PERSON>", "ganho_de_massa_nao_espacado": "<PERSON><PERSON><PERSON>", "qualidade_de_vida": "Qualidade \nde vida", "qualidade_de_vida_nao_espacado": "Qualidade de vida", "controlar_doenca": "<PERSON><PERSON>", "controlar_doenca_nao_espacado": "<PERSON><PERSON>", "qual_a_sua_altura": "Qual a sua altura?", "quantos_dias_voce_pratica_atividade": "Quantos dias por semana você pratica atividade física?", "qual_sua_idade": "Qual sua idade?", "qual_seu_nivel_treino": "Qual seu nível atual de treino?", "qual_o_seu_peso_atual": "Qual o seu peso atual?", "meus_indicadores": "Meus indicadores", "indicadores_incluidos": "Indicadores incluídos", "indicadores_disponiveis": "Indicadores diponíveis", "explique_o_que_aconteceu": "Explique resumidamente o que aconteceu ou o que não está funcionando.", "dica_tente_relogar_ou_reiniciar": "Dica: Se o problema persistir, tente relogar ou reinstalar o seu aplicativo.", "outras_fichas": "Outras fichas", "mensagem_para_o_professor": "Men<PERSON><PERSON><PERSON> para o professor:", "avalie_seu_treino": "<PERSON><PERSON> seu treino", "frequencia_cardiaca": "Frequencia cardiaca", "calorias_totais": "<PERSON><PERSON><PERSON> to<PERSON>", "series_concluidas": "Séries concluídas", "obs_os_dados_de_calorias": "Obs.: Os dados de quantidade de passos são fornecidos pelo app Saúde do seu dispositivo.", "do_treino_concluido": "do treino concluído", "prepare_se_a_seguir": "Prepare-se, a seguir:", "aplicar_a_todos": "Aplicar a todas", "aplicar_mudancas_para_outras_series": "Aplicar mudança para outras séries?", "intervalo": "Intervalo", "sem_agendamento": "Sem agendamentos", "marque_um_novo_agendamento": "Marque um novo agendamento e ele irá aparcer aqui", "agendamento_cancelado_com_sucesso": "Agendamento cancelado com sucesso", "reagendar": "Reagendar", "agendamento_cancelado": "Agendamento cancelado!", "voce_tem_um_agendamento_para_hoje": "Você tem um agendamento para hoje", "horarios_disponiveis": "<PERSON><PERSON><PERSON><PERSON><PERSON> di<PERSON>oní<PERSON>", "sem_horarios_disponiveis": "Sem hor<PERSON> disponíveis", "selecionar_professor": "Selecionar profissional", "sem_professores_com_disponibilidade": "Sem professores com disponibilidade", "reposicoes": "Reposições", "sem_aulas_disponiveis": "Sem aulas disponíveis", "nao_foram_encotradas_aulas": "Não foram encontradas aulas para o dia e filtros selecionados.", "selecionar_unidade": "Selecionar Unidade", "meus_creditos": "<PERSON><PERSON>", "minhas_reposicoes": "Minhas reposições", "saldo_de_reposicao": "Saldo de reposição", "seu_contrato_e_a_base_de_credito_proevolution": "Para saber como usar os seus créditos, vá até ao balcão da sua unidade e consulte a política de utilização.", "seu_contrato_e_a_base_de_credito": "O seu contrato é baseado em créditos. Utilize esses créditos para fazer check-in em aulas. Quando os seus créditos acabarem, não será possível fazer check-in.", "saldo_proveniente_de_uma_reposicao": "Saldo proveniente de uma remarcação. Utilize esses créditos para realizar check-in em aula de turma. Quando seu saldo a<PERSON>, você ficará impossibilitado de fazer check-in.", "saldo_atual": "<PERSON><PERSON>", "selecionar_contrato": "Selecionar Contrato", "notamos_que_voce_tem_mais_de_um_contrato": "Notamos que você tem mais de um contrato, selecione um para continuar", "modalidades": "Modalidades", "transmissao_online": "<PERSON>", "acessar_aula": "Acessar aula", "local_da_aula": "Local da aula", "minha_aulas": "<PERSON><PERSON>", "meus_tickets": "Meus tickets", "vagas_disponiveis": "<PERSON><PERSON><PERSON> dispon<PERSON>", "historico_de_aulas": "Hist<PERSON><PERSON><PERSON>", "credito": "<PERSON><PERSON><PERSON><PERSON>", "dados_do_cartao": "Dados do cartão", "nome_cartao": "Nome <PERSON>", "escanei_este_codigo_para_pagar": "Escaneie este código para pagar", "pague_e_sera_creditado_na_hora": "Pague e será creditado na hora", "ou_copie_este_codigo": "Ou copie este código para fazer o pagamento", "escolha_pagar_via_pix": "Escolha pagar via Pix pelo seu Internet Banking ou app de pagamentos. Depois, cole o seguinte código:", "dados_do_boleto": "Dados do boleto", "historico_de_peso": "Histórico de peso", "suas_fichas": "<PERSON><PERSON>", "exercicio_sem_imagem": "<PERSON><PERSON><PERSON><PERSON><PERSON> sem imagem", "em_andamento": "Em andamento", "aulas_finalizadas": "<PERSON><PERSON> finaliza<PERSON>", "realize_uma_nova_avaliacao": "Realize uma nova avaliação para manter seus dados e objetivos sempre atualizados.", "dias_que_treino": "Dias que treinou", "mensagem_do_professor": "Mensagem do professor", "ultima_execucao": "Última execução", "gordura_visceral": "Gordura visceral", "ver_historico": "<PERSON><PERSON> his<PERSON>ó<PERSON>", "ocultar": "Ocultar", "aula_experimental": "Aula Experimental", "checkin_realizado": "Check-in realizado", "oque_voce_esta_procurando": "O que você está procurando?", "ops_nao_encontramos_nenhuma_unidade": "Ops, não encontramos nenhuma unidade", "verifique_se_nao_ha_erros": "Verifique se não há erros no termo digitado e tente novamente.", "juros_e_taxas": "Juros e taxas", "total": "Total", "dados_de_pagamento": "Dados de pagamento", "voce_tem_ate": "Você tem até", "copie_o_codigo_abaixo": "Copie o código abaixo para realizar o pagamento em seu banco.", "nao_foi_possivel_gerar_boleto": "Não foi possível gerar o boleto", "copiar_codigo_de_barras": "Copiar c<PERSON><PERSON> de <PERSON>", "copiar_codigo_com_sucesso": "Copiar código com sucesso", "o_boleto_podera_ser_compensado": "O boleto poderá ser compensado em até 72 horas após seu pagamento, excluindo fins de semana e feriados.", "mes_de_duracao": "Mês de duração", "meses_de_duracao": "Meses de duração", "valores": "Valores", "valor_mensal": "<PERSON>or mensal", "recorrencia": "Recorrência", "reposicao_disponiveis": "Reposições disponíveis", "reposicao_disponivel": "Reposição disponível", "voce_faz_um_unico_contrato": "Você faz um único contrato e paga todo mês. O limite do seu cartão não fica bloqueado.", "faca_um_unico_contrato": "Faça um único contrato e pague o valor de forma parcelada. O limite do seu cartão fica bloqueado e é liberado conforme o pagamento das parcelas.", "modalidades_inclusas": "Modalidades inclusas", "credito_do_contrato": "Crédito do contrato", "com_este_contrato_voce_adquire": "Com este contrato você adquire", "nome_do_cartao": "Nome no cartão", "numero_de_parcelas": "Número de parcelas", "data_da_cobranca": "Data da cobrança", "cupom_de_desconto": "Cupom de desconto", "digite_aqui": "Digite aqui", "aplicar_cupom": "Aplicar cupom", "contratar_plano": "Contratar plano", "vigencia_do_contrato": "Vigência do contrato", "ferias_agendada": "<PERSON><PERSON><PERSON><PERSON>", "de_a": "de {} a {}", "detalhes_do_contrato": "Detalhes do contrato", "dias_permitidos": "Dias permitidos", "trancamento_agendado": "Trancamento agendado", "valor_nao_informado": "Valor não informado", "novo_contrato_disponivel": "Novo contrato disponível", "o_seu_contrato_esta_disponivel": "O seu contrato código {} está disponível e precisa da sua assinatura. Assine agora mesmo e tenha acesso a todos os recursos do app", "assinar_contrato": "Assinar contrato", "sair_do_app": "<PERSON><PERSON> do <PERSON>", "ao_clicar_em_sair_voce_nao_estara_mais_logado": "Ao clicar em sair você não estará mais logado no app e os dados não salvos serão perdidos.", "ops_ainda_nenhum_contrato": "Ops, ainda não há nenhum contrato por aqui", "voce_ainda_nao_firmou_nenhum_contrato": "Você ainda não firmou nenhum contrato com a sua academia.", "voce_ainda_nao_firmou_nenhum_contrato_vamos_fazer_agora": "Você ainda não firmou nenhum contrato com a sua academia. Vamos fazer um agora?", "retornar_das_ferias": "Retornar das férias", "contrato_trancado": "Contrato trancado", "retornar_do_trancamento": "Retornar do trancamento", "trancamento_vencido": "Trancamento vencido", "o_seu_contrato_esta_com_o_trancamento_vencido": "O seu contrato está com o trancamento vencido, favor procurar a recepção!", "o_seu_boleto_esta_vencido": "O seu boleto está vencido", "fale_com_o_seu_consultor": "Fale com o seu consultor e solicite um novo boleto.", "ops_boleto_nao_gerado": "Ops! Boleto não gerado", "fale_com_o_seu_consultor_solicite_geracao_boleto": "Fale com o seu consultor e solicite a geração do seu boleto.", "o_seu_plano_nao_tem_um_contrato": "O seu plano não tem um contrato vinculado.", "termo_de_exclusao_de_conta": "Termo de exclusão de conta", "alterar_foto_no_cadastro": "Alterar foto no cadastro", "voce_deseja_usar_essa_foto_no_seu_perfil": "Você deseja usar essa foto no seu perfil de usuário? Observação: ela será alterada no perfil usado via computador.", "reportar_esse_comentario": "Reportar esse comentário da postagem como ofensivo ou spam?", "metricas_de_treino": "Métricas de treino", "intensidade_media": "Intensidade média", "seu_imc_excede_o_limite": "Seu IMC excede o limite (25,0), assim como a taxa de gordura corporal", "niveis": "Níveis", "nivel": "Nível", "ultimas_atividades": "Últimas atividades", "veja_seu_resumo_semana": "Veja seu resumo semanal", "veja_suas_conquistas": "Veja suas conquistas e veja sua evolução", "veja_seus_treinos_salvos": "<PERSON><PERSON>a seus treinos salvos", "todos_os_treinos": "Todos os treinos", "veja_todos_os_treinos_disponiveis": "<PERSON>eja todos os treinos disponíveis", "nenhum_treino_encontrado": "Nenhum treino encontrado!", "desculpe_nenhum_treino_encontrado": "<PERSON><PERSON><PERSON><PERSON>, nenhum treino encontrado com esse nome. Por favor, tente novamente", "o_telefone_nao_foi_encontrado": "O telefone não foi encontrado", "o_numero_de_telefone_nao_foi_encontrado": "O número de telefone {} não foi encontrado em nossos registros. Verifique se digitou corretamente.", "corrigir_o_numero_de_telefone": "Corrigir o número de telefone", "fazer_login_com_usuario": "Fazer login com usuário", "seu_treino_esta_quase_pronto": "Seu treino já está quase pronto!", "ative_as_notificacaos_para_ser_avisado": "Ative as notificações para ser avisado quando o seu treino estiver pronto.", "essas_informacoes_sao_importantes": "Essas informações são importantes para seu professor definir o melhor planejamento fitness para você.", "obter_meu_treino": "Obter meu treino", "sua_evolucao_comeca_aqui": "Sua evolução começa aqui!", "gordura_corporal": "Gordura corporal", "massa_ossea": "Massa óssea", "registrar_evolucao": "Registrar evolução", "vincular_a_avaliacao_fisica": "Vincular a avaliação física", "vincular_a_qual_avaliacao_fisica": "Vincular a qual avaliação?", "fotos_ja_vinculadas": "Fotos já vinculadas", "registrar_com_a_camera": "Registrar com a câmera", "utilizar_fotos_da_galeria": "Utilizar fotos da galeria", "selecionar_o_angulo_da_proxima_imagem": "Selecione o ângulo da próxima imagem", "frente": "<PERSON><PERSON>", "lado_direito": "<PERSON><PERSON>", "lado_esquerdo": "<PERSON><PERSON>", "costas": "<PERSON><PERSON>", "concluir_incompleta": "Concluir incompleta", "selecionar_fotos": "Selecionar fotos", "selecionar_2_fotos_para_comparar": "Selecione 2 fotos para comparar", "falha_ao_salvar_foto": "Falha ao salvar foto", "infelizmente_nao_foi_possivel_fazer_essa_operacao": "Infelizmente não foi possível fazer essa operação", "pular_etapa": "pular_etapa", "deseja_pular_o_registro_da_foto_dessa_etapa": "Deseja pular o registro da foto dessa etapa?", "configuracao_de_camera": "Configurações da câmera", "mascara_de_silhueta": "Máscara de Silhueta", "temporizador": "Temporizador", "salvar_configuracao": "Salvar configuraçõ<PERSON>", "tudo_pronto_para_salvar_a_sua_evolucao": "Tudo pronto para salvar a sua evolução", "registre_sua_evolucao": "Registre sua evolução!", "registre_imagens_de_sua_evolucao": "Registre imagens de sua evolução e acompanhe o seu desenvolvimento.", "a_balanca_nao_e_o_unico_jeito": "A balança não é o único jeito de acompanhar sua evolução!", "com_comparativo_voce_pode_acompanhar_antes": "Com o comparativo, você pode acompanhar o antes e o depois para perceber as mudanças que não aparecem na balança.", "capture_4_fotos_do_seu_corpo": "Capture 4 fotos do seu corpo de forma simples e rápida", "utilize_nossa_silhueta_guia": "Utilize nossa silhueta guia para te auxiliar na captura de imagens, adaptando o tamanho de ombros e cintura para facilitar o enquadramento!", "dicas_para_ajudar_no_comparitivo": "Dicas para ajudar no comparativo:", "tire_a_foto_em_local_bem_iluminado": "Tire a foto em local bem iluminado", "de_preferencia_para_fotos": "De preferência para fotos em fundos brancos ou com pouca informação", "utilize_as_mesmas_roupas_ou_similares": "Utilize as mesmas roupas ou similares", "selecionar_o_angulo": "<PERSON><PERSON><PERSON><PERSON>", "treinos_pendetes_para_renovacao": "Treinos pendentes para renovação", "renove_o_treino_dos_seus_alunos": "Renove o treino dos seus alunos.", "no_momento_nao_existe_nenhum_aluno_ativo": "No momento não existe nenhum aluno ativo", "veja_seus_records": "<PERSON><PERSON><PERSON> seus records", "sem_ranking_por_enquanto": "Sem ranking por enquanto!", "ainda_nao_temos_resultados_do_wod": "Ainda não temos resultados do WOD aqui. Seja o precursor e mostre seu progresso para motivar a comunidade!", "veja_seu_records": "<PERSON><PERSON>a seu records", "salvar_resultado": "<PERSON><PERSON>", "seja_o_primeiro_a_registrar": "<PERSON><PERSON> o primeiro a registrar!", "poste_seu_resultado_agora_mesmo": "Poste seu resultado agora mesmo e seja o primeiro do ranking! Divulge e compartilhe com amigos nas redes!", "deixe_seus_prs_sempre_atualizados": "Deixe seus PRs sempre atualizados para poder realizar cálculos baseados neles, além de melhorar a sua pontuação nos rankings", "timer_concluido": "Timer concluído!", "contagem_antes_de_iniciar_o_time": "Contagem antes de iniciar o timer", "sons_do_timer": "Sons do Timer", "habilitar_efeitos_sonoros": "Habilitar efeitos sonoros", "excluir_pr": "Excluir PR", "tem_certeza_que_gostaria_de_excluir_pr": "Tem certeza que gostaria de excluir seu Personal Record?", "manter_pr": "Manter PR", "editar_pr": "Editar <PERSON>", "excluir_personal_record": "Excluir Personal Record", "pr_excluido_com_sucesso": "PR excluído com sucesso", "nome_do_wod": "Nome do WOD*", "meu_wod": "Meu <PERSON>", "imagem": "Imagem", "faca_upload_de_uma_imagem": "Faça upload de uma imagem", "para_melhor_visualização_envie_imagens_quadrada": "Para melhor visualização envie imagens quadradas", "data_de_inicio": "Data de início", "salvar_wod": "<PERSON><PERSON>", "descricao_do_wod": "Descrição do Wod*", "aparelho": "<PERSON><PERSON><PERSON><PERSON>", "sem_aparelho": "<PERSON><PERSON>", "esta_aguardando_voce": "Está aguardando você", "selecionar_metodo_execucao": "Selecionar método execução", "adicionar_nome_na_atividade": "Adicionar nome na atividade", "serie_removida_com_sucesso": "Série removida com sucesso!", "sem_permissao": "<PERSON><PERSON> permis<PERSON>", "ops_voce_nao_tem_permissao": "Ops! Você não tem permissão para realizar essa operação.", "nova_ficha_de_treino": "Nova ficha de treino", "novo_programa_de_treino": "Novo programa de treino", "editar_programa": "Editar programa", "ultima_avaliacao_fisica": "Última avaliação física", "resistencia_muscular": "Resistência muscular", "selecionar_periodo": "Selecionar período", "temos_um_metodo_muito_facil": "Temos um método muito fácil para que você possa trocar o seu cartão cadastrado e regularizar o seu pacote ou assinatura Vitio:\n\nAcesse o link", "central_de_ajuda": "Central de ajuda", "entre_em_contato_conosco": "Entre em contato conosco", "inativo": "Inativo", "oberservacao_excluida_com_sucesso": "Observação excluída com sucesso!", "prepare_se_para_comecar": "Prepare-se para começar", "para_poder_aproveitar_tudo_o_que_sua_academia": "Para poder aproveitar tudo o que sua academia tem a oferecer, antes de tudo você precisa escolher um plano para começar.", "va_na_recepcao_de_sua_academia": "Vá na receção de sua academia e escolha o melhor plano para você.", "acessar_com_outro_usuario": "Aceder com outro utilizador", "contrato_do_plano": "Contrato do plano", "nao_ha_horarios_disponiveis": "Não há horários disponíveis, tente noutra data.", "consulta_concluida": "Consulta concluída", "como_foi_sua_consulta_com": "Como foi a sua consulta com:", "o_nutri_ira_te_responder_assim_que_possivel": "O nutricionista irá responder-te assim que possível, em até 1 dia útil", "baixando_o_arquivo": "A baixar o ficheiro: ", "o_codigo_de_5_digitos_foi_enviado": "O código de 5 dígitos foi enviado por e-mail para o e-mail informado", "conectando": "A conectar..", "editar_email": "Editar e-mail", "editar_cpf": "Editar NIF", "informe_o_seu_email_utilizado_no_vitio": "Informe o seu e-mail utilizado na compra do Vitio", "historico_de_consulta": "Histórico de consultas", "sem_nome": "Se<PERSON> No<PERSON>", "essa_semana": "<PERSON><PERSON> semana", "mes_atual": "<PERSON><PERSON><PERSON>", "mes_anterior": "M<PERSON>s anterior", "nenhuma_consulta_por_aqui": "Nenhuma consulta por aqui ainda.", "que_tal_garantir_um_horario_agora": "Que tal garantir um horário agora?", "agendar_consulta": "Agendar consulta", "ja_tenho_um_plano": "<PERSON><PERSON> tenho plano", "criar_conta": "C<PERSON><PERSON> conta", "garanta_sua_consulta_agora": "Garanta a sua consulta agora!", "escolha_um_dos_melhores_nutricionistas": "Escolha um dos melhores nutricionistas do país para te levar até a sua melhor\nversão.", "garantir_agora": "Garan<PERSON><PERSON> agora", "agende_sua_primeira_consulta": "Agende a sua primeira consulta!", "escolha_o_profissional_que_mais_combina_com_voce": "Escolha o profissional que mais combina consigo.", "seu_plano_alimentar_ja_vem": "O seu plano alimentar já vem!", "o_plano_alimentar_esta_em_producao": "O plano alimentar está em produção. Até 4 dias úteis ele estará disponível para você.", "nao_deixe_esse_novo_capitulo_da_sua_jornada": "Não deixe este novo capítulo da sua jornada fitness acabar aqui", "assine_o_vitio_agora": "Assine o Vitio agora e garanta mais 4 consultas com o seu nutricionista, planos alimentares e muito mais!", "adquira_seu_pacote": "Adquira o seu Pacote de Nutrição agora", "musculo": "<PERSON><PERSON><PERSON><PERSON>", "medida": "Medida", "veja_uma_analise_detalhada": "Veja uma análise detalhada sobre cada aspeto do seu corpo", "visualizar_avaliacao": "Visualizar aval<PERSON>ção", "voce_ainda_tem_1_moeda": "Você ainda tem 1 moeda não acumulativa para usar em consultas este mês", "voce_nao_possui_mais_moedas": "Você não possui mais moedas Vitio. Confira abaixo quando chegarão mais!", "novas_moedas_em": "Novas moedas em:", "aguarde_suas_moedas": "Aguarde suas moedas", "consultas_disponiveis": "Consultas disponíveis", "voce_ainda_tem_2_moedas": "Você ainda tem 2 moedas não acumulativas para usar em consultas este mês", "adquira_seu_pacote_de_nutrica": "Adquira o seu Pacote de Nutrição", "aumentar_saldo": "Aumentar saldo", "mais_receitas": "<PERSON><PERSON>", "descubra_receitas_incriveis": "Descubra receitas incríveis", "descubra_agora": "Descubra agora", "conheca_quem_vai_mudar_a_sua_vida": "Conheça quem vai mudar a sua vida:", "os_melhores_nutricionistas_do_pais": "Os melhores nutricionistas do país dar-lhe-ão o corpo e a saúde que você sempre quis", "aqui_no_vitio_voce_tera": "Aqui no Vitio você terá:", "os_7_passos_para_alcancar_sua_melhor_versao": "Os 7 passos para alcançar a sua melhor versão:", "perguntas_e_respostas": "Perguntas e Respostas:", "consultas_online": "Consultas \nonline", "plano_alimentar": "Plano \nalimentar", "evolucao": "Evolução", "tudo_o_que_voce_presica_esta_aqui": "Tudo o que você\n    precisa está aqui:", "so_treinar_nao_da": "<PERSON><PERSON> treinar não dá.", "economize": "Economize", "chat_com_nutricionista": "Chat com nutricionista", "a_nutricao_te_faz_avancar": "A nutrição te faz avançar.", "seu_nutricionista_analisa_seu_caso": "Seu nutricionista analisa seu caso e traça a jornada ideal para alcançar seu objetivo com qualidade e rapidez.", "tenha_resultados_duradouros": "Tenha resultados duradouros com a nutrição acessível e inteligente!", "tenho_plano_de_saude": "Tenho plano de saúde. O Vitio ainda é para mim?", "o_vitio_e_uma_solucao_inovadora": "O Vitio é uma solução inovadora para levar o apoio nutricional da mais alta qualidade, conectado com a rotina do paciente e ainda por um investimento muito menor do que qualquer outro disponível hoje.", "como_faco_a_consulta": "Como faço a consulta?", "e_so_escolher_o_nutricionista": "É só escolher o nutricionista com o perfil que se encaixa no seu caso, agendar a data e hora da consulta, estar no app no horário marcado para entrar na vídeo chamada e participar da consulta. Tudo dentro do seu aplicativo.", "a_cobranca_e_mensal": "A cobrança é mensal?", "e_feita_a_assinatura_do_pacote": "É feita a assinatura do pacote com valor cheio para garantir o apoio anual. Porém, é dividido em mensalidades para facilitar a contratação pelo paciente, sem ocupar o limite do cartão.", "por_que_o_vitio_e_melhor": "Por que o Vitio é melhor do que consulta presencial?", "porque_tem_toda_a_facilidade": "Porque tem toda a facilidade para receber acesso a serviços de saúde de qualidade sem precisar ter os custos e dores de cabeça do deslocamento, cadastros e locais fixos. Você tem acesso à sua nutri e aos outros serviços Vitio a qualquer hora e em qualquer lugar.", "qual_o_seu_objetivo_com_o_acompanhamento": "Qual o seu objetivo com o acompanhamento nutricional?", "proxima_refeicao": "Próxima refeição", "ate_amanha": "Até amanhã!", "voce_completou_a_dieta_do_dia": "Você completou a dieta do dia. Venha com foco total amanhã!", "sem_titulo": "<PERSON><PERSON>", "sem_descricao": "Sem Descrição", "ops_ocorreu_um_erro": "Ops! Ocorreu um erro", "tente_novamente_mais_tarde": "Tente novamente mais tarde.", "consumido": "<PERSON><PERSON><PERSON><PERSON>", "nao_segui_a_refeicao": "Não segui a refeição", "desempenho_atual": "<PERSON><PERSON><PERSON><PERSON> atual", "otimo": "<PERSON><PERSON><PERSON>", "95%_a_100%": "95% a 100%", "85%_a_94_9%": "85% a 94.9%", "75%_a_84_9%": "75% a 84.9%", "60%_a_74_9%": "60% a 74.9%", "40%_a_59_9%": "40% a 59.9%", "20%_a_39_9%": "20% a 39.9%", "0%_a_19_9%": "0% a 19.9%", "do_plano_seguido": "do plano seguido", "muito_bom": "<PERSON><PERSON> bom", "bom": "Bo<PERSON>", "regular": "Regular", "meu_desempenho": "<PERSON><PERSON>", "opcao": "Opção", "desempenho_plano": "Desempenho Plano", "proximo_horario_disponivel": "Próximo hor<PERSON> disponível", "perfil_do_nutricionista": "Perfil do nutricionista", "sem_bio": "Sem Bio", "filtrar_por": "Filtrar por", "pendencias_encontradas": "Pendências encontradas", "existem_parcelas_vencidas_no_seu_contrato": "Existem parcelas vencidas no seu contrato e não é possível executar o treino. \nFale com o seu consultor.", "seu_programa_de_treino_chegou_ao_fim": "Seu programa de treino chegou ao fim. Fale com o seu professor ou agende uma renovação.", "seu_treino_chegou_ao_fim": "Seu treino chegou ao fim", "todos_os_treinos_ja_foram_realizados": "Todos os treinos já foram realizados. Fale com o seu professor ou agende uma renovação.", "o_seu_professor_ainda_nao_gerou_seu_programa": "O seu professor ainda não gerou seu programa de treino. Fale com seu professor para começar a treinar.", "nao_foi_possivel_validar_programa_de_treino": "Não foi possível validar seu programa de treino. Tente novamente mais tarde.", "ops_nao_foi_possivel_consultar": "Ops! Não foi possível consultar", "analise_segmentar": "<PERSON><PERSON><PERSON><PERSON> segmentar", "peso_real": "Peso real", "aguarde_x_segundo": "Aguarde {} segundo", "aguarde_x_segundos": "Aguarde {} segundos", "desfazer_edicao_atual": "Desfazer edição atual", "regiao_muscular": "Região muscular", "treinos_disponiveis": "<PERSON><PERSON><PERSON><PERSON> disponí<PERSON>", "nao_compartilhar": "Não compartilhar", "avaliacao_enviada": "Avaliação enviada", "voce_gostaria_de_compartilhar": "Você gostaria de compartilhar seu resultado no feed?", "confirmar_presenca?": "Confirmar presen<PERSON>?", "voce_sera_incluido_na_aula": "Você será incluído na aula {} às {} no dia {}.", "cheia": "<PERSON><PERSON><PERSON>", "busque_pelo_nome": "Busque pelo nome", "fila_de_espera": "Fila de espera", "aluno_ja_se_encontra_na_fila_de_espera": "<PERSON><PERSON> já se encontra na fila de espera de uma aula!", "novo_personal_record": "Novo Personal Record", "agendamentos_disponiveis": "Agendamentos disponíveis", "veja_seu_progresso_e_melhore": "Veja seu progresso e melhore a performance de seus treinos.", "ideal_para_montar_uma_nova_ficha": "Ideal para montar uma nova ficha com novos objetivos.", "continue_a_execucao_do_treino": "Continue a execução do treino já prescrito anteriormente.", "marque_um_horario_para_conversar_com_esse_profissional": "Marque um horário para conversar com esse profissional.", "reavalie_seu_treino_e_realize": "Reavalie seu treino e realize correções para sua continuação.", "prescricao_de_treino": "Prescrição de treino", "renovar_treino": "<PERSON><PERSON>", "contato_interpessoal": "Contato interpessoal", "revisao": "<PERSON><PERSON><PERSON>", "agendamentos": "Agendamentos", "selecione_um_professor": "Selecione um profissional", "as": "às", "sem_grupos_musculares": "Sem grupos musculares", "exercicio_em_serie": "Exercício em série", "enviar_avaliacao": "Enviar avaliação", "exercicio_sem_serie": "<PERSON><PERSON><PERSON><PERSON><PERSON> sem série", "cancelar_agendamento": "Cancelar agendamento", "nao_ha_avaliacao_fisica_disponivel": "Não há avaliação física disponível. Tente novamente mais tarde", "falta_x_dias_para_realizar_sua_avaliacao": "Faltam {} dias para realizarmos sua avaliação. Compareça ao local indicado e siga as orientações do avaliador.", "falta_1_dia_para_realizarmos_sua_avaliacao": "Falta 1 dia para realizarmos sua avaliação. Compareça ao local indicado e siga as orientações do avaliador.", "a_sua_consulta_e_hoje": "A sua consulta é hoje. Compareça ao local indicado e siga as orientações do avaliador.", "o_percentual_magro": "O percentual magro compreende os demais tecidos isentos de gordura", "o_percentual_gordo": "O percentual gordo refere-se ao percentual total da gordura corporal", "gordo": "<PERSON><PERSON>", "magro": "<PERSON><PERSON>", "comparar": "Comparar", "nivel_gordura_visceral_baixo": "Seu nível de gordura visceral está baixo, continue assim", "nivel_gordura_visceral_normal": "Seu nível de gordura visceral em um nível normal", "nivel_gordura_visceral_elevado": "Seu nível de gordura visceral está elevado. Tente manter uma rotina saudável pode ajudar com isso", "mensagem_aluno": "Mensagem ao aluno", "mes_duracao": "Mês de duração", "meses_duracao": "Meses de duração", "sobre_recorrencia": "Você faz um único contrato e paga todo mês. O limite do seu cartão não fica bloqueado.", "sobre_cobranca_normal": "Faça um único contrato e pague o valor de forma parcelada. O limite do seu cartão fica bloqueado e é liberado conforme o pagamento das parcelas.", "modalidades_inclusa": "Modalidades inclusas", "credito_contrato": "Crédito do contrato", "sobre_credito": "Com este contrato você adquire", "subtitulo_credito": "Verifique com a sua academia as condições de uso.", "cpf_nao_cadastrado": "CPF não cadastrado!", "subtitulo_cpf_cadastrado": "Favor, vá até à recepção atualizar os seus dados para continuar realizar a compra e aproveitar o seu app com tudo que a academia tem a oferecer.", "comprar_plano": "Comprar plano", "nenhum_aparelho": "<PERSON><PERSON><PERSON> a<PERSON> encontrado", "aparelho_cadastrado": "<PERSON><PERSON><PERSON> a<PERSON> cadastrado", "sem_atividade": "Sem atividades!", "nenhuma_atividade": "Nenhuma atividade encontrada", "atividades_cadastradas": "Nenhuma atividade cadastrada", "ver_ranking": "Ver ranking completo", "situacao_aluno": {"ativo": "Ativo", "inativo": "Inativo", "visitante": "Visitante", "trancado": "Trancado", "cancelado": "Cancelado", "desistente": "<PERSON><PERSON><PERSON>", "vencido": "V<PERSON>cid<PERSON>", "avencer": "<PERSON> Vencer"}, "prescricao_treino": "Prescrição de treino", "texto_avaliacao": "Veja seu progresso e melhore a performance de seus treinos.", "text_prescicao": "Ideal para montar uma nova ficha com novos objetivos.", "texto_renovar_treino": "Continue a execução do treino já prescrito anteriormente.", "texto_contato_interpessoal": "Marque um horário para conversar com esse profissional.", "texto_revisao": "Reavalie seu treino e realize correções para sua continuação.", "marque_novo_agendamento": "Marque um novo agendamento e ele irá aparecer aqui", "deseja_cancelar": "Deseja cancelar o seu agendamento?", "confirmar_agendamento": "Confirmar cancel<PERSON>o", "nao_cancelar": "<PERSON><PERSON> cancelar", "aguardando_voce": "Está aguardando você", "tem_um_agendamento": "Você tem um agendamento para hoje", "selecione_professor": "Selecione um profissional", "sem_horario_disponivel": "Sem hor<PERSON> disponíveis", "sem_professor_disponivel": "Sem profissionais com disponibilidade", "buscar_por_nome": "Buscar por nome", "vagas_": {"one": "{} Vaga", "two": "{} <PERSON><PERSON><PERSON>"}, "data_horario": "{diaTratado}/{mesTratado} às {horas}", "acao_desfeita": "Essa ação não poderá ser desfeita", "upload_image": "Faça upload de uma imagem", "melhor_visualizacao": "Para melhor visualização, envie imagens quadradas", "descricao_wod": "Descrição do Wod*", "selecione_periodo": "Selecionar período", "periodo_consulta": {"semana": "Se<PERSON>", "mes_atual": "<PERSON><PERSON><PERSON>", "mes_anterior": "M<PERSON>s anterior"}, "sem_historico": "<PERSON><PERSON> his<PERSON>ó<PERSON>", "neste_periodo_de": "Neste período, de {} a {}, não foram encontrados registros em sua linha do tempo.", "eventos_historico": {"aula_desmarcada": "<PERSON><PERSON> desmarcada: {}", "fez_aula": "<PERSON>z aula: {}", "registrou_wod": "Registrou Wod: {}", "notificacao": "Notificação: {}", "treinou": "Executou a ficha de treino: {}", "mudou_de_nivel": "<PERSON><PERSON><PERSON>í<PERSON>: {}", "montou_treino": "Novo Treino: {}", "realizou_avaliacao": "{}", "acabou_treino": "Acabou Treino: {}", "renovou_treino": "Renovou Treino: {}", "alteracao_agendamento_servicos": "Agendamento alterado: {}", "agendamento": "Agendamento: {}"}, "obs_excluido": "Observação excluída com sucesso!", "nao_add": "<PERSON>ão adicionar", "niveis_corporal": {"baixo": "Baixo", "normal": "Normal", "alto": "Alto", "sobrepeso": "Sobrepeso", "obesidade": "Obesidade", "elevado": "<PERSON><PERSON><PERSON>"}, "texto_residual": "Residual corresponde ao percentual dos componentes corporais, excluindo gordura, músculos e ossos", "muscular_baixo": "Aumente a massa muscular com dieta proteica e exercícios de hipertrofia.", "muscular_normal": "Seu percentual de massa muscular encontra-se em um nível normal", "muscular_bom": "Seu percentual de massa muscular está ótimo, continue assim", "massa_magra_baixo": "Sua massa magra está abaixo do ideal. Recomenda-se um programa de exercícios de resistência e uma dieta rica em proteínas para promover o crescimento muscular e a saúde dos ossos.", "massa_magra_normal": "Sua massa magra está dentro da faixa considerada saudável para sua idade e sexo.", "massa_magra_alto": "Sua massa magra está acima da média, o que é um sinal de boa saúde e força física. Continue com seu programa de exercícios e dieta equilibrada para manter sua condição física ideal.", "gordura_bom": "Baixo percentual de gordura: atenção, exceto para atletas.", "gordura_normal": "Percentual normal, continue assim!", "gordura_alto": "Alto percentual de gordura: risco à saúde.", "ossea_baixo": "Massa óssea baixa: risco à saúde. Consuma cálcio.", "ossea_normal": "Sua massa óssea está em um nível normal", "ossea_alto": "Massa óssea alta: um sinal de robustez e saúde. Continue com uma dieta equilibrada e exercícios físicos para manter sua estrutura óssea forte e resistente ao longo da vida", "nivelAgua_baixo": "Seu nível de água no corpo está muito abaixo dos níveis saudáveis, tenha uma rotina de hidrata<PERSON> me<PERSON>hor.", "nivelAgua_normal": "Seu nível de água no corpo encontra-se em um nível normal", "nivelAgua_bom": "Seu nível de água no corpo está em níveis muito bons, continue assim!", "imc_baixo": "Seu IMC está baixo (18.5), assim como a taxa de gordura corporal", "imc_normal": "Seu IMC está normal (25), assim como a taxa de gordura corporal", "imc_elevado": "Seu IMC excede o limite (30), assim como a taxa de gordura corporal", "imc_muitoElevado": "Seu IMC excede muito o limite (35), assim como a taxa de gordura corporal", "visceral_baixo": "Seu nível de gordura visceral está baixo, continue assim", "visceral_normal": "Seu nível de gordura visceral está em um nível normal", "visceral_alto": "Seu nível de gordura visceral está elevado. Tente manter uma rotina saudável para melhorar isso", "ultimaAvaliacao": "Última avaliação física", "nenhuma_aula_encontrada": "Não foram encontradas aulas para o dia e filtros selecionados.", "local_aula": "Local da aula", "sala_principal": "Sala principal", "vagas_turma": "<PERSON><PERSON><PERSON>", "minhas_aulas": "<PERSON><PERSON>", "fila_espera": "Fila de espera", "editar_aluno": "<PERSON><PERSON>", "clique_veja_aulas": "Clique e veja a lista de aulas", "termino_programa": "<PERSON><PERSON><PERSON><PERSON>", "aluno_sem_permissao": "<PERSON>s alunos não têm permissão para realizar esta operação. Por favor, dirijam-se à recepção para cancelar sua avaliação física.", "wod_nao_cadastrado": "O Wod do dia ainda não foi cadastrado", "selecionar_metodo": "Selecionar método de execução", "metodo_execucao": "Método de execução", "aplicar_padrao": "Aplicar padrão às outras séries", "segundos": "segundos", "atividades_selecionadas": "Atividades selecionadas", "deseja_limpar_selecao": "Deseja limpar a sua seleção?", "todos_itens_selecionados": "Todos os itens selecionados serão excluídos, tem certeza que deseja fazer isso?", "programa_adicionado": "Programa adicionado!", "add_programa_atual": "Você gostaria de adicionar o programa atual para outros alunos?", "cade_programa_treino": "Ops! Cadê o programa de treino?", "aluno_sem_programa": "O aluno não possui um programa de treino para adicionar a ficha selecionada. Cadastre um programa antes para continuar.", "aluno_sendo_acompanhado": "O aluno já está sendo acompanhado.", "prescricao_ia": "Prescrição IA", "prescricao_por_ia": "Prescrição por IA", "recurso_ia": "O recurso de Prescrição de Treinos por IA não está habilitado na sua unidade. Acesse o App Pacto e habilite o recurso.", "serie_removido_sucesso": "Série removida com sucesso!", "diaria_gympass": "<PERSON><PERSON><PERSON>", "desafio": "<PERSON><PERSON><PERSON>", "domingo": "D", "segunda": "S", "terca": "T", "quarta": "Q", "quinta": "Q", "sexta": "S", "sabado": "S", "agenda_avaliacao_permissao": "Sem permissão para agendar uma avaliação física. Por favor, dirija-se à recepção para obter mais informações.", "sem_permissao_avaliacao": "Você não possui permissão para visualizar sua avaliação física.", "detalhes_do_plano": "Detalhes do plano", "sem_email_cadastrado": "Sem e-mail cadastrado", "data_de_cobranca": "Data da cobrança", "para_poder_aproveitar_tudo": "Para aproveitar tudo o que sua academia tem a oferecer, escolha um plano antes de tudo.", "resumo_de_compra": "Resumo de compra", "teste": "TESTE", "antes_de_finalizar_o_pagamento": "Obs.: Antes de finalizar o pagamento, é necessário visualizar os termos e condições do contrato.", "li_os_termos_e_aceito": "Li os termos e aceito", "mais_de_um_cadastro_encontrado": "Mais de um cadastro encontrado com este e-mail/CPF", "dados_indisponiveis": "Dados indisponíveis", "data_futura_pr": "Não é possível registrar uma PR com data futura. Corrija a data e tente novamente.", "parcela": "<PERSON><PERSON><PERSON>", "parcelas": "<PERSON><PERSON><PERSON><PERSON>", "Mes": "<PERSON><PERSON><PERSON>", "nenhum_cartao": "<PERSON><PERSON><PERSON>", "trancar_plano": "Trancar plano", "produto": "Produ<PERSON>", "motivo": "Motivo", "periodo_de_ferias": "Per<PERSON><PERSON>", "cobranca": "Cobrança", "previsao_da_foto": "<PERSON><PERSON><PERSON> da foto", "habilita_uma_foto_previa": "Habilita uma foto prévia do exercício na lista em execução", "concluir_a_execucao": "Concluir a execução de treino automaticamente após ficar inativo por:", "suporte": "Suporte", "agite_o_telefone": "Agite o telefone para reportar", "permitir_que_ao_agitar": "Permitir que ao agitar o telefone em qualquer tela, abra o suporte do aplicativo.", "portugues": "Português", "espanhol": "Espanhol", "ingles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "falha_ao_solicitar_as_ferias": "Falha ao solicitar as férias!", "renovar_plano": "<PERSON>var plano", "ola": "O<PERSON><PERSON>", "programa_de_treino_chegou_fim!": "Programa de treino chegou ao fim!", "text_fale_professor_agende_renovacao_treino": "Fale com seu professor ou agende a renovação do seu treino.", "text_fale_professor_solicite_renovacao_treino": "Fale com seu professor e solicite a renovação do treino.", "programa_de_treino": "Programa de treino", "valido_entre_ate": "<PERSON><PERSON><PERSON><PERSON> entre {} até {}", "ultimos_treinos": "Últimos tre<PERSON>s", "sem_treinos_executados": "Sem treinos realizados!", "text_nao_realizou_treino_periodo": "Não realizou nenhum treino neste período", "encerrar_treino?": "Encerrar treino?", "text_certeza_encerrar_treino_parcialmente": "Tem certeza que deseja encerrar o treino parcialmente?", "as_com_espaco": " às ", "direita_abrev": "D", "esquerda_abrev": "E", "meus_pontos": "<PERSON><PERSON> pontos", "ponto": "ponto", "pontos": "pontos", "acumule_pontos_no_clube_de_vantagens": "Acumule pontos no Clube de Vantagens da academia para trocar por benefícios exclusivos. Continue se exercitando e aproveite ao máximo!", "horariosem_estatistcas_de_inicio": "<PERSON><PERSON><PERSON><PERSON> iní<PERSON>", "sem_avaliacao_fisica!": "Sem avaliação física!", "vc_nao_possui_aval_fis_registradas": "Você não possui avaliações físicas registradas", "portugues_pt": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)", "portugues_br": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "Em Aberto": "Pendente", "Pago": "Pago", "Vencida": "V<PERSON>cid<PERSON>", "data_consulta_dias": "*A data da consulta é a partir dos dias {}", "inf_importantes_prof_planejamento": "Estas informações são importantes para o seu professor definir o melhor planeamento fitness para si.", "ative_not_treino_pronto": "Ative as notificações para ser avisado quando o seu treino estiver pronto.", "seu_treino_quase_pronto": "O seu treino está quase pronto!", "seu_plano_disponivel_comecar": "O seu plano de treino foi criado e já está disponível para começar.", "seu_treino_esta_pronto": "O seu treino está pronto!", "muscles_used": "<PERSON><PERSON><PERSON><PERSON>", "supper": "<PERSON><PERSON>", "holandes": "<PERSON><PERSON><PERSON><PERSON>", "personal": "<PERSON><PERSON><PERSON><PERSON>", "records": "Recordes", "titulo_sem_not": "Sem notificações", "comece_sua_nova_jornada": "Inicia a tua nova jornada", "novo_contrato_disponivel_para_voce": "Novo contrato disponível para ti!", "o_seu_contrato_cod": "O teu contrato código {} está disponível e precisa da tua assinatura.", "siga_seguintes_passos": "Segue os seguintes passos:", "revise_dados_contrato": "Revê os dados do contrato.", "cadastre_foto_perfil": "Regista a tua foto de perfil", "sair_aplicativo": "<PERSON><PERSON> <PERSON> aplicação", "ao_clicar_sair_dados_serao_perdidos": "Ao clicar em sair, não ficarás mais logado na aplicação e os dados não guardados serão perdidos.", "atualizar_dados": "<PERSON><PERSON><PERSON><PERSON> dad<PERSON>", "pendente": "Pendente", "contrato": "Contrato", "confira_termos_contrato_assine_acesso_recursos": "Confere os termos do contrato e assina para ter acesso a todas as funcionalidades da aplicação", "termos_contrato": "Termos do contrato", "assinatura_contrato": "Assinatura do contrato", "opcional": "Opcional", "atualize_foto_perfil": "Atualiza a tua foto de perfil", "atualizar_foto": "Atualizar foto", "acesso_unidade": "Acesso à unidade", "titulo_personal_records": "<PERSON><PERSON>", "o_que_vc_esta_procurando": "O que estás a procurar?", "frances": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "concluir_todos": "<PERSON><PERSON><PERSON><PERSON> todos", "nao_gostei_programa_treino_ia": "Não gostei do meu programa", "gerar_novo_programa_ia": "Gerar um novo programa de treino por IA", "renovar_treino_ia": "<PERSON>var treino por IA", "refazer_amamnese_treino": "Re<PERSON>zer anamnese para gerar treino", "voce_possui_problemas_listados_abaixo": "Tem algum dos problemas listados abaixo?", "problemas_cardiacos": "Problemas <PERSON>", "problemas_nas_costas_coluna": "Problemas nas costas ou coluna", "problemas_joelhos": "Problemas nos joelhos", "lesoes_fisicas": "Lesões físicas", "restricao_medica_ativ_fis": "Restrição médica para prática de atividade física", "outras_restricoes_limitacoes_ativ_fis": "Outras restrições ou limitações para a prática de atividades físicas", "sim_possu_restricoes_listadas_acima": "<PERSON><PERSON>, possuo alguma das restrições listadas acima.", "nao_possuo_restricoes": "<PERSON>ão, não possuo restrições.", "atestado_aptidao_fisica": "Atestado de aptidão física", "voce_atesta_nao_possuir_restricoes_fis": "Você atesta não possuir quaisquer tipos de restrições físicas para a realização de atividades físicas?", "revisar": "<PERSON>er", "selecionado": "Selecionado", "ainda_nao_treino": "Ainda não treino", "nao_tenho_coord_suf_exe_aju_correcoes": "Não tenho coordenação suficiente para fazer exercícios e preciso de ajuda ou correções", "nao_consigo_sentir_musculo_traba_durante_exerc": "Não consigo sentir o músculo alvo trabalhando durante o exercício", "consigo_exec_satisf_exer": "Consigo executar de forma satisfatória todos os exercícios", "consigo_sentir_musculos_corret": "Consigo sentir os músculos trabalhando corretamente", "possuo_forca_signif": "Possuo força significativa", "executo_perf_todos_exerc_consigo_musc_trabalhando": "Executo perfeitamente todos os exercícios e consigo sentir os músculos alvo trabalhando", "compreendo_exe_exerc_alinhar_corpo_musculos": "Compreendo a execução dos exercícios e como alinhar o corpo para atingir partes específicas dos músculos", "ja_exper_tec_intensidade": "Já tenho experiência com várias técnicas de alta intensidade", "como_voce_classifica_exp_treino": "Como classifica a sua experiência de treino?", "Nunca treinou": "Nunca treinou", "Estou voltando a treinar agora": "Estou a voltar a treinar agora", "Treino a menos de 6 meses": "Treino há menos de 6 meses", "Já treinei antes estou voltando": "<PERSON>á treinei antes e estou a voltar", "Já treino entre 6 meses e 1 ano": "<PERSON>á treino entre 6 meses e 1 ano", "Já treino entre 1 ano e 2 anos": "Já treino entre 1 ano e 2 anos", "Já treino a mais de 2 anos": "<PERSON><PERSON> treino há mais de 2 anos", "Sou bodybuilder ou atleta profissional ": "Sou bodybuilder ou atleta profissional", "alemao": "Alemão", "Parcela 1": "Parcela 1", "Parcela 2": "Parcela 2", "Parcela 3": "Parcela 3", "Parcela 4": "Parcela 4", "Parcela 5": "Parcela 5", "Parcela 6": "Parcela 6", "Parcela 7": "Parcela 7", "Parcela 8": "Parcela 8", "Parcela 9": "Parcela 9", "Parcela 10": "Parcela 10", "Parcela 11": "Parcela 11", "Parcela 12": "Parcela 12", "sem_dados": "Sem dados", "1 série": "1 série", "2 séries": "2 séries", "3 séries": "3 séries", "4 séries": "4 séries", "5 séries": "5 séries", "6 séries": "6 séries", "7 séries": "7 séries", "8 séries": "8 séries", "9 séries": "9 séries", "10 séries": "10 séries", "voce_possui_reposicoes_disponiveis": "Tem reposições disponíveis", "ate_o_final_do_seu_plano": "até o final do seu plano", "infelizmente_gerar_treino": "Infelizmente, não podemos \n gerar o seu treino", "entre_contato_prof_info_restricoes_treino_personalizado": "Entre em contato com o seu professor, informe as suas restrições médicas e solicite um treino personalizado.", "desculpe_ocorreu_incosc_login": "<PERSON><PERSON><PERSON><PERSON>, ocorreu uma inconsistência no login. Por favor, tente novamente.", "revisar_treino_ia": "<PERSON>er <PERSON>.A.", "aprovar": "<PERSON><PERSON><PERSON>", "aprovar_treino": "<PERSON><PERSON><PERSON> t<PERSON>", "deseja_aprovar_treino_sem_revisar": "Deseja aprovar o treino sem o rever?", "todos_treinos_foram_revisados": "Todos os treinos foram revistos!", "no_momento_nao_treino_revisao": "<PERSON>este momento, não há nenhum treino à espera de revisão.", "expira_em": "Expira em:", "deseja_fechar_app": "Deseja fechar a aplicação?", "ao_clicar_fechar_app_dados_perdidos": "Ao clicar em fechar a aplicação, os dados não guardados serão perdidos.", "fechar_app": "<PERSON><PERSON><PERSON>", "nao_foi_possivel_excluir_treino": "Não foi possível excluir o treino, tente novamente mais tarde.", "para_adicionar_video_add_url": "Para adicionar o vídeo, é necessário inserir um URL válido.", "falha_salvar": "<PERSON>alha ao guardar", "insira_url_valida": "Insira um URL válido", "voce_nao_possui_aut_acessar_treinos_entre_contato_adm": "Não tem autorização para aceder aos Treinos em casa. Entre em contato com a administração para resolver.", "revistar_programa": "Rever Programa", "programas_gerados": "Programas gerados", "inicio": "Início", "termino": "<PERSON><PERSON><PERSON><PERSON>", "//////////////\\\\\\\\\\\\\\\\\\\\ ↓↓↓ ATENÇÃO ↓↓↓": "↓↓↓ ATENÇÃO ↓↓↓", "/////\\\\ INFO=>": "Tradução atualizada!!!. Se estiver adicionando novas traduções, inclua-as abaixo. Ao realizar modificações ou adicionar traduções, certifique-se de replicar essas alterações para os demais idiomas.", "//////////////\\\\\\\\\\\\\\\\\\\\ ↑↑↑ ATENÇÃO ↑↑↑": "↓↓↓ ATENÇÃO ↓↓↓", "procure_por_professor": "Procure por professor", "nao_foi_possivel_cadastrar_tente_novamente": "Não foi possível cadastrar. Por favor, tente novamente.", "excluir_programa_aluno": "O seu utilizador não tem permissão para excluir um programa de treino.", "editar_programa_aluno": "O seu utilizador não tem permissão para editar um programa de treino.", "incluir_programa_aluno": "O seu utilizador não tem permissão para incluir um programa de treino.", "renovar_programa_aluno": "O seu utilizador não tem permissão para renovar um programa de treino.", "consultar_programa_aluno": "O seu usuário não tem permissão para consultar um programa de treino.", "renovacaoAutomatica": "O seu plano já é configurado para renovação automática, impossibilitando a renovação manual.", "visualizacao_das_aulas": "Visualização das aulas", "agrupar_por": "Agrupar por", "mostar_aulas_cheias": "Mostrar aulas completas", "horario_das_aulas": "<PERSON><PERSON><PERSON><PERSON>", "de_ativas": "{} de {} ativas", "conversorDePeso": {"Conversor_de_peso": "Conversor de peso", "Conversor_Kg_lb": "Conversor Lb / Kg", "clique_nos_cantos_ou_desliza": "Clique nos cantos ou deslize para calcular", "converter": "Converter"}, "aulas_favoritas": "<PERSON><PERSON> favoritas", "e_preciso_conceder_permissao_para_acessar_o_armazenamento": "É necessário conceder per<PERSON><PERSON><PERSON> para aceder ao armazenamento interno", "acesse_as_configuracoes_para_conceder_permissoes": "Aceda às configurações para conceder as permissões necessárias", "ja_concedi": "<PERSON><PERSON> con<PERSON>i", "aulas_agendadas_hoje": "<PERSON><PERSON> para hoje", "cref_vencido": "O seu CREF está vencido.", "cref_vencido_texto": "Regularize a sua situação para ter acesso. Caso esteja regularizada procure a administração.", "cadastro_inativo": "Esse usuário está inativado.", "cadastro_inativo_texto": "Seu usuário foi inativado pela academia. Se precisar de assistência ou tiver dúvidas sobre o processo, entre em contato com a administração da unidade que você está vinculado.", "ha": "há", "sem_registro_acesso": "Sem registros de entrada recente", "todas_as_carteiras": "<PERSON><PERSON> as carteiras", "sua_carteira": "<PERSON><PERSON> carteira", "filtro": "Filtro", "ultimos_acessos": "Últimos ace<PERSON>s", "nivel_atual": "<PERSON><PERSON><PERSON> atual:", "alunos_ja_acompanhados": "Alguns alunos já estão a ser acompanhados. Gostaria de substituir esses alunos por novos?", "subistituir_alunos": "Substituir os al<PERSON>s", "manter_alunos": "<PERSON><PERSON> os al<PERSON>", "primeiro": "<PERSON><PERSON>", "segundo": "<PERSON><PERSON><PERSON>", "terceiro": "<PERSON><PERSON><PERSON>", "treinar": "T<PERSON>inar", "ficha_sem_execucao": "Ficha sem execução", "avaliar": "Avaliar", "avaliar_wod": "<PERSON><PERSON> o WOD", "avaliar_geral_wod": "Avaliação geral do WOD", "percepcao_esforco": "Qual a sua percepção de esforço?", "menos_esforco": "Menos esforço", "mais_esforco": "<PERSON><PERSON>", "muito_leve": "<PERSON><PERSON> leve", "pouco_leve": "Um pouco leve", "leve": "<PERSON><PERSON>", "leve_moderado": "De leve a moderado", "moderado": "Moderado", "moderado_intenso": "De moderado a intenso", "pouco_intenso": "Um pouco intenso", "intenso": "Intenso", "muito_intenso": "<PERSON><PERSON> intenso", "extremo": "Extremo", "ops_parece_que_esta_faltando_algo": "Ops, parece que algo deu errado!", "tamanho_resolucao_imagem": "Tamanho e resolução da imagem", "text_capa_personalizada": "A imagem será usada como pré-visualização do treino. Recomendamos que a capa personalizada tenha", "text_resolucao_min": "Resolução de 1280x720 (com uma largura mínima de 640 píxeis)", "text_formato": "Formato em JPG ou PNG", "text_resolucao_max": "Resolução máxima de 2 MB", "text_proporcao": "Proporção de 16:9", "ok_entendi": "Ok, entendido!", "capa_teino": "Capa do treino", "adicionar_outro_video": "Adicionar outro vídeo", "url_video_youtube": "URL do vídeo do YouTube", "url_live_youtube": "URL Live do YouTube", "nome_treino": "Nome do treino", "criar_treino_ao_vivo": "Criar treino ao vivo", "criar_treino_online": "Criar treino online", "cadastrar_treino": "Registar treino", "treino_ao_vivo": "Treino ao vivo", "validar_checkin": "Validar Check-in", "validar_checkin_totalPass": "Validar Check-in TotalPass", "falha_validar_totalPass": "Falha na validação TotalPass", "nao_existem_dados_validos_termos": "Não existem dados válidos para aceitar os termos e condições. Por favor, dirija-se à recepção para atualizar seus dados ou documentos.\n\nPara ter acesso ao aplicativo, é necessário aceitar os termos e políticas.", "titulo_alerta_marcacao": "Tem certeza que deseja marcar presença para todos os alunos?", "subtitulo_alerta_marcacao": "<PERSON>o prosseguir, você está ciente que todos os alunos da lista ficarão com presença confirmada na aula.", "marca_presenca_todos": "Marcar presen<PERSON> para todos", "nenhuma_aula_encontrada_empty_state": "<PERSON>enhuma aula encontrada", "nao_foram_encontradas_aulas_no_periodo": "Não foram encontradas aulas registradas no período selecionado.", "em_aberto": "<PERSON>", "respondido": "Respondido", "botao_responder_parq": "Responder Par-Q", "botao_ver_meu_historico": "Ver meu histórico", "texto_formulario_historico_parq": "Hist<PERSON><PERSON><PERSON>", "parcelas_selecionadas": "Parcelas selecionadas", "tipo_agendamento": "Tipo de agendamento", "veja_suas_aulas_realizadas": "Veja suas aulas realizadas", "nivel_de_dificuldade": "Nível de di<PERSON>", "anilhas": "Discos de peso", "halters": "<PERSON><PERSON><PERSON>", "fita_supensa": "Fita suspensa", "barra_fixa": "Barra fixa", "corda": "<PERSON><PERSON>", "superband": "Banda elástica", "miniband": "Mini banda elástica", "colchonete": "Tapete de exercício", "tornozeleira": "Peso para tornozelo", "bola_suica": "Bola de ginástica", "roda": "Roda", "criar_treino": "<PERSON><PERSON><PERSON> tre<PERSON>", "usar_imagem_recente": "Usar imagem recente", "realizou": "realizou", "detalhes_do_treino": "Detalhes do treino", "quem_realizou_o_treino": "Quem realizou o treino", "excluir_treino": "Excluir treino", "deseja_realmente_excluir_este_treino?": "Deseja mesmo excluir este treino?", "cancelar": "<PERSON><PERSON><PERSON>", "execucoes_realizadas": "Execuções realizadas", "manter_peso": "Manter peso", "grupos_musculares": "Grupos musculares", "descricao": "Descrição", "editar_treino": "<PERSON><PERSON> t<PERSON>", "assistir_treino": "Ver treino", "data_do_treino_ao_vivo": "Data do treino ao vivo", "confirmar_data": "Confirmar data", "hora_inicio_treino_ao_vivo": "Hora de início do treino ao vivo", "hora_inicio_treino": "Hora de início do treino", "confirmar_horario": "Confirmar <PERSON><PERSON>", "carregando": "A carregar", "encerrado": "Terminado", "programado": "Agendado", "Ganhar massa": "Gan<PERSON> massa", "Emagrecer": "Perder peso", "Manter o peso": "Manter o peso", "Iniciante": "Iniciante", "Intermediario": "Intermédio", "abdomen": "Abdómen", "Abdomen": "Abdómen", "Braços": "Braços", "Costas": "<PERSON><PERSON>", "Sem equipamentos": "Sem equipamentos", "Anilhas": "Discos de peso", "Halters": "<PERSON><PERSON><PERSON>", "Fita suspensa": "Fita suspensa", "Barra fixa": "Barra fixa", "Corda": "<PERSON><PERSON>", "Superband": "Banda elástica", "Miniband": "Mini banda elástica", "Colchonete": "Tapete de exercício", "Tornozeleira": "Peso para tornozelo", "Bola suíça": "Bola de ginástica", "Roda": "Roda", "url_ja_adicionada": "URL já adicionada", "texto_validar_codigo_email": "Um código de verificação será enviado para o seu e-mail cadastrado. Utilize esse código para redefinir a sua senha e recuperar o acesso ao aplicativo. Certifique-se de verificar a sua caixa de entrada e a pasta de spam.", "remover_fila": "Remover da fila", "deseja_remover_fila": "Deseja realmente remover o aluno da fila?", "posicao": "Posição", "deseja_sair_fila": "Deseja realmente sair da fila de espera?", "falha_remover_fila": "Falha ao remover al<PERSON> da fila de espera. Tente novamente mais tarde.", "Sair_da_fila": "<PERSON><PERSON> da <PERSON>la", "acesso_camera_microfone": "É necessário permitir o acesso à câmara e ao microfone", "acesso_camera_microfone_subtitulo": "Aceda às definições do seu dispositivo para conceder as permissões necessárias e tente novamente", "abrir_ajustes": "A<PERSON>r definiç<PERSON>es", "dependente": "Dependente", "recente": "<PERSON><PERSON>", "falha_reservar_equipamento": "Falha ao reservar equipamento", "editar_reserva": "Editar reserva", "selecionar_equipamento": "Selecione um equipamento", "reserva_aviso": "A disposição dos equipamentos é ilustrativa e pode ser alterada no sem aviso prévio.", "reservador": "Reservado", "equipamento_selecionado": "Equipamento selecionado", "cancelar_reserva": "Cancelar reserva", "salvar_edicao": "<PERSON>var edi<PERSON>", "confirmar_reserva": "Confirmar reserva", "reserva_text": "Selecione um equipamento para reservar.", "reserva_de_equipamentos": "Reserva de equipamentos", "equipamento_reservado": "Equipamento reservado", "equipamento_livre": "Equipamento livre", "erro_ao_carregar_observacoes": "Erro ao consultar observações tente novamente mais tarde", "alterar_nivel": "<PERSON><PERSON><PERSON> ní<PERSON>", "Avançado 1": "Avançado 1", "Avançado 2": "Avançado 2", "Iniciante 1": "Iniciante 1", "Iniciante 2": "Iniciante 2", "Intermediário 1": "Intermediário 1", "Intermediário 2": "Intermediário 2", "meus_treinos": "Os meus treinos", "fez_um_treino_extra": "Fez um treino extra?", "ultimos_treinos_realizados": "Últimos treinos realizados", "histroico_treinos": "Histórico de treinos", "registrar_exercicio_extra": "Registar exercício extra", "data_do_treino": "Data do treino", "calorias_gastas_kcal": "Calorias gastas (kcal)", "duracao_treino_min": "Duração do treino (min)", "distancia_mt": "Distância (m)", "voltas": "Voltas", "escolha_um_exercicio": "Escolha um exercício", "tempo_calorias_distância": "Tempo, calorias e distância", "tempo_calorias": "Tempo e calorias", "tempo_calorias_voltas": "Tempo, calorias e voltas", "selecione_ficha_programa_atual": "Selecione a ficha do programa atual", "alongamento": "Alongamentos", "arremesso_de_disco": "Lançamento do disco", "artes_marciais": "Artes marciais", "basquete": "Basquetebol", "caminhada_ao_ar_livre": "Caminhada ao ar livre", "caminhada_com_carrinho_de_bebe": "Caminhada com carrinho de be<PERSON>", "caminhada_com_peso": "Caminhada com peso", "caminhada_em_esteira": "Caminhada em passadeira", "caminhada_em_montanhas": "Caminhad<PERSON> em montanhas", "ciclismo_ao_ar_livre": "Ciclismo ao ar livre", "ciclismo_indoor": "Ciclismo indoor", "corrida_ao_ar_livre": "Corrida ao ar livre", "corrida_na_esteira": "Corrida na passadeira", "danca": "Dança", "eliptico": "Elíptica", "escalada": "Escalada", "escalador": "<PERSON><PERSON><PERSON><PERSON>", "esportes_de_raquete": "Desportos de raquete", "futebol": "Futebol", "golfe": "Golfe", "hiit": "HIIT", "hoquei_no_gelo": "Hóquei no gelo", "ioga": "<PERSON><PERSON>", "meditacao": "Meditação", "mergulho": "Mergul<PERSON>", "natacao_em_aguas_abertas": "Natação em águas abertas", "natacao_em_piscina": "Natação em piscina", "patinacao": "Patinagem", "pilates": "Pilates", "pular_corda": "Saltar à corda", "remo_indoor": "Remo indoor", "treinamento_funcional": "Treino funcional", "treinamento_de_forca": "Treino de força", "volei": "Voleibol", "outros_personalizado": "<PERSON><PERSON> (personalizado)", "informe_cartao_completo": "* Para editar as informações, é necessário preencher os dados do cartão. Isso garante a segurança e a validação das alterações. Insira os dados corretamente para continuar.", "login_rapido_titulo": "Aguarde um momento enquanto alteramos o utilizador ligado", "login_rapido_subtitulo": "Estamos a preparar a app para si. Alguns recursos podem não estar disponíveis para todos os utilizadores.", "login_rapido_info_sem_usuario": "Após iniciar sessão com outro utilizador ou aceder ao perfil de um dependente, eles serão exibidos aqui para facilitar o acesso rápido entre os perfis.", "midias_enviadas_e_recebidas": "Mídias enviadas e recebidas", "midias_enviadas": "Mídias enviadas", "midias_enviadas_e_recebidas_vazio": "Nenhuma mídia foi partilhada nesta conversa. Quando enviar ou receber ficheiros, fotos ou vídeos, eles serão exibidos aqui.", "copiar": "Copiar", "resumo_de_aulas": "Resu<PERSON>", "aulas_realizadas": "<PERSON><PERSON> \nrealiza<PERSON>", "aulas_do_mes": "Aulas do \nmês", "semanas_consecutivas_com_presenca": "Semanas consecutivas com presença", "texto_semanas_consecutivas_com_presenca": "Não foi possível carregar os dados de aulas realizadas. Por favor, atualize a página.", "recarregar": "<PERSON><PERSON><PERSON><PERSON>", "clube_de_vantagens": "Clube de vantagens", "ver_brindes_disponiveis": "Ver brindes disponíveis", "brindes_disponiveis_para_resgate": "<PERSON><PERSON><PERSON> disponíveis para resgate", "nao_encontramos_nenhum_brinde_disponivel": "Não encontrámos nenhum brinde disponível para resgate no momento.", "text_subtitulo_brindes": "Resgate o seu brinde na receção da unidade! Informe qual brinde deseja e apresente os seus pontos acumulados.", "lista_de_presenca": "Lista de presença", "confirmar_presenca_do_aluno_selecionado": "Confirmar presença do aluno", "confirmar_presenca_dos_alunos_selecionados": "Confirmar presen<PERSON> {} <PERSON><PERSON><PERSON>", "confirmado": "<PERSON><PERSON><PERSON><PERSON>", "aguardando_confirmacao": "Aguardando <PERSON>", "refazer": "<PERSON><PERSON><PERSON>", "concluida": "conclu<PERSON><PERSON>", "concluidas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "meus_creditos_descricao": "Provenientes de remarcações e créditos", "aulas_coletivas": "<PERSON><PERSON>", "utilizados": "Utilizados", "expirados": "Expirados", "turmas": "<PERSON><PERSON><PERSON>", "tenis": "<PERSON><PERSON><PERSON>", "tenis_de_mesa": "Ténis de mesa", "beach_tennis": "Beach Tennis", "padel": "Pa<PERSON>", "badminton": "Bad<PERSON>ton", "squash": "Squash", "frescobol": "Frescobol", "selecione_uma_ficha": "Selecione uma ficha", "titulo_avaliacao_professores": "Avaliação do professor", "info_avaliacao_professores": "<PERSON><PERSON> pode avaliar os professores associados ao seu perfil uma vez a cada 30 dias.", "avaliacao_pendente": "Avaliação pendente", "avaliacao": "Avaliação", "data_avaliacao": "Data da avaliação", "comentario_opcional": "Comentário (opcional)", "hint_comentario_opcional": "Escreva aqui o seu comentário. Comente sobre a sua experiência com o professor. Máximo de 500 caracteres.", "salvar_avaliacao": "Guardar avaliação", "por_favor_avalie_professor": "Por favor, avalie o professor", "nao_existem_niveis_cadastrados_ou_ativos_no_momento": "Não existem níveis cadastrados ou ativos no momento.", "chat_maisc": "Cha<PERSON>", "msn_chat": "Ol<PERSON>! Parece que nenhum chat foi iniciado. Se precisar de ajuda ou tiver alguma dúvida, por favor, inicie uma nova conversa. Estamos aqui para ajudar!", "iniciar_chat": "Iniciar chat", "erro_ao_carregar_chats": "<PERSON>rro ao carregar chats.", "nenhum_resultado_encontrado": "Nenhum resultado encontrado", "text_pesquisa_chat": "Não encontrámos nenhum chat com o termo pesquisado. Por favor, tente novamente.", "sem_alunos_disponiveis": "Sem alunos disponíveis", "titulo_adicionar_aluno": "Adicionar al<PERSON> aula", "confirma_adicionar_aluno": "Tem a certeza de que deseja adicionar o aluno {aluno} à aula {aula}?", "recarregar_consulta": "<PERSON><PERSON><PERSON><PERSON>", "titulo_boleto_indisponivel": "<PERSON><PERSON><PERSON> boleto disponível de momento", "mensagem_boleto_indisponivel": "Para efetuar o pagamento por boleto, entre em contacto com a unidade para solicitá-lo ou escolha outro método de pagamento disponível.", "codigo_copiado_sucesso": "Código copiado com sucesso!", "renovar_criar_meu_programa_de_treino": "<PERSON><PERSON>/criar meu programa de treino", "nao_gostei_do_meu_programa": "Não gostei do meu programa", "atalho": "<PERSON><PERSON><PERSON>", "saude_e_bem_estar": "Saúde e bem-estar", "dietas_e_alimentacoes": "Dietas e alimentações", "motivacoes_e_inspiracoes": "Motivações e inspirações", "imagens_e_referencias": "Imagens e referências", "sites_e_blogs": "Sites e blogues", "meu_link_titulo": "O meu link", "botao_salvar": "Guardar", "botao_excluir": "Eliminar", "mensagem_link_salvo": "Link guardado com sucesso", "mensagem_link_removido": "Link removido com sucesso", "descricao_link_privado": "O seu link é privado e visível apenas por si. Use esta funcionalidade para aceder rapidamente a links personalizados como avaliações físicas, planos alimentares, vídeos de exercícios e muito mais.", "label_nome_link": "Nome do link*", "hint_nome_link": "O meu plano alimentar", "label_url": "URL*", "hint_url": "https://www.exemplo.pt", "botao_colar": "Colar", "label_icone_atalho": "Ícone do atalho", "titulo_icone_atalho": "Ícones do atalho", "excluir_link": "Excluir link", "excluir_link_mensagem": "Deseja realmente excluir o link?", "voce_ainda_nao_realizou_nenhum_treino": "Você ainda não realizou nenhum treino", "assim_que_voce_completar_seu_primeiro_treino_ele_aparecera_aqui": "Assim que completar o seu primeiro treino, ele aparecerá aqui.", "checkin_validado_sucesso": "Check-in validado com sucesso!", "avaliacao_professor_sucesso": "Avaliação do professor realizada com sucesso!", "pr_lancado_sucesso": "PR lançado com sucesso!", "senha_recuperada": "Senha recuperada com sucesso!", "treinoOnline_excluido_sucesso": "Treino online excluído com sucesso!", "plano_inativo_titulo": "Plano inativo", "plano_inativo_mensagem": "Este plano está inativo no momento e não pode ser renovado. Por favor, entre em contacto com a receção para mais informações.", "hipertrofia": "Hipertrofia", "forca": "Força", "nunca_treinei_ou_ja_treinei_e_estou_voltando_agora": "Nunca treinei ou já treinei e estou a voltar agora", "ja_treino_entre_6_meses_e_1_ano": "<PERSON>á treino entre 6 meses e 1 ano", "ja_treino_ha_mais_de_1_ano": "<PERSON><PERSON> treino há mais de 1 ano", "professores": "Professores", "outras_opcoes": "Outras opções", "aditivo": "Aditivo", "aditivos": "Aditivos", "confira_os_termos_do_aditivo_e_assine_para_ter_acesso_a_todos_os_recursos_do_app": "Confira os termos do aditivo e assine para ter acesso a todos os recursos do app", "assinatura_aditivo": "Assinatura do aditivo", "erro_ao_processar": "Erro ao processar", "ocorreu_um_erro_ao_processar_a_assinatura_tente_novamente": "Ocorreu um erro ao processar a assinatura. Tente novamente.", "assinatura_pendente": "Assinatura pendente", "nao_foi_possivel_enviar_a_assinatura_verifique_sua_conexao_e_tente_novamente": "Não foi possível enviar a assinatura. Verifique sua conexão e tente novamente.", "assine_o_contrato_para_continuar": "Assine o contrato para continuar", "ver_mais_aditivos": "Ver mais aditivos", "contrato_assinado_com_sucesso": "Contrato assinado com sucesso!", "aditivo_assinado_com_sucesso": "Aditivo assinado com sucesso!", "contrato_precisa_assinatura_responsavel": "És menor de idade e, por isso, não podes assinar o contrato sozinho(a). Por favor, dirige-te à receção do ginásio acompanhado(a) pelo teu responsável para realizar a assinatura e ativar o uso da aplicação.", "novo_aditivo_disponivel_para_voce": "Novo aditivo disponível para você!", "o_seu_aditivo_cod": "O seu aditivo código {} está disponível e precisa da sua assinatura. Assine agora mesmo e tenha acesso a todos os recursos do app", "assinar_aditivo": "Assinar aditivo", "tempo_de_aula": "Tempo de aula", "menos_detalhes": "<PERSON><PERSON>", "mais_detalhes": "<PERSON><PERSON>", "parq_positivo": "Aluno é ParQ positivo.", "selfie_validada_com_sucesso!": "Selfie validada com sucesso!", "envie_uma_selfie_com_um_documento_oficial": "Envie uma selfie com um documento de identificação oficial.", "escolha_uma_forma_de_autenticacao_de_dois_fatores": "Escolha uma forma de autenticação de dois fatores.", "assine_aditivo": "Assine o aditivo", "assine_contrato": "Assine o contrato", "adicione_uma_foto_de_perfil_opcional": "Adicione uma foto de perfil. (Opcional)", "selfie_com_documento": "Selfie com documento", "autenticar": "Autenticar", "autenticacacao_de_dois_fatores": "Autenticação de dois fatores", "autenticar_assinatura": "Autenticar assinatura", "text_esolha_autenticacao": "Escolha uma forma de receber o código de autenticação para validar a sua assinatura.", "sms": "SMS", "email": "E-mail", "nova_foto": "Nova foto", "verificando": "A verificar...", "verificando_documento_e_rosto": "A verificar documento e rosto...", "analisando_a_imagem": "A analisar a imagem...", "procurando_por_rosto": "A procurar por rosto...", "text_falhou_imagem_rosto": "Não detetámos nenhum rosto na imagem. Por favor, certifique-se de que o seu rosto está visível.", "text_iluminacao_imagem": "A imagem pode estar com pouca iluminação ou não conseguimos ver o seu rosto ou o documento com clareza. Tente novamente num local bem iluminado e certifique-se de que ambos estão visíveis.", "procurando_por_texto_no_documento": "A procurar por texto no documento...", "text_falhou_imagem_doc": "Não conseguimos identificar texto de documento na imagem. Certifique-se de que o seu documento está visível.", "verificacao_concluida_com_sucesso": "Verificação concluída com sucesso!", "texto_instrucoes": "Para garantirmos a sua segurança, pedimos que tire uma foto com o seu rosto ao lado do seu documento de identificação. Certifique-se de que ambos estão bem iluminados e visíveis na imagem.", "acessar_galeria": "Aceder à galeria", "verefique_seu_documento_rosto": "Verifique se o documento e o seu rosto estão visíveis:", "falha_na_verificacao": "Falha na verificação", "confirmar_foto": "Confirmar foto", "iniciando_camera": "A iniciar câmara...", "camera_nao_disponivel": "Câmara não disponível", "erro_ao_iniciar_camera": "Erro ao iniciar a câmara. Por favor, verifique as permissões e tente novamente.", "text_alerta_instrucoes": "Por favor, tente novamente seguindo as instruções.", "erro_ao_acessar_a_galeria": "Erro ao aceder à galeria. Por favor, verifique as permissões e tente novamente.", "text_erro_ao_acessar_a_galeria": "Não foi possível aceder às suas fotos. Verifique as permissões da aplicação e tente novamente.", "text_erro_sms": "Não foi possível enviar o código de verificação. Por favor, verifique se o número está correto junto ao balcão de atendimento do seu ginásio.", "erro_ao_enviar_sms": "Erro ao enviar SMS", "ocorreu_um_erro_ao_processar_sua_solicitacao": "Ocorreu um erro ao processar o seu pedido. Por favor, tente novamente mais tarde.", "erro": "Erro", "enviando_codigo_por_email": "A enviar código por e-mail...", "erro_ao_enviar_codigo": "Erro ao enviar código", "tente_novamente": "Tente novamente.", "enviando_codigo_sms": "A enviar código SMS...", "sem_dados_de_email": "Sem dados de e-mail", "sem_dados_de_telefone": "Sem dados de telefone", "text_forma_receber_cod": "Escolha uma forma de receber o código de autenticação para validar a sua assinatura.", "enviaremos_um_codigo_numero": "Enviaremos um código para o número {} para que possa autenticar a sua assinatura de contrato.", "enviaremos_um_codigo_email": "Enviaremos um código para o e-mail {} para que possa autenticar a sua assinatura de contrato.", "codigo_reenviado_email": "<PERSON><PERSON><PERSON> reenviado para o e-mail!", "codigo_reenviado_sms": "Código reenviado por SMS!", "erro_reenviar_sms": "Erro ao reenviar SMS", "dados_autenticao_incompletos": "Dados de autenticação incompletos. Tente novamente.", "codigo_invalido_verifique": "Código inválido. Verifique e tente novamente.", "codigo_invalido": "<PERSON><PERSON><PERSON>", "cod_inf_nao_e_valido": "O código informado não é válido. Por favor, verifique e tente novamente.", "autenticacao": "Autenticação", "autenticar_com_sms": "Autenticar com SMS", "autenticar_com_email": "Autenticar com e-mail", "solicitacao_de_autenticacao_enviada": "Pedido de autenticação enviado!", "inf_cod_seis_digitos": "Indique o código de 6 dígitos enviado para o {} {}", "verificar_cod": "Verificar código", "reenviar_cod": "Reenviar código", "aguarde_segundos": "Aguarde {} segundos", "aditivo_do_contrato": "Aditivo do contrato {}", "validando_cod": "A validar código...", "cod_invalido_verifique_tente_novamente": "Código inválido. Verifique e tente novamente.", "erro_ao_verificar_codigo": "Erro ao verificar código", "processando_assinatura": "A processar assinatura {} de {}", "processando_assinaturas": "A processar assinaturas", "contrato_args": "Contrato {}", "erro_no_processamento": "Erro no processamento", "ocorreram_erro_assinaturas": "Ocorreram erros ao processar algumas assinaturas", "veja_seus_recordes_pesos": "Veja seus recordes de pesos", "ficha_salva_sucesso": "Ficha guardada com sucesso!", "creditos_de_treino": "Créditos de treino", "transferir_creditos_de_treino": "Transferir créditos de treino", "transferencia": "Transferência", "informe_o_aluno_e_quantos_creditos_deseja_tranferir": "Informe o aluno e quantos créditos deseja transferir", "aluno_destinatario": "<PERSON><PERSON>", "quantidade_de_creditos": "Quantidade de créditos", "voce_possui_de_credito_de_treino": "Voc<PERSON> possui {} de crédito de treino", "transferindo": "A transferir", "para:": "Para:", "cpf": "NIF", "data_da_transferencia": "Data da transferência", "transferir": "Transferir", "creditos_de_treino_transferido_com_sucesso!": "Créditos de treino transferidos com sucesso!", "cpf_ou_email": "NIF ou E-mail", "aluno_nao_encontrado": "<PERSON><PERSON> não encontrado", "text_aluno_nao_encontrado": "Não localizamos o aluno. Verifique os dados inseridos e tente novamente. O e-mail ou NIF pode estar incorreto.", "seu_perfil_sem_permissao": "O seu perfil não permite aceder a esta funcionalidade.", "aguarde_alguns_instantes": "Aguarde alguns instantes e tente novamente", "seu_treino_esta_em_revisao": "Seu treino está em revisão", "carrossel_treinos_aprovacao_erro_carregar": "Erro ao carregar treinos", "carrossel_treinos_aprovacao_tentar_novamente": "Tentar novamente", "carrossel_treinos_aprovacao_todos_revisados": "Todos os treinos foram revistos", "carrossel_treinos_aprovacao_aguardando_revisao": "Aguard<PERSON><PERSON> revisão", "carrossel_treinos_aprovacao_ver_todos": "Ver todos", "carrossel_treinos_aprovacao_ops": "Ops", "carrossel_treinos_aprovacao_expira_em": "Expira em {}", "carrossel_treinos_aprovacao_atualizando": "A actualizar...", "carrossel_treinos_aprovacao_auto_30s": "Auto 30s", "carrossel_treinos_aprovacao_auto_countdown": "Auto {}s", "cancelar_contrato": {"erro_titulo": "Erro ao cancelar", "botao_ok": "Ok", "erro_mesmo_dia": "Não é possível cancelar um contrato no mesmo dia de início", "confirmacao_titulo": "Confirmação", "confirmacao_headline": "Confirme o cancelamento do contrato", "confirmacao_body": "O cancelamento do contrato é definitivo e não pode ser desfeito. A data de cancelamento será registrada como hoje {}.", "botao_voltar": "Voltar", "botao_confirmar": "Confirmar cancel<PERSON>o", "botao_avancar": "<PERSON><PERSON><PERSON><PERSON>", "titulo": "Cancelamento", "atencao_titulo": "Atenção", "atencao_body": "Você está prestes a cancelar seu contrato. Essa ação não pode ser desfeita e a data de cancelamento não pode ser alterada, sendo válida a partir de hoje.", "atencao_justificativa": "Para continuar, selecione uma justificativa para o cancelamento.", "data_cancelamento": "Data de cancelamento", "comentario_label": "Coment<PERSON>rio*", "comentario_hint": "Conte mais sobre o que levou você a cancelar.", "sucesso_envio": "Solicitação enviada com sucesso", "aluno_receptivo": "<PERSON>uno Receptivo", "cliente_receptivo_nao_pode_ser_marcado_presente": "Cliente Receptivo, não pode ser marcado como presente no aplicativo."}, "seu_treino_estara_disponivel_quando_aprovado": "O seu treino estará disponível quando for aprovado pelo professor"}