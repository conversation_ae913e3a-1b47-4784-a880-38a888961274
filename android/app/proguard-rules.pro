# Proguard optimization file
-optimizationpasses 5
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# Keep Application classes
-keep public class * extends android.app.Application

# Keep Activity classes
-keep public class * extends android.app.Activity

-keep class com.lib.flutter_blue_plus.* { *; }

# Keep Fragment classes
-keep public class * extends android.app.Fragment
-keep public class * extends androidx.fragment.app.Fragment

# Keep classes with custom views
-keep public class * extends android.view.View
-keepclassmembers public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Keep custom Parcelable objects
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep custom serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object readResolve();
    java.lang.Object writeReplace();
}

# Keep data classes and JSON serialization libraries (Gson, Moshi, etc.)
-keep class com.your.package.name.** { *; }
-keepattributes Signature

# Retain annotations
-keepattributes *Annotation*

# Enable library optimization
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Flutter-specific optimizations
# Retain Flutter engine components
-keep class io.flutter.** { *; }
-keep class io.flutter.embedding.engine.** { *; }
-keep class io.flutter.plugin.** { *; }

# Keep JSON converter for serialization (if applicable)
-keepclassmembers class **.R$* {
    public static <fields>;
}

# Google Play Core - Ignore missing classes during R8 minification
-dontwarn com.google.android.play.core.**
-dontwarn com.google.android.play.core.splitcompat.**
-dontwarn com.google.android.play.core.splitinstall.**
-dontwarn com.google.android.play.core.tasks.**

# Firebase and Google Services
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Gson serialization
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# ESSENCIAL: Evita crash do TypeToken em notificações
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class com.dexterous.flutterlocalnotifications.** { *; }

# OkHttp and Retrofit
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Health Connect and Health devices
-keep class androidx.health.** { *; }
-dontwarn androidx.health.**

# Camera
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# WebView
-keep class android.webkit.** { *; }
-dontwarn android.webkit.**

# Mixpanel
-dontwarn com.mixpanel.**
-keep class com.mixpanel.** { *; }

# PDF Viewer
-keep class com.github.barteksc.** { *; }
-dontwarn com.github.barteksc.**

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom exceptions
-keep public class * extends java.lang.Exception

# Keep enums
-keep class * extends java.lang.Enum { *; }

# Specific rules for Flutter plugins used in the project
# Flutter Blue Plus
-keep class com.lib.flutter_blue_plus.** { *; }
-dontwarn com.lib.flutter_blue_plus.**

# Health devices and Health Connect
-keep class dev.steenbakker.mobile_scanner.** { *; }
-dontwarn dev.steenbakker.mobile_scanner.**

# Image picker and camera
-keep class io.flutter.plugins.imagepicker.** { *; }
-keep class io.flutter.plugins.camera.** { *; }
-dontwarn io.flutter.plugins.imagepicker.**
-dontwarn io.flutter.plugins.camera.**

# URL launcher
-keep class io.flutter.plugins.urllauncher.** { *; }
-dontwarn io.flutter.plugins.urllauncher.**

# Shared preferences
-keep class io.flutter.plugins.sharedpreferences.** { *; }
-dontwarn io.flutter.plugins.sharedpreferences.**

# Path provider
-keep class io.flutter.plugins.pathprovider.** { *; }
-dontwarn io.flutter.plugins.pathprovider.**

# Permission handler
-keep class com.baseflow.permissionhandler.** { *; }
-dontwarn com.baseflow.permissionhandler.**

# Device info
-keep class io.flutter.plugins.deviceinfo.** { *; }
-dontwarn io.flutter.plugins.deviceinfo.**

# Package info
-keep class io.flutter.plugins.packageinfo.** { *; }
-dontwarn io.flutter.plugins.packageinfo.**

# Connectivity
-keep class io.flutter.plugins.connectivity.** { *; }
-dontwarn io.flutter.plugins.connectivity.**

# Local auth
-keep class io.flutter.plugins.localauth.** { *; }
-dontwarn io.flutter.plugins.localauth.**

# Video player
-keep class io.flutter.plugins.videoplayer.** { *; }
-dontwarn io.flutter.plugins.videoplayer.**

# WebView
-keep class io.flutter.plugins.webviewflutter.** { *; }
-dontwarn io.flutter.plugins.webviewflutter.**

# Keep all model classes that might be used for JSON serialization
-keep class com.pacto.** { *; }
-keep class com.example.treino_droid_upgrade.** { *; }

# Keep all classes with @JsonSerializable annotation
-keep @com.google.gson.annotations.SerializedName class * { *; }

# Additional safety rules for R8
-keepattributes InnerClasses
-keepattributes EnclosingMethod
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes RuntimeInvisibleParameterAnnotations
